package router

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gopkg.in/go-playground/validator.v9"

	"git.wndv.co/go/logx/v2"
	srvgin "git.wndv.co/go/srv/gin"
	"git.wndv.co/go/srv/provider"
	"git.wndv.co/lineman/fleet-distribution/cmd/api/docs"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/assignmentbenchmarks"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/dbconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/dedicated_zone"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/deliveryfee"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverinsurance"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/event"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/featureflagconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/fraud"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/health"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentivesource"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/internalapi"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/orderassigner"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/otp"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/phones"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/product"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/province"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rain_situation"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rating"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/region"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/reward"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/shift"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/summaryofchange"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttled_order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttleddispatchdetail"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/trip"
	api_validator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/withholdingtaxcertificate"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/zone"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/xgo/errors/mwhttp"
)

var _ provider.RouterCustomizer = &routerGinCustomizer{}

type routerGinCustomizer struct {
	apis         *APISet
	builder      *middlewares.Builder
	apiSpecConf  config.APISpecConfig
	routerConfig config.RouterConfig
	featureFlag  featureflag.Service
}

func (g *routerGinCustomizer) AddCustomAccessLog(builder srvgin.Builder) {
	logDecor := srvgin.DefaultAccessLogDecorator()

	logutil.MaskLineAccessToken(logDecor)

	logx.Info().Msgf("InboundHTTPLogMaskedBodyRegexes: %+v", g.routerConfig.InboundHTTPLogMaskedBodyRegexes)

	for _, raw := range g.routerConfig.InboundHTTPLogMaskedBodyRegexes {
		path := strings.TrimSpace(raw)
		if path == "" {
			continue
		}

		logDecor.AddProcessorByPath(srvgin.ProcessorByPath{
			Path:           path,
			IsRegularExpr:  true,
			IsWriteLog:     true,
			IsWriteReqBody: false,
			IsWriteResBody: false,
		})
	}

	builder.WithCustomAccessLog(logDecor)
}

func (g *routerGinCustomizer) Configure(builder srvgin.Builder) error {
	builder.
		WithRecoveryFunc(mwhttp.RecoveryFn).
		WithTracer(g.routerConfig.IsWithTracerEnabled)
	g.AddCustomAccessLog(builder)
	return nil
}

func (g *routerGinCustomizer) Register(svrGin *srvgin.Gin) error {
	Register(svrGin.Engine, g.apis, g.builder, g.apiSpecConf, g.routerConfig, g.featureFlag)
	return nil
}

// @@no-locator-generation@@
func ProvideRouterCustomizer(apis *APISet, builder *middlewares.Builder, apiSpecConf config.APISpecConfig, routerConfig config.RouterConfig, featureFlag featureflag.Service) provider.RouterCustomizer {
	return &routerGinCustomizer{apis: apis, builder: builder, apiSpecConf: apiSpecConf, routerConfig: routerConfig, featureFlag: featureFlag}
}

// PreRegister apis into router.
// Remove metric, tracer and health when we switch to use pillar internal server 100% instead
func Register(router *gin.Engine, apis *APISet, builder *middlewares.Builder, apiSpecConf config.APISpecConfig, routerConfig config.RouterConfig, featureFlag featureflag.Service) {
	router.Use(builder.WithScope())
	router.Use(middlewares.ErrorMiddleware())

	registerSwagger(router, apiSpecConf)
	registerDelivery(apis, router, builder)
	registerOrder(apis, builder, router, routerConfig)
	registerAssignment(apis, builder, router, routerConfig)
	registerTrip(apis, builder, router)
	registerDriver(apis, router, builder)
	registerPhones(apis, router)
	registerProvinces(apis, router)
	registerRegion(apis, router)
	registerRegistration(apis, router)
	registerOTP(apis, router)
	registerAdmin(apis, router, builder, featureFlag)
	registerInternal(apis, router, featureFlag)
	registerPartner(apis, router)
	registerRating(apis, router)
	registerHealthAndMetric(router)
	registerQuestion(apis, router)
	registerEvent(apis, router)
	registerSummaryOfChangeAPI(apis, router, builder)
	registerThrottledOrder(apis, router)
}

func registerSwagger(router *gin.Engine, config config.APISpecConfig) {
	if !config.IsEnabled {
		return
	}
	docs.SwaggerInfo.BasePath = config.BasePath
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}

func registerDelivery(apis *APISet, router *gin.Engine, builder *middlewares.Builder) {
	lockOrder := builder.Locker("orderID",
		middlewares.WithPrefix("lock_order"),
		middlewares.WithRetry(2),
	)

	deliveryV1 := router.Group("/v1/delivery")
	{
		dpapi := apis.DeliveryPortalAPI
		foodV1 := deliveryV1.Group("/food")
		{
			foodV1.POST("/order/can-defer", dpapi.CanDefer)
			foodV1.POST("/order/quote", dpapi.QuoteOrder)
			foodV1.POST("/order/create", dpapi.CreateOrder)
			foodV1.POST("/order/edit", lockOrder, dpapi.EditOrder)
			foodV1.POST("/order/update-delivery-location", dpapi.UpdateDeliveryLocation)
			foodV1.PUT("/order/:orderID/confirm-price", dpapi.ConfirmPrice)
			foodV1.PUT("/order/:orderID/cancel", lockOrder, dpapi.CancelOrder)
			foodV1.PUT("/order/:orderID/system-cancel", lockOrder, dpapi.SystemCancelOrder)
			foodV1.PUT("/order/:orderID/restaurant-accepted", lockOrder, dpapi.RestaurantAccepted)
			foodV1.GET("/order/:orderID/status", dpapi.GetOrderStatus)
			foodV1.GET("/order/:orderID/detail", dpapi.GetOrderDetail)
			foodV1.POST("/driver/rating", dpapi.RatingDriver)
			foodV1.POST("/driver/tip", dpapi.TipDriver)
			foodV1.PUT("/order/:orderID/unlock-mp", dpapi.UnlockMP)
			// Internal use only.
			foodV1.POST("/price", dpapi.CalculateDeliveryPrice)
			foodV1.PUT("/order/:orderID/payment-method", lockOrder, dpapi.ChangePaymentMethod)
		}

		deliveryV1.POST("/order/quote", dpapi.QuoteOrder)
		deliveryV1.POST("/order/create", dpapi.CreateOrder)
		deliveryV1.POST("/order/edit", dpapi.EditOrder)
		deliveryV1.POST("/order/update-delivery-location", dpapi.UpdateDeliveryLocation)
		deliveryV1.PUT("/order/:orderID/confirm-price", dpapi.ConfirmPrice)
		deliveryV1.PUT("/order/:orderID/cancel", lockOrder, dpapi.CancelOrder)
		deliveryV1.PUT("/order/:orderID/restaurant-accepted", dpapi.RestaurantAccepted)
		deliveryV1.PUT("/order/:orderID/refund", dpapi.RefundOrder)
		deliveryV1.POST("/order/:orderID/validate-refund", dpapi.ValidateRefundOrder)
		deliveryV1.GET("/order/:orderID/status", dpapi.GetOrderStatus)
		deliveryV1.GET("/order/:orderID/detail", dpapi.GetOrderDetail)
		deliveryV1.POST("/driver/rating", dpapi.RatingDriver)
		deliveryV1.POST("/driver/tip", dpapi.TipDriver)
		// Internal use only.
		deliveryV1.POST("/price", dpapi.CalculateDeliveryPrice)
		deliveryV1.PUT("/order/:orderID/payment-method", lockOrder, dpapi.ChangePaymentMethod)
		deliveryV1.POST("/fare-metadata", dpapi.GetFareMetadata)

		messengerV1 := deliveryV1.Group("/messenger")
		{
			messengerV1.POST("/order/quote", dpapi.QuoteOrder)
			messengerV1.POST("/order/create", dpapi.CreateOrder)
			messengerV1.PUT("/order/:orderID/cancel", lockOrder, dpapi.CancelOrder)
			messengerV1.POST("/order/update-delivery-location", dpapi.UpdateDeliveryLocation)
			messengerV1.PUT("/order/:orderID/system-cancel", lockOrder, dpapi.SystemCancelOrder)
			messengerV1.GET("/order/:orderID/status", dpapi.GetOrderStatus)
			messengerV1.GET("/order/:orderID/detail", dpapi.GetOrderDetail)
			messengerV1.PUT("/order/:orderID/payment-method", lockOrder, dpapi.ChangePaymentMethod)
			messengerV1.POST("/price", dpapi.CalculateDeliveryPrice)
			messengerV1.POST("/driver/rating", dpapi.RatingDriver)
			messengerV1.POST("/driver/tip", dpapi.TipDriver)
		}
	}

	deliveryV2 := router.Group("/v2/delivery")
	{
		dpapi := apis.DeliveryPortalAPI
		deliveryV2.POST("/order/create", dpapi.CreateOrder2)
		deliveryV2.POST("/orders/:orderID/routes/:pos/continue", dpapi.Continue)
	}
}

func registerOrder(apis *APISet, builder *middlewares.Builder, router *gin.Engine, routerConfig config.RouterConfig) {
	router.POST("/v1/orders", builder.Auth(), apis.OrderAPI.GetOrders)
	router.GET("/v1/orders/cancel-reasons/:serviceType", builder.Auth(), apis.OrderAPI.ListCancelReasons)
	router.POST("/v1/orders/next-status", builder.Auth(), apis.OrderPortalAPI.UpdateMultipleOrderStatus)

	orderV1 := router.Group("/v1/order")
	{
		lockAcceptOrder := builder.Locker("orderID",
			middlewares.WithPrefix("lock_order"),
			middlewares.WithAcquireLogError(apiutil.NewFromString("ORDER_ALREADY_TAKEN", "order already taken by another driver")),
		)

		lockNextStatus := builder.Locker("orderID",
			middlewares.WithPrefix("lock_order"),
			middlewares.WithAcquireLogError(apiutil.NewFromString("IN_PROCESS_ORDER", "order is in update")),
		)

		lockCancelOrder := builder.Locker("orderID",
			middlewares.WithPrefix("lock_order"),
			middlewares.WithAcquireLogError(apiutil.NewFromString("ORDER_CANCELED", "order got canceled")),
		)

		api := apis.OrderAPI
		opapi := apis.OrderPortalAPI
		orderV1.Use(builder.Auth())
		orderV1.GET("/:orderID", api.GetOrderDetail)
		orderV1.PUT("/:orderID/update-price", opapi.UpdatePrice)
		orderV1.POST("/:orderID/reject", lockAcceptOrder, api.RejectOrderHandler)
		orderV1.POST("/:orderID/accept", lockAcceptOrder, builder.MinVersion(1, routerConfig.AcceptOrderAPIMinPatchVersion, 0), opapi.AcceptOrderHandler)
		orderV1.PUT("/:orderID/next-status", lockNextStatus, opapi.UpdateOrderStatus)
		orderV1.POST("/:orderID/rating", api.CreateRestaurantRating)
		orderV1.PUT("/:orderID/cancel", lockCancelOrder, api.CancelOrder)
		orderV1.POST("/:orderID/upload-photo", api.UploadPhoto)
		orderV1.POST("/:orderID/upload-delivering-photo", api.UploadDeliveringPhoto)
		orderV1.GET("/:orderID/qr-payment-detail", api.GetQRPaymentDetail)
		orderV1.GET("/:orderID/validate-convert-qr-to-cash", api.ValidateConvertQRPaymentToCash)
		orderV1.POST("/:orderID/convert-qr-to-cash", api.ConvertQRPaymentToCash)
		orderV1.POST("/:orderID/cell-phone-contact", api.SetCellPhoneContact)
	}
}

func registerAssignment(apis *APISet, builder *middlewares.Builder, router *gin.Engine, routerConfig config.RouterConfig) {
	assignmentV1 := router.Group("/v1/assignment")
	opapi := apis.OrderPortalAPI
	api := apis.OrderAPI
	tripapi := apis.TripAPI
	assignmentV1.Use(builder.Auth())
	assignmentV1.POST("/:assignmentID/accept", builder.MinVersion(1, routerConfig.AcceptOrderAPIMinPatchVersion, 0), opapi.AcceptAssignmentHandler)
	assignmentV1.POST("/:assignmentID/reject", api.RejectAssignmentHandler)
	assignmentV1.POST("/:assignmentID/ack", tripapi.AckAssignmentHandler)
	assignmentV1.GET("/:assignmentID", tripapi.GetAssignmentDetail)
}

func registerTrip(apis *APISet, builder *middlewares.Builder, router *gin.Engine) {
	tripV1 := router.Group("/v1/trip")
	{
		api := apis.TripAPI
		tripV1.Use(builder.Auth())
		tripV1.GET("/:tripID", api.GetTripDetail)
		tripV1.POST("/:tripID/rating", api.RateRestaurant)
	}
}

func registerDriver(apis *APISet, router *gin.Engine, builder *middlewares.Builder) {
	driverV1 := router.Group("/v1/driver")
	{
		api := apis.DriverRegisAPI
		driverV1.POST("/register", api.PreRegister)

		heatmapApi := apis.HeatMapAPI
		driverV1.GET("/heatmap", builder.Auth(), heatmapApi.Get)

		pdpaApi := apis.PdpaApi
		driverV1.POST("/pdpa", builder.Auth(), pdpaApi.Create)

		shiftAPI := apis.ShiftAPI
		driverV1.POST("/shifts", builder.Auth(), shiftAPI.BookShift)
		driverV1.GET("/shifts/active-coordinates", builder.Auth(), shiftAPI.GetActiveCoordinates)
		driverV1.GET("/shift/:shiftId", builder.Auth(), shiftAPI.GetShift)
		driverV1.DELETE("/shifts/:shiftId", builder.Auth(), shiftAPI.UnBookShift)
		driverV1.GET("/shifts", builder.Auth(), shiftAPI.ListShifts)
		driverV1.GET("/shifts/validate-cancellation", builder.Auth(), shiftAPI.ValidateCancellation)
		driverV1.GET("/shifts/incoming", builder.Auth(), shiftAPI.ListIncomingShifts)
		driverV1.GET("/shifts/cancel", builder.Auth(), shiftAPI.ListCancelReasons)
		driverV1.POST("/shifts/cancel", builder.Auth(), shiftAPI.CancelShift)

		withholdingTaxCertificateApi := apis.WithholdingTaxCertificateApi
		driverDocsAPI := apis.DriverDocsAPI
		driverV1.GET("/documents/withholding-tax-certificates", builder.Auth(), withholdingTaxCertificateApi.Get)
		driverV1.GET("/documents", builder.Auth(), driverDocsAPI.Get)

		rainSituationAPI := apis.RainSituationAPI
		driverV1.GET("/rain-situation-map", builder.Auth(), rainSituationAPI.GetRainSituationMap)
	}
}

func registerPhones(apis *APISet, router *gin.Engine) {
	phonesV1 := router.Group("/v1/phones")
	{
		api := apis.PhonesAPI
		phonesV1.POST("/whitelist", api.IsRequireOTP)
	}
}

func registerProvinces(apis *APISet, router *gin.Engine) {
	provincesV1 := router.Group("/v1/provinces")
	{
		api := apis.ProvinceAPI
		provincesV1.POST("/search", api.GetFlow)
	}
}

func registerRegion(apis *APISet, router *gin.Engine) {
	regionV1 := router.Group("/v1/regions")
	{
		api := apis.RegionAPI
		regionV1.GET("/registration-provinces", api.ListRegistrationProvince)
	}
}

func registerRegistration(apis *APISet, router *gin.Engine) {
	regisV1 := router.Group("/v1/registrations")
	{
		api := apis.DriverRegisAPI
		regisV1.POST("", api.Register)
		regisV1.GET("", api.GetRegistration)
		regisV1.PUT("", api.UpdateRegistration)
		regisV1.GET(fmt.Sprintf("/:%s", driver.KeyLineUid), api.GetRegistrationByLineUid)
		regisV1.PUT(fmt.Sprintf("/:%s", driver.KeyLineUid), api.UpdateRegistrationByLineUid)
	}
}

func registerQuestion(apis *APISet, router *gin.Engine) {
	regisV1 := router.Group("/v1/questions")
	{
		api := apis.DriverRegisAPI
		regisV1.GET("", api.GetQuestions)
		regisV1.GET(fmt.Sprintf("/:%s", driver.KeyLineUid), api.GetQuestionsByLineUid)
		// /v1/questions/validation
		regisV1.POST("/:path1", api.ValidateQuestions)
		// /v1/questions/:path1/:path2 - deprecated
		regisV1.POST("/:path1/:path2", api.ValidateQuestions)
	}
}

func registerOTP(apis *APISet, router *gin.Engine) {
	otpV1 := router.Group("/v1/otp")
	{
		otpapi := apis.OTPAPI
		otpV1.POST("", otpapi.Generate)
		otpV1.POST("/validate", otpapi.Validate)
	}
}

func registerEvent(apis *APISet, router *gin.Engine) {
	eventsV1 := router.Group("/v1/events")
	{
		eventsapi := apis.EventAPI
		eventsV1.POST("", eventsapi.Save)
	}
}

func registerAdmin(apis *APISet, router *gin.Engine, builder *middlewares.Builder, flag featureflag.Service) {
	adminV1 := router.Group("/v1/admin")
	{
		opapi := apis.OrderPortalAPI
		ordadmapi := apis.OrderAdminAPI
		driveradminapi := apis.DriverAdminAPI
		registrationadminapi := apis.RegistrationAdminAPI

		// Deprecated: /order/:orderID/cancel will be remove after change
		// to rms cancel order api in lm-delivery.
		adminV1.PUT("/order/:orderID/cancel", ordadmapi.RMSCancelOrder)
		// for force-completing an order from the delivery service.
		adminV1.POST("/order/:orderID/system-complete", opapi.SystemCompleteOrder)

		// for force-completing an order from admin tools server.
		adminV1.POST("/order/:orderID/complete", builder.AdminAuth(), opapi.AdminCompleteOrder)
		adminV1.POST("/bulk/orders/complete", builder.AdminAuth(), opapi.AdminBulkCompleteOrder)
		adminV1.POST("/order/:orderID/cs-cancel", ordadmapi.CSCancelOrder)
		adminV1.POST("/bulk/orders/cs-cancel", builder.AdminAuth(), ordadmapi.CSBulkCancelOrder)
		adminV1.POST("/order/:orderID/force-reassign", builder.AdminAuth(), ordadmapi.ForceReassign)
		adminV1.GET("/order/:orderID/can-reassign", builder.AdminAuth(), ordadmapi.CanReassign)
		adminV1.GET("/order/:orderID/requestor", ordadmapi.GetOrderRequestor)
		adminV1.GET("/orders/:orderID", ordadmapi.GetOrderDetail)
		adminV1.PUT("/orders/:orderID/delivering-photo-status/pass", ordadmapi.DeliveringPhotoStatusPass)
		adminV1.PUT("/orders/:orderID/delivering-photo-status/not_pass", ordadmapi.DeliveringPhotoStatusNotPass)
		adminV1.GET("/orders", ordadmapi.RecentOrders)
		adminV1.POST("/orders", ordadmapi.ListOrders)
		adminV1.PUT("/orders/:orderID/update-cancel-reason", ordadmapi.UpdateOrderCancelReason)

		adminV1.PUT("/bulk/orders/fraud_status", ordadmapi.BulkOrderFraudStatus)
		adminV1.POST("/compensate/order-distance", ordadmapi.CompensateOrderDistance)
		adminV1.POST("/compensate/trip-distance", ordadmapi.CompensateTripDistance)
		adminV1.GET("/review-order-photo", ordadmapi.ReviewOrderPhotoList)
		adminV1.GET("/review-order-photo/:orderID", ordadmapi.ReviewOrderPhotoDetail)

		adminV1.GET("/internal-cancel-reasons", ordadmapi.GetInternalCancelReasons)
		adminV1.GET("/internal-cancel-reasons/:internalCancelReasonId", ordadmapi.GetInternalCancelReason)
		adminV1.POST("/internal-cancel-reasons", builder.AdminAuth(), ordadmapi.CreateInternalCancelReason)
		adminV1.PUT("/internal-cancel-reasons/:internalCancelReasonId", builder.AdminAuth(), ordadmapi.UpdateInternalCancelReason)
		adminV1.DELETE("/internal-cancel-reasons/:internalCancelReasonId", builder.AdminAuth(), ordadmapi.DeleteInternalCancelReason)

		adminV1.GET("/driver-registration", registrationadminapi.ListRegistration)
		adminV1.GET("/driver-registration/:id", registrationadminapi.GetRegistration)
		adminV1.PUT("/driver-registration/:id", builder.AdminAuth(), registrationadminapi.UpdateRegistration)
		adminV1.POST("/driver-registration/:id/approve", builder.AdminAuth(), registrationadminapi.ApproveRegistration)
		adminV1.POST("/bulk/driver-registration/approve", builder.AdminAuth(), registrationadminapi.BulkApproveRegistrations)
		adminV1.POST("/driver-registration/:id/request-update", registrationadminapi.RequestUpdateRegistration)
		adminV1.POST("/driver-registration/:id/reject", registrationadminapi.RejectRegistration)
		adminV1.POST("/driver-registration/:id/archive", registrationadminapi.ArchiveRegistration)
		adminV1.PUT("/bulk/drivers-registration/archive", registrationadminapi.SetMultipleArchive)
		adminV1.POST("/bulk/driver-registration/assign-reviewer", registrationadminapi.BulkAssignReviewerRegistrations)
		adminV1.PUT("/driver-registration/:id/approve-transfer", registrationadminapi.ApproveTransfer)
		adminV1.GET("/driver-registration/:id/audit-log-history", registrationadminapi.GetRegistrationAuditLog)

		// Criminal Check Status
		adminV1.PUT("/driver-registration/:id/criminal-check-status/pass", builder.AdminAuth(), registrationadminapi.CriminalCheckStatusPass)
		adminV1.PUT("/driver-registration/:id/criminal-check-status/not_pass", builder.AdminAuth(), registrationadminapi.CriminalCheckStatusNotPass)
		adminV1.PUT("/driver-registration/:id/criminal-check-status/whitelist", builder.AdminAuth(), registrationadminapi.CriminalCheckStatusWhiteList)
		adminV1.PUT("/bulk/driver-registration/criminal-check-status", builder.AdminAuth(), registrationadminapi.BulkUpdateCriminalCheckStatusRegis)

		regionapi := apis.RegionAdminAPI
		adminV1.GET("/regions", regionapi.AvailableRegions)
		adminV1.GET("/provinces", regionapi.ListProvinces)

		// TODO: move to DriverAdminAPI.
		adminV1.GET("/drivers/:driver_id", driveradminapi.GetDriver)
		adminV1.GET("/drivers/:driver_id/audit-log-history", builder.AdminAuth(), driveradminapi.GetDriverAuditLog)

		adminV1.PUT("/drivers/:driver_id", builder.AdminAuth(), driveradminapi.UpdateDriver)
		adminV1.POST("/drivers/:driver_id/remark", driveradminapi.UpdateDriverRemark)
		adminV1.POST("/drivers/:driver_id/profile/completed", driveradminapi.CompleteProfileStatus)
		adminV1.POST("/drivers/:driver_id/profile/request_update", driveradminapi.RequestUpdateProfileStatus)
		adminV1.PUT("/drivers/:driver_id/status/:driver_status", builder.AdminAuth(), driveradminapi.UpdateDriverStatus)
		adminV1.PUT("/bulk/deactivate-drivers", builder.AdminAuth(), driveradminapi.BulkDeactivateDriver)
		adminV1.PUT("/bulk/region-drivers", builder.AdminAuth(), driveradminapi.BulkUpdateDriverRegion)
		adminV1.PUT("/bulk/have-jacket", builder.AdminAuth(), driveradminapi.BulkUpdateHaveJacketAndBox)
		adminV1.PUT("/drivers/:driver_id/criminal_check_status/pass", builder.AdminAuth(), driveradminapi.CriminalCheckStatusPass)
		adminV1.PUT("/drivers/:driver_id/criminal_check_status/not_pass", builder.AdminAuth(), driveradminapi.CriminalCheckStatusNotPass)
		adminV1.PUT("/drivers/:driver_id/criminal_check_status/whitelist", builder.AdminAuth(), driveradminapi.CriminalCheckStatusWhiteList)
		adminV1.PUT("/bulk/drivers/criminal_check_status", driveradminapi.BulkUpdateDriverCriminalStatus)
		adminV1.PUT("/bulk/drivers/financial-risk-control", builder.AdminAuth(), driveradminapi.BulkUpdateDriverFinancialRiskControl)

		fraudapiV1 := apis.FraudAPI
		adminV1.POST("/fraud/transaction-scores", fraudapiV1.ListTransactionFraudScore)
		adminV1.GET("/fraud/transaction-scores/:transaction_id", fraudapiV1.GetTransactionFraudScore)

		approvalapi := apis.PaymentAPI.ApprovalAPI

		adminV1.GET("/approvals", approvalapi.ListApproval)
		adminV1.POST("/approvals", approvalapi.CreateApproval)
		adminV1.GET("/approvals/:approval_id", approvalapi.GetApproval)
		adminV1.PUT("/approvals/:approval_id/approve", approvalapi.ApproveApproval)
		adminV1.PUT("/approvals/:approval_id/reject", approvalapi.RejectApproval)
		adminV1.POST("/approvals/:approval_id/remarks", approvalapi.AddRemark)
		adminV1.POST("/bulk/approvals/free_credit", approvalapi.BulkFreeCredit)
		adminV1.PUT("/bulk/approvals/approve", approvalapi.ApproveMultipleApprovals)

		transapi := apis.PaymentAPI.TransactionAPI

		adminV1.GET("/transactions", transapi.ListTransaction)
		adminV1.GET("/transaction-ids", transapi.ListTransactionIDs)
		adminV1.GET("/transactions/:transaction_id", transapi.GetTransaction)
		adminV1.POST("/transactions/:transaction_id/remarks", transapi.AddRemark)
		adminV1.PUT("/transactions/:transaction_id/approve", transapi.ApprovePendingTransaction)
		adminV1.PUT("/transactions/:transaction_id/reject", transapi.RejectPendingTransaction)
		adminV1.PUT("/bulk/transactions/approve", transapi.ApproveMultiplePendingTransaction)
		adminV1.PUT("/bulk/transactions/reject", transapi.RejectMultiplePendingTransaction)
		adminV1.GET("/transaction-ref-ids/:transaction_ref_id", transapi.GetTransactionByRefID)

		adminV1.POST("/consolidation/withdraw", transapi.ConsolidationWithdraw)
		adminV1.PUT("/import/transactions-status", transapi.UpdateWithdrawTransactionStatus)
		adminV1.PUT("/import/transactions-wallet-topup", builder.AdminAuth(), transapi.ImportWalletTopUp)
		adminV1.PUT("/import/transactions-uob-topup", builder.AdminAuth(), transapi.ImportUOBTopUp)
		adminV1.PUT("/bulk/transactions/delete-withdraw-ref-id", transapi.BulkDeleteWithdrawRefId)

		drivTranAPI := apis.PaymentAPI.DriverTransactionAPI

		adminV1.GET("/driver-transactions/:driver_id", drivTranAPI.GetDriverTransaction)
		adminV1.PUT("/driver-transactions/:driver_id/ban", drivTranAPI.BanWithdraw)
		adminV1.PUT("/driver-transactions/:driver_id/unban", drivTranAPI.UnbanWithdraw)
		adminV1.POST("/driver-transactions/:driver_id/credit", drivTranAPI.ActionOnCredit)
		adminV1.PUT("/bulk/driver-transactions/credit-to-wallet", drivTranAPI.BulkTransferCreditToWallet)
		adminV1.PUT("/bulk/driver-transactions/unban", drivTranAPI.BulkUnbanWithdraw)

		incentiveapi := apis.SettingIncentiveAPI

		adminV1.POST("/incentive-settings", incentiveapi.Create)
		adminV1.GET("/incentive-settings", incentiveapi.List)
		adminV1.GET("/incentive-settings/:incentive_id", incentiveapi.Get)
		adminV1.PUT("/incentive-settings/:incentive_id", incentiveapi.Update)
		adminV1.DELETE("/incentive-settings/:incentive_id", incentiveapi.Delete)

		deliveryFee := apis.DeliveryFee

		adminV1.POST("/delivery-fee-settings", deliveryFee.Create)
		adminV1.PUT("/delivery-fee-settings/:id", deliveryFee.Update)
		adminV1.GET("/delivery-fee-settings", deliveryFee.List)
		adminV1.GET("/delivery-fee-settings/:id", deliveryFee.GetDetail)

		adminV1.GET("/delivery-fee-settings/:id/schemes", deliveryFee.ListScheme)
		adminV1.GET("/delivery-fee-settings/:id/schemes/:key", deliveryFee.GetScheme)

		adminV1.POST("/delivery-fee-settings-price-schemes", deliveryFee.CreatePriceScheme)
		adminV1.GET("/delivery-fee-settings-price-schemes", deliveryFee.GetPriceSchemes)
		adminV1.GET("/delivery-fee-settings-price-schemes/:setting_delivery_fee_price_schemes_id", deliveryFee.GetPriceScheme)
		adminV1.PUT("/delivery-fee-settings-price-schemes/:setting_delivery_fee_price_schemes_id", deliveryFee.UpdatePriceScheme)
		adminV1.DELETE("/delivery-fee-settings-price-schemes/:setting_delivery_fee_price_schemes_id", deliveryFee.DeletePriceScheme)

		adminV1.POST("/delivery-fee-settings/:id/price-polygon", deliveryFee.AddPricePolygon)
		adminV1.GET("/delivery-fee-settings/:id/price-polygon/:price_polygon_id", deliveryFee.GetPricePolygon)
		adminV1.PUT("/delivery-fee-settings/:id/price-polygon/:price_polygon_id", deliveryFee.UpdatePricePolygon)
		adminV1.DELETE("/delivery-fee-settings/:id/price-polygon/:price_polygon_id", deliveryFee.DeletePricePolygon)

		groupTxnAPI := apis.PaymentAPI.GroupTransactionAPI

		adminV1.POST("/group-transactions", groupTxnAPI.Create)
		adminV1.GET("/group-transactions", groupTxnAPI.List)
		adminV1.GET("/group-transactions/:id", groupTxnAPI.Get)
		adminV1.PUT("/group-transactions/:id/status/approve", groupTxnAPI.Approve)
		adminV1.PUT("/group-transactions/:id/status/reject", groupTxnAPI.Reject)

		txnSchemeAPI := apis.PaymentAPI.TransactionSchemeAPI
		adminV1.GET("/transaction-schemes", builder.AdminAuth(), txnSchemeAPI.List)
		adminV1.GET("/transaction-schemes/:id", builder.AdminAuth(), txnSchemeAPI.Get)
		adminV1.POST("/transaction-schemes", builder.AdminAuth(), txnSchemeAPI.Create)
		adminV1.PUT("/transaction-schemes/:id", builder.AdminAuth(), txnSchemeAPI.Update)
		adminV1.PUT("/transaction-schemes/:id/archived", builder.AdminAuth(), txnSchemeAPI.Archived)

		areaAPI := apis.SettingServiceAreaAPI
		reportAdminServiceAreasAPIUsed := middlewares.ReportDeprecatedUsed(func() bool {
			return flag.IsEnabledWithDefaultFalse(nil, featureflag.ReportAdminServiceAreaApiWasCalled.Name)
		})
		adminV1.POST("/service-areas", reportAdminServiceAreasAPIUsed, areaAPI.Create)
		adminV1.GET(fmt.Sprintf("/service-areas/:%s", srvarea.KeyServiceAreaSettingID), reportAdminServiceAreasAPIUsed, areaAPI.Get)
		adminV1.GET("/service-areas", reportAdminServiceAreasAPIUsed, areaAPI.List)
		adminV1.PUT(fmt.Sprintf("/service-areas/:%s", srvarea.KeyServiceAreaSettingID), reportAdminServiceAreasAPIUsed, areaAPI.Update)
		adminV1.PUT(fmt.Sprintf("/service-areas/:%s/tier-benefit", srvarea.KeyServiceAreaSettingID), reportAdminServiceAreasAPIUsed, areaAPI.UpdateTierBenefit)

		// temporary endpoint for 3rd calling during migration TODO cleanup when no more 3rd calling
		deprecatedAdminV1 := adminV1.Group("deprecated")
		deprecatedAdminV1.POST("/service-areas", areaAPI.Create)
		deprecatedAdminV1.GET(fmt.Sprintf("/service-areas/:%s", srvarea.KeyServiceAreaSettingID), areaAPI.Get)
		deprecatedAdminV1.GET("/service-areas", areaAPI.List)
		deprecatedAdminV1.PUT(fmt.Sprintf("/service-areas/:%s", srvarea.KeyServiceAreaSettingID), areaAPI.Update)
		deprecatedAdminV1.PUT(fmt.Sprintf("/service-areas/:%s/tier-benefit", srvarea.KeyServiceAreaSettingID), areaAPI.UpdateTierBenefit)

		careaAPI := apis.ClientAreaAPI
		adminV1.POST("/client-areas", careaAPI.Create)
		adminV1.PUT(fmt.Sprintf("/client-areas/:%s", srvarea.KeyClientAreaID), careaAPI.Update)
		adminV1.GET(fmt.Sprintf("/client-areas/:%s", srvarea.KeyClientAreaID), careaAPI.Get)
		adminV1.GET("/client-areas", careaAPI.List)

		provinceAdminAPI := apis.ProvinceAdminAPI
		adminV1.POST("/province", provinceAdminAPI.Create)
		adminV1.GET(fmt.Sprintf("/province/:%s", province.ProvinceKeyID), provinceAdminAPI.Get)
		adminV1.GET("/province", provinceAdminAPI.List)
		adminV1.PUT(fmt.Sprintf("/province/:%s", province.ProvinceKeyID), provinceAdminAPI.Update)
		adminV1.DELETE(fmt.Sprintf("/province/:%s", province.ProvinceKeyID), provinceAdminAPI.Delete)

		phoneAPI := apis.PhonesAPI
		adminV1.POST("/phones", phoneAPI.BulkAdd)
		adminV1.POST("/phones/search", phoneAPI.List)

		ratingAPI := apis.RatingAdminAPI
		adminV1.GET("/rating/:id", ratingAPI.Get)
		adminV1.GET("/rating", ratingAPI.GetAll)
		adminV1.POST("/rating", ratingAPI.Create)
		adminV1.PUT("/rating/:id", ratingAPI.Update)
		adminV1.DELETE("/rating/:id", ratingAPI.Delete)

		configAdminApi := apis.DbConfigAdminAPI
		adminV1.GET("/db-configs", configAdminApi.GetDbConfig)
		adminV1.PUT("/db-configs", builder.AdminAuth(), configAdminApi.UpdateDbConfig)
		adminV1.PATCH("/db-configs", builder.AdminAuth(), configAdminApi.UpdateSingleConfig)

		shiftAPI := apis.ShiftAPI
		adminV1.GET("/shifts", builder.AdminAuth(), shiftAPI.GetAll)
		adminV1.GET("/shifts/:shift_id", builder.AdminAuth(), shiftAPI.Get)
		adminV1.POST("/shifts", builder.AdminAuth(), shiftAPI.Create)
		adminV1.DELETE("/shifts/:id", builder.AdminAuth(), shiftAPI.Delete)
		adminV1.PUT("/shifts/:shift_id", builder.AdminAuth(), shiftAPI.Update)
		adminV1.PUT("/bulk/shifts/active", builder.AdminAuth(), shiftAPI.BulkActiveShifts)

		incentiveSourceAPI := apis.IncentiveSourceAPI
		adminV1.GET("/incentive-sources", incentiveSourceAPI.GetAll)

		productApi := apis.ProductApi
		adminV1.POST("/product-groups", builder.AdminAuth(), productApi.ListProductGroups)
		adminV1.POST("/upload-product-groups", builder.AdminAuth(), productApi.BulkUploadProductGroup)
		adminV1.GET("/product-groups/:product_group_id", builder.AdminAuth(), productApi.GetProductGroupById)
		adminV1.POST("/products", builder.AdminAuth(), productApi.ListProducts)
		adminV1.POST("/upload-products", builder.AdminAuth(), productApi.BulkUploadProduct)
		adminV1.PUT("/product-groups/:product_group_id", builder.AdminAuth(), productApi.UpdateProductGroupDetail)
		adminV1.GET("/products-exporter", builder.AdminAuth(), productApi.ExportCSVProducts)
		adminV1.PUT("/update-products", builder.AdminAuth(), productApi.BulkUpdate)
		adminV1.GET("/products/:product_id", builder.AdminAuth(), productApi.GetProductById)
		adminV1.PUT("/products/:product_id", builder.AdminAuth(), productApi.UpdateProductById)

		tripApi := apis.TripAPI
		adminV1.GET("/trip/:orderID", tripApi.GetTripDetailAdmin)
		adminV1.POST("/trip/:trip_id/sync-trip-state", tripApi.SyncTripState)
		adminV1.POST("/trip/:trip_id/complete", builder.AdminAuth(), tripApi.AdminCompleteTrip)

		adminV1.GET("/trips/:trip_id", tripApi.GetTripByIdAdmin)
		adminV1.GET("/trips", tripApi.List)

		insuranceApi := apis.DriverInsuranceApi
		adminV1.POST("/insurance/consolidate", builder.AdminAuth(), insuranceApi.ConsolidateInsurance)
		adminV1.PUT("/insurance/update-policy-number", insuranceApi.UpdateDriverInsurancesPolicyNumber)
		adminV1.POST("/insurance/approve", insuranceApi.ApproveInsurance)
		// re-upload is deprecated
		adminV1.PATCH("/insurance/reupload", insuranceApi.ReuploadFailedImage)
		adminV1.GET("/insurance/consolidated-list", builder.AdminAuth(), insuranceApi.GetConsolidatedInsuranceInformations)
		adminV1.POST("/insurance/upload-insurance-policies", insuranceApi.UploadInsurancePolicy)
		adminV1.GET("/insurance/insurance-policies", insuranceApi.GetInsurancePolicy)

		dedicatedZoneAPI := apis.DedicatedZoneAPI
		adminV1.GET("/dedicated-zones", builder.AdminAuth(), dedicatedZoneAPI.List)
		adminV1.POST("/dedicated-zones", builder.AdminAuth(), dedicatedZoneAPI.Create)
		adminV1.GET("/dedicated-zones/:zoneLabel", builder.AdminAuth(), dedicatedZoneAPI.Get)
		adminV1.PUT("/dedicated-zones/:zoneLabel", builder.AdminAuth(), dedicatedZoneAPI.Update)

		zoneAPI := apis.ZoneAdminAPI
		// admin
		adminV1.POST("/zones", builder.AdminAuth(), zoneAPI.Create)
		adminV1.GET("/zones", builder.AdminAuth(), zoneAPI.List)
		adminV1.GET("/zones/:id", builder.AdminAuth(), zoneAPI.Get)
		adminV1.PUT("/zones/:id", builder.AdminAuth(), zoneAPI.Update)
		// transpay
		adminV1.GET("/zone-codes", zoneAPI.GetZoneCodes)

		throttledDispatchDetailAPI := apis.ThrottledDispatchDetailAdminAPI
		adminV1.GET("/throttled-dispatch-details", builder.AdminAuth(), throttledDispatchDetailAPI.List)
		adminV1.POST("/throttled-dispatch-details", builder.AdminAuth(), throttledDispatchDetailAPI.Create)
		adminV1.GET("/throttled-dispatch-details/:zoneID", builder.AdminAuth(), throttledDispatchDetailAPI.Get)
		adminV1.PUT("/throttled-dispatch-details/:zoneID", builder.AdminAuth(), throttledDispatchDetailAPI.Update)

		rewardAdminAPI := apis.RewardAdminAPI
		adminV1.GET("/coin/:driver_id", builder.AdminAuth(), rewardAdminAPI.GetDriverCoinBalance)
		adminV1.POST("/coin/bulk/csv", builder.AdminAuth(), rewardAdminAPI.BulkUploadCoinTransaction)
		adminV1.GET("/coin/conversion-rate", builder.AdminAuth(), rewardAdminAPI.ListConversionRate)
		adminV1.GET("/coin/conversion-rate/:id", builder.AdminAuth(), rewardAdminAPI.GetCoinCashConversionRateByID)
		adminV1.POST("/coin/conversion-rate", builder.AdminAuth(), rewardAdminAPI.CreateCoinCashConversionRate)
		adminV1.PUT("/coin/conversion-rate/:id", builder.AdminAuth(), rewardAdminAPI.UpdateCoinCashConversionRate)

		rainSituationAPI := apis.RainSituationAdminAPI
		adminV1.POST("/rain-situations", builder.AdminAuth(), rainSituationAPI.CreateRainSituation)
		adminV1.GET("/rain-situations", builder.AdminAuth(), rainSituationAPI.ListRainSituations)
		adminV1.PUT("/rain-situations/:id", builder.AdminAuth(), rainSituationAPI.UpdateRainSituation)
		adminV1.GET("/rain-situations/:id", builder.AdminAuth(), rainSituationAPI.GetRainSituation)
		adminV1.DELETE("/rain-situations/:id", builder.AdminAuth(), rainSituationAPI.DeleteRainSituation)
		adminV1.PUT("/bulk/rain-situations", builder.AdminAuth(), rainSituationAPI.BulkUpdateRainSituation)

		summaryOfChangeAPI := apis.SummaryOfChangeAPI
		adminV1.POST("/summary-of-changes", builder.AdminAuth(), summaryOfChangeAPI.Create)
		adminV1.GET("/summary-of-changes/:id", builder.AdminAuth(), summaryOfChangeAPI.Get)
		adminV1.GET("/summary-of-changes", builder.AdminAuth(), summaryOfChangeAPI.GetAll)
		adminV1.PUT("/summary-of-changes/:id", builder.AdminAuth(), summaryOfChangeAPI.Update)
		adminV1.PUT("/summary-of-changes/:id/archived", builder.AdminAuth(), summaryOfChangeAPI.Archived)

		featureFlagConfigAPI := apis.FeatureFlagConfigAPI
		adminV1.GET("/feature-flag-configs", builder.AdminAuth(), featureFlagConfigAPI.GetFeatureFlagConfigs)
		adminV1.PUT("/feature-flag-configs/:feature_name/toggle", builder.AdminAuth(), featureFlagConfigAPI.UpdateFeatureFlagConfigToggle)
		adminV1.PUT("/feature-flag-configs/:feature_name/strategy/:id", builder.AdminAuth(), featureFlagConfigAPI.UpdateFeatureFlagConfigStrategy)
		adminV1.GET("/feature-flag-configs/audit-log-history", featureFlagConfigAPI.GetFeatureFlagConfigAuditLog)

		assignmentBenchmarksAPI := apis.AssignmentBenchmarkAPI
		adminV1.POST("/bulk/csv/benchmark-getting-order/upload", builder.AdminAuth(), assignmentBenchmarksAPI.BulkUploadCSV)
		adminV1.GET("/bulk/csv/benchmark-getting-order/latest-log", builder.AdminAuth(), assignmentBenchmarksAPI.GetLatestLog)

		salesforceAdminapi := apis.SalesforceAdminAPI
		adminV1Salesforce := adminV1.Group("/salesforce")
		{
			adminV1Salesforce.GET("/drivers/:driver_id", salesforceAdminapi.GetDriver)
		}
	}
}

func registerInternal(apis *APISet, router *gin.Engine, flag featureflag.Service) {
	internalV1 := router.Group("/v1/internal")
	{
		api := apis.InternalAPI
		rainSituationAPI := apis.RainSituationAPI
		// order internal api.
		internalV1.GET("/order/:orderID/driver-assignmented", api.GetDriverAssignmented)
		internalV1.GET("/order/:orderID/rating", api.GetDriverRating)
		internalV1.PUT("/order/:orderID/expire", api.SetExpireAt)
		internalV1.POST("/order/:orderID/upload-delivering-photo", api.UploadDeliveringPhoto)
		internalV1.POST("/order", api.MakeCompletedOrder)
		internalV1.POST("/incentive_order", api.MakeCompletedIncentiveOrder)
		internalV1.POST("/order/set-defer-distribution", api.SetDeferOrderDistributionTime)
		internalV1.GET("/order/:orderID/prechat", api.GetSalesForcePrechatInfos)

		// add transaction to driver
		internalV1.POST("/transaction", api.AddTransaction)
		internalV1.DELETE("/transaction/:transaction_id", api.DeleteTransaction)
		internalV1.GET("/transaction/export", api.GetExportDriverTransaction)

		internalV1.POST("/group-transactions", api.UpdateGroupTransactions)
		internalV1.DELETE("/group-transactions", api.DeleteGroupTransactions)

		// driver internal api.
		internalV1.GET("/driver-locations", api.GetDriversByLocation)
		internalV1.GET("/driver/:driver_id/location", api.GetDriverLocation)
		internalV1.POST("/driver/:driver_id/location", api.UpdateDriverLocation)
		internalV1.PUT("/driver/:driver_id/update-cache", api.UpdateDriverStatusCache)
		internalV1.POST("/driver/:driver_id/wallet", api.SetDriverWallet)
		internalV1.POST("/driver/:driver_id/credit", api.SetDriverFreeCredit)
		internalV1.POST("/driver/:driver_id/purchasecredit", api.SetDriverCredit)
		internalV1.POST("/driver/:driver_id/positivecredit", api.SetDriverPositiveCredit)
		internalV1.PUT("/driver/:driver_id/cash", api.SetCash)
		// intentionally, this api was created for performance testing tool.
		// perf test tools was using admin force online driver which cause a lot of db spike from insert audit log.
		// so, this api was inspired from admin force online driver but without wrote audit log step to reduce db spike on perf test.
		internalV1.PUT("/driver/:driver_id/status/ONLINE", api.SetDriverOnline)
		internalV1.PUT("/driver/:driver_id/status/OFFLINE", api.SetDriverOffline)
		internalV1.PUT("/driver/:driver_id/reset-latest-random-at", api.ResetLatestRandomAt)
		internalV1.DELETE("/driver/:driver_id/orders", api.RemoveCompletedOrders)
		internalV1.DELETE("/driver/:driver_id/sma-rating-score", api.ResetSMARatingScore)
		internalV1.DELETE("/driver/:driver_id/ban-histories", api.RemoveBanHistories)
		internalV1.DELETE("/driver/:driver_id/cancellation-quota", api.RemoveCancellationQuota)
		internalV1.DELETE("/driver/:driver_id/remove-driver-statistic", api.RemoveDriverStatistic)
		internalV1.POST("/driver/:driver_id/set-driver-counts", api.SetDriverCounts)
		internalV1.DELETE("/driver/:driver_id/remove-driver-counts", api.RemoveDriverCounts)
		internalV1.DELETE("/driver/:driver_id/remove-attendance-logs", api.RemoveAttendanceLogs)
		// HACK(thanabodee.c): change to driver_id to avoid router conflict. but still needs
		// to send line_uid.
		internalV1.DELETE("/driver/:driver_id/delete", api.RemoveDriver)
		internalV1.PUT("/driver/:driver_id/unlock", api.UnlockAutoAssignState)
		internalV1.GET("/driver/:driver_id/last-attempt", api.GetDriverLastAttempt)
		internalV1.PUT("/driver/:driver_id/last-attempt", api.SetDriverLastAttempt)

		internalV1.PUT("/driver/:driver_id/sma_rating_score", api.SetSMARatingScore)
		internalV1.GET("/driver/:driver_id/profile_histories", api.GetProfileHistories)
		internalV1.PUT("/driver/:driver_id/profile_histories", api.SetProfileHistories)
		internalV1.POST("/driver/create-transaction", api.CreateTransaction)

		internalV1.PATCH("/drivers-transaction/:driver_id/free-credits/:index", api.SetFreeCreditExpiration)
		internalV1.PUT("/drivers-transaction/:driver_id/today-earning", api.SetTodayEarning)
		internalV1.PUT("/drivers-transaction/:driver_id/reset-balance", api.ResetDriverBalance)

		internalV1.DELETE("/incentive-settings/:incentive_id", api.RemoveIncentive)
		internalV1.GET("/services-areas/update-cache", api.UpdateCache)

		internalV1.DELETE("/service-areas/region/:region", middlewares.ReportDeprecatedUsed(func() bool {
			return flag.IsEnabledWithDefaultFalse(nil, featureflag.ReportAdminServiceAreaApiWasCalled.Name)
		}), api.RemoveServiceAreaByRegion)
		internalV1.DELETE("deprecated/service-areas/region/:region", api.RemoveServiceAreaByRegion)

		internalV1.DELETE(fmt.Sprintf("/client-areas/area/:%s/service-type/:%s", srvarea.KeyClientAreaArea, srvarea.KeyClientServiceType), api.RemoveClientArea)

		internalV1.PUT("/driver/:driver_id/role", api.UpdateDriverRole)

		internalV1.GET("/otp-session/:lineUID", api.GetOTPSession)

		internalV1.PUT("/log-levels", api.SetLogLevel)

		internalV1.DELETE("/transaction-schemes/:transaction_scheme_id", api.DeleteTransactionScheme)
		internalV1.PUT("/driver-uob-ref", api.SetUOBRefID)

		internalV1.POST("/assignment-log/assign-to-driver", api.AssignToDriverRequest)

		internalV1.POST("/trip/:trip_id/sync-trip-state", api.SyncTripState)
		internalV1.GET("/trip/get-unsync", api.GetUnsyncTripIDs)
		internalV1.POST("/bulk/trip/complete-trip", api.BulkCompleteTrip)
		internalV1.POST("/driver/:driver_id/set-driver-income-daily-summary", api.SetDriverIncomeDailySummary)
		internalV1.DELETE("/driver/:driver_id/remove-driver-income-daily-summary", api.RemoveDriverIncomeDailySummary)
		internalV1.GET("/driver/:driver_id/total-income", api.GetTotalIncome)
		internalV1.GET("/driver/:driver_id/transactions/by-trans-ref-id/:trans_ref_id", api.ListDriverTransactionsByTransRefID)

		internalV1.GET("/kafka/producer/imf-connection-check", api.IMFKafkaConnectionCheck)

		internalV1.POST("/distribute-order", api.DistributeOrder)
		internalV1.POST("/redistribute-order", api.RedistributeOrder)
		internalV1.POST("/distribute-orders-in-zone", api.DistributeOrdersInZone)
		internalV1.POST("/publish-distribute-order-event", api.PublishDistributeOrderEvent)

		internalV1.POST("/db-configs/refresh", api.RefreshDBConfigs)

		internalV1.PUT("/rain-situations", rainSituationAPI.UpsertRainSituations)
		internalV1.GET("/check-pip", api.CheckPIPAPIAcccessibility)

		internalV1.POST("/set-recommendation", api.SetDriverRecommendation)
		internalV1.POST("/unset-recommendation", api.UnsetDriverRecommendation)
		internalV1.GET("/unleash-flag", api.CheckUnleashFlag)

		internalV1.POST(("/test-circuite-breaker/delivery"), api.PostDeliveryCircuitBreaker)

		internalV1.POST("/search/direct/driver-in-multipolygon", api.SearchDriverInMultipolygon)
		internalV1.POST("/search/direct/driver-in-radius", api.SearchDriverInRadius)
		internalV1.POST("/assign-order", apis.OrderAssignerHandler.AssignHandler)
		internalV1.POST("/check-if-candidates-enough", api.CheckIfCandidatesEnough)
	}

	internalV2 := router.Group("/v2/internal")
	{
		api := apis.InternalAPI

		internalV2.POST("/distribute-order", api.DistributeOrderV2)
	}
}

func registerPartner(apis *APISet, router *gin.Engine) {
	partnersGroup := router.Group("/partners")
	{
		api := apis.CitiAPI

		// We can remove `/citi/hooks` after we make sure `/citi/v2/hooks` it's work.`
		partnersGroup.POST("/citi/hooks", api.TopupNotification)
		partnersGroup.POST("/citi/v2/hooks", api.TopupNotificationV2)
	}

	{
		api := apis.UobAPI

		partnersGroup.POST("/uob/hooks", api.TopupNotification)
	}
}

func registerRating(apis *APISet, router *gin.Engine) {
	ratingV1 := router.Group("/v1/rating")
	{
		api := apis.RatingAPI
		ratingV1.GET("/options", api.GetAll)
	}
}

func registerSummaryOfChangeAPI(apis *APISet, router *gin.Engine, builder *middlewares.Builder) {
	ratingV1 := router.Group("/v1/driver/summary-of-changes")
	{
		api := apis.SummaryOfChangeAPI
		ratingV1.GET("", builder.Auth(), api.GetSummaryOfChanges)
	}
}

func registerThrottledOrder(apis *APISet, router *gin.Engine) {
	throttledOrderV1 := router.Group("/v1/throttled-orders")
	{
		api := apis.ThrottledOrderAPI
		throttledOrderV1.PUT("/mp/should-pickup-at", api.UpdateMpShouldPickupAt)
		throttledOrderV1.PUT("/mp/should-pickup-at-and-invalidate", api.UpdateMpShouldPickupAtAndInvalidate)
		throttledOrderV1.POST("/find", api.Find)
	}
}

func registerHealthAndMetric(router *gin.Engine) {
	router.GET("/health", health.HealthCheckHandler)
	router.GET("/metric", gin.WrapH(metric.Exporter()))
}

// APISet provides all apis related in this project.
type APISet struct {
	OTPAPI                          *otp.OTPAPI
	DeliveryPortalAPI               *order.DeliveryPortalAPI
	OrderPortalAPI                  *order.OrderPortalAPI
	OrderAPI                        *order.OrderAPI
	TripAPI                         *trip.TripAPI
	OrderAdminAPI                   *order.AdminAPI
	DriverAdminAPI                  *driver.DriverAdminAPI
	PhonesAPI                       *phones.PhonesAPI
	DriverRegisAPI                  *driver.RegistrationAPI
	CitiAPI                         *partners.CitiAPI
	UobAPI                          *partners.UobAPI
	SettingIncentiveAPI             *incentive.IncentiveAPI
	RegionAPI                       *region.RegionAPI
	RegionAdminAPI                  *region.RegionAdminAPI
	RegistrationAdminAPI            *driver.RegistrationAdminAPI
	DeliveryFee                     *deliveryfee.DeliveryFeeAPI
	PaymentAPI                      *payment.API
	SettingServiceAreaAPI           *srvarea.ServiceAreaSettingAPI
	ClientAreaAPI                   *srvarea.ClientAreaAPI
	FraudAPI                        *fraud.FraudAPI
	InternalAPI                     *internalapi.InternalAPI
	HeatMapAPI                      *heatmap.API
	ProvinceAPI                     *province.ProvinceAPI
	ProvinceAdminAPI                *province.ProvinceAdminAPI
	EventAPI                        *event.EventAPI
	RatingAPI                       *rating.RatingAPI
	RatingAdminAPI                  *rating.RatingAdminAPI
	PdpaApi                         *driver.PdpaApi
	DbConfigAdminAPI                *dbconfig.AdminAPI
	ShiftAPI                        *shift.ShiftAPI
	IncentiveSourceAPI              *incentivesource.IncentiveSourceAPI
	WithholdingTaxCertificateApi    *withholdingtaxcertificate.WithholdingTaxCertificateAPI
	ProductApi                      *product.ProductAPI
	DriverInsuranceApi              *driverinsurance.InsuranceAPI
	DriverDocsAPI                   *driver.Document
	DedicatedZoneAPI                *dedicated_zone.DedicatedZoneAPI
	ZoneAdminAPI                    *zone.ZoneAdminAPI
	ThrottledDispatchDetailAdminAPI *throttleddispatchdetail.ThrottledDispatchDetailAdminAPI
	RewardAdminAPI                  *reward.RewardAdminAPI
	RainSituationAPI                *rain_situation.RainSituationAPI
	RainSituationAdminAPI           *rain_situation.RainSituationAdminAPI
	SummaryOfChangeAPI              *summaryofchange.SummaryOfChangeAPI
	FeatureFlagConfigAPI            *featureflagconfig.FeatureFlagConfigAPI
	AssignmentBenchmarkAPI          *assignmentbenchmarks.AssignmentBenchmarkAPI
	OrderAssignerHandler            *orderassigner.OrderAssignerHandler
	SalesforceAdminAPI              *driver.DriverSalesforceAPI
	ThrottledOrderAPI               *throttled_order.ThrottledOrderAPI
}

func init() {
	// initial GIN binding validator once, to avoid custom validator registration racing on integration test
	bv := api_validator.NewDefaultValidator()

	// register domain specific validator here, to avoid cyclic dependency
	v, _ := bv.Engine().(*validator.Validate)
	payment.RegisterApprovalValidator(v)
	payment.RegisterTransactionValidator(v)
	deliveryfee.RegisterValidator(v)

	binding.Validator = bv
}
