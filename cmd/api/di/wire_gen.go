// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package di

import (
	"git.wndv.co/go/pillars/provider"
	provider3 "git.wndv.co/go/srv/provider"
	"git.wndv.co/go/unleash/lmwnunleash"
	provider2 "git.wndv.co/go/unleash/provider"
	"git.wndv.co/go/unleash/strategies"
	"git.wndv.co/lineman/fleet-distribution/cmd/api/http/router"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/assignmentbenchmarks"
	dbconfig2 "git.wndv.co/lineman/fleet-distribution/internal/apis/dbconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/dedicated_zone"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/deliveryfee"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverinsurance"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/event"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/featureflagconfig"
	fraud2 "git.wndv.co/lineman/fleet-distribution/internal/apis/fraud"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentivesource"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/internalapi"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/orderassigner"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/otp"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/phones"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/product"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/province"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rain_situation"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rating"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/region"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/reward"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/shift"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/summaryofchange"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttled_order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttleddispatchdetail"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/trip"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/withholdingtaxcertificate"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/zone"
	"git.wndv.co/lineman/fleet-distribution/internal/aws"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/experimentplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetarea"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetorder"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/heatmapdemand"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/manmap"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/matchrate"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongotxn"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/riderlevel"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/sms"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uobclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uwterror"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/di"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/accountinghub"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/drivertransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/form"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/fraud"
	order2 "git.wndv.co/lineman/fleet-distribution/internal/domain/service/order"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/pendingtransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/email"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/chat"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/driverprovision"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/featureplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/fleetpool"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/formservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/inventoryservice"
	polygon2 "git.wndv.co/lineman/fleet-distribution/internal/grpc/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/priceintervention"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/rainservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/translationservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/user"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure"
	cache2 "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/executor"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
	"git.wndv.co/lineman/fleet-distribution/internal/messages"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/firebase"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/socketio"
	"git.wndv.co/lineman/fleet-distribution/internal/preload"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/tmpl"
	"git.wndv.co/lineman/fleet-shared/dbconfig"
)

// Injectors from container.go:

func InitializeContainer() (*APIContainer, func(), error) {
	pillarsCustomizer := provider.DefaultPillarsCustomizer()
	internalServer, cleanup2, err := provider.ProvidePillarsV2(pillarsCustomizer)
	if err != nil {
		return nil, nil, err
	}
	initFirst := di.ProvideInit1()
	sentryConfig := config.ProvideSentryConfig()
	initSentry, cleanup3 := di.ProvideInitSentry(sentryConfig)
	globalConfig := config.ProvideGlobalConfig()
	initVOS := di.ProvideInit2(globalConfig)
	conn, cleanup4 := di.ProvideDBConnection()
	initMetric := di.ProvideInitMetric()
	initModel := di.ProvideInitModel()
	dbConfigDataStore := config.ProvideDBConfigDataStore(conn)
	prometheusMeter := metric.ProvidePrometheusMeter()
	proxyDBConfigRepository := config.ProvideDBConfigRepository(dbConfigDataStore, prometheusMeter)
	configUpdaterConfig := dbconfig.ProvideConfigUpdaterConfig()
	v := dbconfig.ProvideConfigUpdater(proxyDBConfigRepository, configUpdaterConfig)
	initToggle := di.ProvideInitToggle(v, prometheusMeter)
	setupConfig := di.ProvideTopkekConfig()
	initTopkek, cleanup5 := di.ProvideTopkek(setupConfig, conn)
	secondaryDBConnection, cleanup6 := datastore.ProvideSecondaryDBConnection()
	containerInitializer := di.CommonCmdAppInitializer(initFirst, initSentry, initVOS, conn, initMetric, initModel, initToggle, initTopkek, secondaryDBConnection)
	smsConfig := sms.ProvideSMSConfig()
	client := httpclient.ProvideDefaultClient()
	smsSMS := sms.ProvideSMS(smsConfig, client)
	redisConfig := datastore.ProvideRedisConfig()
	redisConn, cleanup7 := datastore.ProvideRedisConn(redisConfig)
	redisClient := datastore.ProvideRedis(redisConn, redisConfig)
	cacheCache := cache.ProvideCache(redisClient)
	otpConfig := model.ProvideOTPConfig()
	timeNowFunc := model.ProvideTimeNow()
	newOTPSession := model.ProvideNewOTPSession(otpConfig, timeNowFunc)
	proxyOTPSessionRepo := persistence.ProvideOTPSessionRepo(cacheCache, newOTPSession, prometheusMeter)
	endpoint := line.ProvideEnvironmentConfig()
	linehttpClientConfig := line.ProvideLINEHTTPClientConfig(endpoint)
	lineClient := line.ProvideClient(linehttpClientConfig)
	otpapi := otp.ProvideOTPAPI(smsSMS, proxyOTPSessionRepo, lineClient)
	quoteDataStore := persistence.ProvideQuoteDataStore(conn)
	proxyQuoteRepository := persistence.ProvideMongoQuoteRepository(quoteDataStore, prometheusMeter)
	orderDataStore := persistence.ProvideOrderDataStore(conn)
	orderRevisionDataStore := persistence.ProvideOrderRevisionDataStore(conn)
	orderConfig := persistence.ProvideOrderConfig()
	atomicRevisionConfig := persistence.ProvideRevisionConfig(v)
	v2 := config.ProvideAtomicMongoTxnConfig(v)
	txnHelper := mongotxn.ProvideMongoTxnHelper(prometheusMeter, conn, v2, secondaryDBConnection)
	proxyOrderRepository := persistence.ProvideMongoOrderRepository(orderDataStore, orderRevisionDataStore, redisClient, orderConfig, prometheusMeter, atomicRevisionConfig, txnHelper)
	locationRedisConfig := config.ProvideLocationRedisConfig()
	locationRedisClient, cleanup8 := persistence.ProvideLocationRedisClient(locationRedisConfig)
	atomicDriverLocationConfig := persistence.ProvideDriverLocationConfig(v)
	manMapConfig := manmap.ProvideManMapConfig()
	mapService := manmap.ProvideMapServiceClient(manMapConfig, client)
	proxyDriverLocationRepository, cleanup9 := persistence.ProvideDriverLocationRepository(locationRedisClient, atomicDriverLocationConfig, mapService, prometheusMeter)
	driversDataStore := persistence.ProvideDriversDataStore(conn)
	dataStoreInterface := persistence.ProvideAuditLogDataStore(conn)
	proxyAuditLogRepository := persistence.ProvideAuditLogRepository(dataStoreInterface, prometheusMeter)
	attendanceRateConfig := config.ProvideAttendanceRateConfig()
	proxyDriverRepository := persistence.ProvideDriverRepository(driversDataStore, redisClient, attendanceRateConfig, prometheusMeter)
	transactionDataStore := persistence.ProvideTransactionDataStore(conn)
	proxyTransactionRepository := persistence.ProvideDataStoreTransactionRepository(transactionDataStore, prometheusMeter)
	tripDataStore := persistence.ProvideTripDataStore(conn)
	proxyTripRepository := persistence.ProvideTripRepository(tripDataStore, prometheusMeter)
	uobRefDataStore := persistence.ProvideUobRefDataStore(conn)
	proxyUobRefRepository := persistence.ProvideMongoUobRefRepository(uobRefDataStore, prometheusMeter)
	serviceAreaDataStore := persistence.ProvideServiceAreaDataStore(conn)
	serviceAreaRepositoryConfig := config.ProvideServiceAreaRepositoryConfig()
	localcacheConfig := localcache.ProvideLocalCacheConfig()
	caches := localcache.ProvideLocalCache(localcacheConfig)
	fleetAreaClientConfig := fleetarea.ProvideFleetAreaClientConfig()
	fleetAreaClient, cleanup10 := fleetarea.ProvideFleetAreaClient(fleetAreaClientConfig)
	unleashConfig := featureflag.ProvideUnleashConfig()
	configUnleashConfig := provider2.ProvideUnleashConfig()
	v3 := strategies.ProvideStrategies(configUnleashConfig)
	unleashCustomizer := lmwnunleash.ProvideDefaultCustomizer()
	featureflagService, cleanup11 := featureflag.ProvideFeatureFlagService(unleashConfig, v3, unleashCustomizer)
	proxyServiceAreaRepository := persistence.ProvideMongoServiceAreaRepository(serviceAreaDataStore, redisClient, serviceAreaRepositoryConfig, caches, prometheusMeter, fleetAreaClient, featureflagService)
	proxyDriverActiveTimeRepository := persistence.ProvideRedisDriverActiveTimeRepository(redisClient, prometheusMeter)
	driverRatingConfig := config.ProvideDriverRatingConfig()
	paymentConfig := config.ProvidePaymentConfig()
	v4 := config.ProvideAtomicCancellationRateConfig(v)
	redisLocker := locker.ProvideRedisLocker(redisClient)
	atomicDriverServiceConfig := service.ProvideAtomicDriverServiceConfig(v)
	deviceManagerImpl := service.ProvideDeviceManagerImpl(driversDataStore, redisClient)
	deliveryConfig := delivery.ProvideDeliveryConfig()
	clientConfig := delivery.ProvideDeliveryClientConfig()
	fleetAPI := delivery.ProvideclientFleetAPIClient(clientConfig)
	deliveryDelivery := delivery.ProvideDelivery(deliveryConfig, fleetAPI)
	driverService := service.ProvideDriverService(driversDataStore, redisClient, proxyAuditLogRepository, proxyDriverLocationRepository, proxyDriverRepository, proxyTransactionRepository, proxyTripRepository, proxyOrderRepository, proxyUobRefRepository, proxyServiceAreaRepository, proxyDriverActiveTimeRepository, driverRatingConfig, paymentConfig, globalConfig, v4, prometheusMeter, redisLocker, atomicDriverServiceConfig, deviceManagerImpl, deliveryDelivery)
	experimentalMapService := manmap.ProvideExperimentalMapServiceClient(manMapConfig, client)
	driverAssignmentLogsDataStore := persistence.ProvideDriverAssignmentLogsDataStore(conn)
	proxyAssignmentLogRepository := persistence.ProvideMongoAssignmentLogRepository(driverAssignmentLogsDataStore, prometheusMeter)
	firebaseCustomHttpClient := firebase.ProvideFirebaseCustomClient(globalConfig)
	firebaseClientOptions := firebase.ProvideAppOptionConfig(globalConfig, firebaseCustomHttpClient)
	app := firebase.ProvideApp(firebaseClientOptions)
	messagingClient := firebase.ProvideMessaging(app)
	atomicFirebaseConfig := firebase.ProvideFirebaseConfig(v)
	firebasePushNotificationService := firebase.ProvideFirebasePushNotificationService(messagingClient, atomicFirebaseConfig)
	socketioConfig := socketio.ProvideConfig()
	socketioClient := socketio.ProvideSocketIOClient(socketioConfig, client)
	atomicSocketIOConfig := socketio.ProvideSocketIOConfig(v)
	socketIOPushNotificationService := socketio.ProvideSocketIOPushNotificationService(socketioClient, atomicSocketIOConfig)
	overrideNotifyConfig := service.ProvideConfig()
	pushNotifier, cleanup12 := service.ProvidePushNotifier(firebasePushNotificationService, socketIOPushNotificationService, deviceManagerImpl, overrideNotifyConfig)
	locationManagerConfig := config.ProvideLocationManagerConfig()
	v5 := config.ProvideGlobalServiceAreaConfig(v)
	v6 := config.ProvideAtomicPredictionServiceConfig(v)
	servicePreferenceKillSwitchService := service.ProvideServicePreferenceKillSwitchService(featureflagService)
	servicePreferenceService := service.ProvideServicePreferenceService(servicePreferenceKillSwitchService, v5)
	serviceAreaService := service.ProvideServiceAreaServiceImpl(v5, v6, proxyServiceAreaRepository, servicePreferenceService)
	fleetpoolConfig := fleetpool.ProvideConfig()
	connectionFactory := grpc.ProvideGRPCConnectionFactory()
	grpcClient, cleanup13 := fleetpool.ProvideFleetPoolGRPCClient(fleetpoolConfig, connectionFactory)
	riderSearchServiceClient := fleetpool.ProvideRiderSearchServiceClient(grpcClient)
	metricsRegistryImpl := metric.ProvideMetricsRegistry(prometheusMeter)
	locationManagerImpl, cleanup14 := service.ProvideLocationManagerImpl(caches, proxyDriverLocationRepository, deliveryDelivery, proxyDriverRepository, proxyServiceAreaRepository, locationManagerConfig, serviceAreaService, proxyTripRepository, proxyOrderRepository, featureflagService, riderSearchServiceClient, metricsRegistryImpl)
	repConfig := infrastructure.ProvideRepConfig()
	repEventBus, cleanup15 := infrastructure.ProvideRepEventBus(repConfig, prometheusMeter)
	mongoBanMetadataDataStore := persistence.ProvideBanMetadataDataStore(conn)
	proxyBanMetadataRepository := persistence.ProvideBanMetadataRepository(mongoBanMetadataDataStore, prometheusMeter)
	banHistoriesDataStore := persistence.ProvideBanHistoriesDataStore(conn)
	proxyBanHistoryRepository := persistence.ProvideBanHistoryServiceImpl(banHistoriesDataStore, prometheusMeter)
	driverTransactionDataStore := persistence.ProvideDriverTransactionDataStore(conn)
	proxyDriverTransactionRepository := persistence.ProvideDataStoreDriverTransactionRepository(driverTransactionDataStore, prometheusMeter)
	banServiceImpl := service.ProvideBanServiceImpl(proxyDriverRepository, locationManagerImpl, redisClient, pushNotifier, repEventBus, proxyBanMetadataRepository, proxyBanHistoryRepository, proxyDriverTransactionRepository)
	fraudConfig := fraudadvisor.ProvideFraudConfig()
	fraudAdvisorHTTPClientConfig := httpclient.ProvideFraudAdvisorHTTPClientConfig()
	fraudAdvisorHTTPClient := httpclient.ProvideFraudAdvisorHTTPClient(fraudAdvisorHTTPClientConfig)
	fraudAdvisorServiceImpl := fraudadvisor.ProvideFraudAdvisorServiceImpl(fraudConfig, fraudAdvisorHTTPClient, featureflagService)
	transactionFraudScoreDataStore := persistence.ProvideTransactionFraudScoreDataStore(conn)
	proxyTransactionFraudScoreRepository := persistence.ProvideMongoTransactionFraudScoreRepository(transactionFraudScoreDataStore, prometheusMeter)
	productGroupDataStore := persistence.ProvideProductGroupDataStore(conn)
	inventoryserviceConfig := inventoryservice.ProvideConfig()
	priorityGroupServiceClient, cleanup16 := inventoryservice.ProvideGRPCPriorityGroupServiceClient(inventoryserviceConfig)
	proxyProductGroupRepository := persistence.ProvideProductGroupRepository(productGroupDataStore, prometheusMeter, priorityGroupServiceClient)
	productServiceClient, cleanup17 := inventoryservice.ProvideGRPCProductServiceClient(inventoryserviceConfig)
	driverTransactionConfig := config.ProvideDriverTransactionConfig()
	mongoRewardTransactionDataStore := persistence.ProvideMongoRewardTransactionDataStore(conn)
	proxyRewardTransactionRepository := persistence.ProvideMongoRewardTransactionRepository(mongoRewardTransactionDataStore, prometheusMeter)
	incentiveDataStore := incentive.ProvideIncentiveDataStore(conn)
	proxyIncentiveRepository := incentive.ProvideDataStoreIncentiveRepository(incentiveDataStore, prometheusMeter)
	incomeDailySummaryStore := persistence.ProvideIncomeDailySummaryStore(conn)
	proxyIncomeDailySummaryRepository := persistence.ProvideIncomeDailySummaryRepository(incomeDailySummaryStore, prometheusMeter)
	incomeSummaryServiceImpl := income.ProvideIncomeSummaryService(proxyTransactionRepository, proxyRewardTransactionRepository, proxyIncentiveRepository, proxyOrderRepository, proxyIncomeDailySummaryRepository)
	incomeAggregateServiceImpl := aggregate.ProvideIncomeAggregateService(incomeSummaryServiceImpl, proxyIncomeDailySummaryRepository)
	pendingTransactionDataStore := persistence.ProvidePendingTransactionDataStore(conn)
	proxyPendingTransactionRepository := persistence.ProvideDataStorePendingTransactionRepository(pendingTransactionDataStore, prometheusMeter)
	accountingHubTransactionService := accountinghub.ProvideAccountingHubTransactionService(featureflagService)
	driverTransactionServiceV2 := service.ProvideDriverTransactionServiceV2(proxyDriverTransactionRepository, proxyTransactionRepository, proxyProductGroupRepository, txnHelper, productServiceClient, driverTransactionConfig, incomeAggregateServiceImpl, proxyPendingTransactionRepository, accountingHubTransactionService)
	driverTransactionService := payment.ProvideDriverTransactionService(banServiceImpl, proxyDriverTransactionRepository, proxyTransactionRepository, fraudAdvisorServiceImpl, proxyTransactionFraudScoreRepository, driverService, proxyDriverRepository, paymentConfig, txnHelper, proxyProductGroupRepository, driverTransactionServiceV2)
	atomicOrderDBConfig := order.ProvideAtomicOrderDBConfig(v)
	orderAPIConfig := order.ProvideOrderAPIConfig(atomicOrderDBConfig)
	kafkaConfig := infrastructure.ProvideKafkaConfig()
	kafkaEventBus, cleanup18 := infrastructure.ProvideKafkaConnector(kafkaConfig, client, prometheusMeter)
	driverStatisticDataStore := persistence.ProvideDriverStatisticDataStore(conn)
	proxyDriverStatisticRepository := persistence.ProvideDataStoreDriverStatisticRepository(driverStatisticDataStore, prometheusMeter)
	statisticServiceImpl := service.ProvideStatisticServiceImpl(proxyDriverStatisticRepository, proxyAssignmentLogRepository, proxyDriverRepository, proxyIncentiveRepository)
	driverOrderInfoDataStore := persistence.ProvideDriverOrderInfoDataStore(conn)
	proxyDriverOrderInfoRepository := persistence.ProvideDriverOrderInfoRepository(driverOrderInfoDataStore, prometheusMeter)
	v7 := order.ProvideContingencyConfig(v)
	v8 := dispatcherconfig.ProvideAtomicDistributionConfig(v)
	predictionConfig := prediction.ProvidePredictionConfig()
	dalianClient := httpclient.ProvideDalianClient()
	predictionPrediction := prediction.ProvidePrediction(predictionConfig, dalianClient)
	v9 := config.ProvideAtomicTripConfig(v)
	deliveryFeeSettingDataStore := persistence.ProvideDeliveryFeeSettingDataStore(conn)
	deliveryFeeSettingRepositoryConfig := config.ProvideDeliveryFeeSettingRepositoryConfig()
	proxyDeliveryFeeSettingRepository := persistence.ProvideDeliveryFeeSettingMongo(deliveryFeeSettingDataStore, redisClient, deliveryFeeSettingRepositoryConfig, prometheusMeter)
	settingDeliveryFeePriceSchemesDataStore := persistence.ProvideSettingDeliveryFeePriceSchemesDataStore(conn)
	deliveryFeeSettingPriceSchemesRepositoryConfig := config.ProvideDeliveryFeeSettingPriceSchemesRepositoryConfig()
	proxySettingDeliveryFeePriceSchemesRepository := persistence.ProvideSettingDeliveryFeePriceSchemesRepository(settingDeliveryFeePriceSchemesDataStore, redisClient, deliveryFeeSettingPriceSchemesRepositoryConfig, prometheusMeter)
	deliveryFeeService := service.ProvideDeliveryFeeService(proxyDeliveryFeeSettingRepository, proxySettingDeliveryFeePriceSchemesRepository)
	onTopFareDataStore := persistence.ProvideOnTopFareDataStore(conn)
	zoneDataStore := persistence.ProvideZoneDataStore(conn)
	proxyZoneRepository := persistence.ProvideZoneRepository(zoneDataStore, prometheusMeter, featureflagService, fleetAreaClient)
	proxyOnTopFareRepository := persistence.ProvideOnTopFareRepository(onTopFareDataStore, proxyZoneRepository, prometheusMeter)
	onTopFareService := service.ProvideOnTopFareService(proxyOnTopFareRepository, driverService)
	mongoRewardBalanceDataStore := persistence.ProvideRewardBalanceDataStore(conn)
	proxyRewardBalanceRepository := persistence.ProvideRewardBalanceRepository(mongoRewardBalanceDataStore, prometheusMeter)
	mongoDailyRewardDataStore := persistence.ProvideMongoDailyRewardDataStore(conn)
	proxyDailyRewardRepository := persistence.ProvideMongoDailyRewardRepository(mongoDailyRewardDataStore, prometheusMeter)
	rewardService := service.ProvideRewardService(proxyRewardBalanceRepository, proxyDailyRewardRepository, proxyRewardTransactionRepository, proxyDriverRepository, txnHelper)
	atomicMapOverrideConfig := config.ProvideAtomicMapOverrideConfig(v)
	installmentOnTopTransactionProvider := drivertransaction.ProvideInstallmentOnTopTransactionProvider(proxyDriverRepository)
	onTopSchemeTransactionProviderSelector := drivertransaction.ProvideOnTopSchemeTransactionProviderSelector(installmentOnTopTransactionProvider)
	missionLogEventServiceConfig := service.ProvideMissionLogEventServiceConfig()
	imfKafkaProducerConfig := kafcclient.ProvideIMFKafkaProducerConfig()
	imfKafkaClient, cleanup19 := kafcclient.ProvideIMFKafkaClient(imfKafkaProducerConfig)
	imfKafkaProducer := kafcclient.ProvideIMFKafcProducer(imfKafkaProducerConfig, imfKafkaClient)
	polygonConfig := polygon.ProvidePolygonConfig()
	httpPolygon := polygon.ProvideHttpPolygon(polygonConfig, client)
	serviceAreaConfig := config.ProvideServiceAreaConfig()
	preloadExecutor := preload.ProvidePreloadExecutor()
	proxyRegionRepository := persistence.ProvideRegionRepository(httpPolygon, serviceAreaConfig, preloadExecutor, prometheusMeter)
	clientAreaDataStore := persistence.ProvideClientAreaDataStore(conn)
	proxyClientAreaRepository := persistence.ProvideMongoClientAreaRepository(clientAreaDataStore, caches, prometheusMeter)
	areaServiceImpl := service.ProvideAreaServiceImpl(httpPolygon, proxyRegionRepository, proxyClientAreaRepository, caches, servicePreferenceService)
	atomicBackToBackConfig := config.ProvideBackToBackConfig(v)
	driverServiceTypeCapacityService := service.ProvideDriverServiceTypeCapacityService(proxyServiceAreaRepository, proxyTripRepository, areaServiceImpl, featureflagService, atomicBackToBackConfig, proxyDriverRepository)
	missionLogEventService := service.ProvideMissionLogEventService(missionLogEventServiceConfig, imfKafkaProducer, proxyOrderRepository, proxyTripRepository, driverServiceTypeCapacityService)
	serviceOptInReminderRepository := persistence.ProvideOptInReminderRepository(redisClient, prometheusMeter)
	v10 := service.ProvideAtomicServiceOptInReminderServiceConfig(v)
	serviceOptInReminderService := service.ProvideServiceOptInReminderService(proxyDriverRepository, serviceOptInReminderRepository, proxyClientAreaRepository, pushNotifier, v10)
	tripService := service.ProvideTripServices(v9, proxyTripRepository, proxyOrderRepository, proxyDriverRepository, deliveryFeeService, txnHelper, mapService, prometheusMeter, banServiceImpl, driverService, driverTransactionServiceV2, repEventBus, proxyServiceAreaRepository, onTopFareService, rewardService, atomicMapOverrideConfig, deliveryDelivery, onTopSchemeTransactionProviderSelector, proxyPendingTransactionRepository, missionLogEventService, featureflagService, serviceOptInReminderService)
	distributionLogEventServiceConfig := service.ProvideDistributionLogEventServiceConfig()
	secureIMFKafkaProducerConfig := kafcclient.ProvideSecureIMFKafkaProducerConfig()
	secureIMFKafkaClient, cleanup20 := kafcclient.ProvideSecureIMFKafkaClient(secureIMFKafkaProducerConfig)
	secureIMFKafkaProducer := kafcclient.ProvideSecureIMFKafcProducer(secureIMFKafkaProducerConfig, secureIMFKafkaClient)
	distributionLogEventService := service.ProvideDistributionLogEventService(distributionLogEventServiceConfig, secureIMFKafkaProducer)
	distributionLogManager := service.ProvideDistributionLogManager(distributionLogEventService)
	predictionService := service.ProvidePredictionService(serviceAreaService, proxyOrderRepository, predictionPrediction, mapService, v6, proxyTripRepository, tripService, v9, atomicMapOverrideConfig, distributionLogManager, proxySettingDeliveryFeePriceSchemesRepository, servicePreferenceService)
	atomicAutoAcceptConfig := dispatcherconfig.ProvideAutoAcceptConfig(v)
	v11 := dispatcherconfig.ProvideAutoAssignDbConfig(v)
	atomicSupplyPositioningConfig := config.ProvideAtomicSupplyPositioningConfig(v)
	atomicDedicatedPriorityScorerConfig := config.ProvideAtomicDedicatedPriorityScorerConfig(v)
	autoAssignConfig := distribution.ProvideAutoAssignConfig(atomicBackToBackConfig, atomicAutoAcceptConfig, v11, atomicSupplyPositioningConfig, atomicDedicatedPriorityScorerConfig)
	cookingTimeDelayRepository := persistence.ProvideCookingTimeDelayRepository(redisClient, prometheusMeter)
	assignmentDataStore := persistence.ProvideAssignmentDataStore(conn)
	assignmentRepository := persistence.ProvideAssignmentRepository(assignmentDataStore, prometheusMeter)
	throttledOrderDBConfig := config.ProvideThrottledOrderConfig()
	throttledOrderDataStore := persistence.ProvideThrottledOrderDataStore(conn, secondaryDBConnection, throttledOrderDBConfig)
	throttledOrderRepository := persistence.ProvideThrottledOrderRepository(throttledOrderDataStore, prometheusMeter)
	orderDistributionEventServiceConfig := service.ProvideOrderDistributionEventServiceConfig()
	orderDistributionEventService := service.ProvideOrderDistributionEventService(orderDistributionEventServiceConfig, secureIMFKafkaProducer)
	orderDistributionEventManager := service.ProvideOrderDistributionEventManager(orderDistributionEventService)
	unAcknowledgeReassignReposity := persistence.ProvideUnAcknowledgeReassignRepository(redisClient, prometheusMeter)
	uwterrorConfig := uwterror.ProvideConfig()
	featureplatformConfig := featureplatform.ProvideConfig()
	featureplatformClient, cleanup21 := featureplatform.ProvideGRPCFeaturePlatformClient(featureplatformConfig)
	uwterrorService := uwterror.ProvideUWTErrorService(uwterrorConfig, featureplatformClient, caches)
	atomicDistributionExperimentPlatformDbConfig := experimentplatform.ProvideDistributionExperimentPlatformDbConfig(v)
	distributionExperimentPlatformClient, cleanup22 := experimentplatform.ProvideDistributionExperimentPlatformClient(atomicDistributionExperimentPlatformDbConfig)
	acceptorDeps := order.ProvideAcceptorDeps(proxyOrderRepository, proxyDriverLocationRepository, driverService, proxyDriverRepository, mapService, proxyAssignmentLogRepository, pushNotifier, redisLocker, banServiceImpl, driverTransactionService, orderAPIConfig, repEventBus, kafkaEventBus, proxyServiceAreaRepository, v7, serviceAreaService, tripService, proxyTripRepository, cookingTimeDelayRepository, predictionService, onTopFareService, assignmentRepository, throttledOrderRepository, deliveryDelivery, prometheusMeter, atomicMapOverrideConfig, orderDistributionEventManager, unAcknowledgeReassignReposity, uwterrorService, metricsRegistryImpl, serviceOptInReminderService, distributionExperimentPlatformClient)
	acceptor := order.ProvideAcceptor(acceptorDeps)
	dedicatedZoneDataStore := persistence.ProvideDedicatedZoneDataStore(conn)
	dedicatedZoneRepository := persistence.ProvideDedicatedZoneRepository(dedicatedZoneDataStore, caches, prometheusMeter)
	throttledDispatchDetailDataStore := persistence.ProvideThrottledDispatchDetailDataStore(conn)
	throttledDispatchDetailRepository := persistence.ProvideThrottledDispatchDetailRepository(throttledDispatchDetailDataStore, proxyZoneRepository, prometheusMeter)
	dispatcherConfig := dispatcher.ProvideDispatcherConfig()
	dispatcherDispatcher := dispatcher.ProvideDriverServiceDispatcher(dispatcherConfig, client, featureflagService)
	deferredOrderDataStore := persistence.ProvideDeferredOrderDataStore(conn)
	deferredOrderRepository := persistence.ProvideDeferredOrderRepository(deferredOrderDataStore, prometheusMeter)
	atomicRainSituationConfig := service.ProvideAtomicRainSituationConfig(v)
	rainSituationDataStore := persistence.ProvideRainSituationDataStore(conn)
	proxyRainSituationRepository := persistence.ProvideRainSituationRepository(rainSituationDataStore, prometheusMeter)
	vosConfig := file.ProvideVosConfig()
	vosInternalConfig := service.ProvideVosInternalConfig()
	vosFleetConfig := service.ProvideVosFleetConfig()
	vosServiceImpl := service.ProvideVOSServiceImpl(vosConfig, vosInternalConfig, vosFleetConfig)
	priceinterventionConfig := priceintervention.ProvideConfig()
	priceInterventionClient, cleanup23 := priceintervention.ProvidePriceInterventionClient(priceinterventionConfig)
	rainserviceConfig := rainservice.ProvideConfig()
	rainServiceClient, cleanup24 := rainservice.ProvideRainServiceClient(rainserviceConfig)
	slackConfig := slack.ProvideSlackConfig()
	slackSlack := slack.ProvideSlack(slackConfig)
	rainedCache := service.ProvideRainedCache()
	rainSituationServiceImpl := service.ProvideRainSituationService(atomicRainSituationConfig, proxyRainSituationRepository, vosServiceImpl, proxyServiceAreaRepository, priceInterventionClient, rainServiceClient, globalConfig, slackSlack, rainedCache)
	userConfig := user.ProvideConfig()
	userServiceClient, cleanup25 := user.ProvideGRPCUserServiceClient(userConfig)
	workerContextConfig := safe.ProvideWorkerContextConfig()
	cleanupPriority := cleanup.ProvideCleanupPriority()
	workerContext := safe.ProvideWorkerContext(workerContextConfig, cleanupPriority)
	atomicOrderHeartbeatServiceDbConfig := service.ProvideOrderHeartbeatServiceConfig()
	proxyOrderHeartbeatRepository := persistence.ProvideOrderHearthBeatRepository(redisClient, prometheusMeter, workerContext)
	orderHeartbeatServiceImpl := service.ProvideOrderHeartbeatService(atomicOrderHeartbeatServiceDbConfig, proxyOrderHeartbeatRepository)
	distributionServiceConfig := service.ProvideDistributionServiceConfig()
	secureIMFKafkaSyncProducerConfig := kafcclientdistribution.ProvideSecureIMFKafkaSyncProducerConfig()
	secureIMFKafkaSyncClient, cleanup26 := kafcclientdistribution.ProvideSecureIMFKafkaSyncClient(secureIMFKafkaSyncProducerConfig)
	secureIMFKafkaSyncProducer := kafcclientdistribution.ProvideSecureIMFKafkaSyncProducer(secureIMFKafkaSyncProducerConfig, secureIMFKafkaSyncClient)
	distributionService := service.ProvideDistributionService(distributionServiceConfig, secureIMFKafkaSyncProducer)
	fleetOrderClientConfig := fleetorder.ProvideDispatcherConfig()
	fleetOrderClient := fleetorder.ProvideFleetOrderClient(fleetOrderClientConfig, client)
	illegalDriverRepository := persistence.ProvideIllegalDriverRepository(redisClient, prometheusMeter)
	assigningStateManager := distribution.ProvideAssigningStateManager(redisClient)
	autoAssignOrderDistributorDeps := distribution.ProvideAutoAssignOrderDistributorDeps(locationManagerImpl, proxyOrderRepository, proxyDriverRepository, pushNotifier, proxyAssignmentLogRepository, driverTransactionService, proxyDriverStatisticRepository, statisticServiceImpl, proxyDriverOrderInfoRepository, proxyIncentiveRepository, redisLocker, orderAPIConfig, prometheusMeter, repEventBus, v7, kafkaEventBus, v8, predictionService, txnHelper, autoAssignConfig, acceptor, driverService, mapService, dedicatedZoneRepository, proxyDriverLocationRepository, proxyTripRepository, onTopFareService, throttledDispatchDetailRepository, throttledOrderRepository, dispatcherDispatcher, deferredOrderRepository, assignmentRepository, rainSituationServiceImpl, proxyServiceAreaRepository, proxyZoneRepository, userServiceClient, redisClient, deliveryDelivery, workerContext, servicePreferenceService, distributionLogManager, orderHeartbeatServiceImpl, orderDistributionEventManager, distributionExperimentPlatformClient, distributionService, featureflagService, metricsRegistryImpl, fleetOrderClient, illegalDriverRepository, assigningStateManager, throttledOrderDBConfig)
	autoAssignOrderDistributor, cleanup27 := distribution.ProvideAutoAssignOrderDistributor(autoAssignOrderDistributorDeps)
	localTaskExecutor, cleanup28 := executor.ProvideInfraLocalTaskExecutor()
	shiftDataStore := persistence.ProvideShiftDataStore(conn)
	proxyShiftRepository := persistence.ProvideShiftRepository(shiftDataStore, prometheusMeter)
	shiftServices := service.ProvideShiftServices(proxyShiftRepository, proxyDriverRepository, txnHelper)
	cancellerImpl := order.ProvideCanceller(proxyOrderRepository, driverService, proxyAssignmentLogRepository, pushNotifier, proxyDriverRepository, banServiceImpl, repEventBus, kafkaEventBus, driverService, shiftServices, orderAPIConfig, v4, autoAssignOrderDistributor, txnHelper, tripService, proxyTripRepository, cookingTimeDelayRepository, onTopFareService, proxyDriverLocationRepository, mapService, redisLocker, proxyServiceAreaRepository, throttledOrderRepository, deliveryDelivery, prometheusMeter, serviceOptInReminderService, dispatcherDispatcher, featureflagService)
	groupTransactionDataStore := persistence.ProvideGroupTransactionDataStore(conn)
	proxyGroupTransactionRepository := persistence.ProvideMongoGroupTransactionRepository(groupTransactionDataStore, prometheusMeter)
	configValidator := distribution.ProvideConfigValidator(proxyServiceAreaRepository, proxyOrderRepository, throttledDispatchDetailRepository, autoAssignConfig, v8, predictionService, distributionExperimentPlatformClient)
	translationConfig := translation.ProvideConfig()
	translationGRPCClient, cleanup29 := translation.ProvideTranslationGRPCClient(translationConfig, connectionFactory)
	translationServiceClient := translation.ProvideTranslationServiceClient(translationGRPCClient, prometheusMeter)
	translationServiceImpl := service.ProvideTranslationServiceImpl(translationServiceClient)
	config2 := polygon2.ProvideConfig()
	userPolygonServiceClient, cleanup30 := polygon2.ProvideGRPCUserPolygonServiceClient(config2)
	formserviceConfig := formservice.ProvideConfig()
	formServiceClient, cleanup31 := formservice.ProvideGRPCFormServiceClient(formserviceConfig)
	formService := form.ProvideService(formServiceClient)
	pendingtransactionService := pendingtransaction.ProvidePendingTransactionService(proxyPendingTransactionRepository)
	orderService := order2.ProvideOrderService(proxyOrderRepository, pendingtransactionService, featureflagService)
	matchrateConfig := matchrate.ProvideConfig()
	matchrateService := matchrate.ProvideFeaturePlatformService(matchrateConfig, featureplatformClient)
	providerDeps := order.ProvideProviderDeps(proxyQuoteRepository, proxyOrderRepository, proxyDriverLocationRepository, driverService, proxyDriverRepository, mapService, experimentalMapService, proxyAssignmentLogRepository, pushNotifier, deliveryDelivery, banServiceImpl, driverTransactionService, orderAPIConfig, fraudAdvisorServiceImpl, proxyBanHistoryRepository, proxyDriverTransactionRepository, repEventBus, kafkaEventBus, driverService, autoAssignOrderDistributor, deliveryFeeService, areaServiceImpl, localTaskExecutor, statisticServiceImpl, proxyOnTopFareRepository, proxyTransactionRepository, proxyServiceAreaRepository, throttledOrderRepository, prometheusMeter, v7, predictionService, shiftServices, serviceAreaService, cancellerImpl, acceptor, tripService, proxyTripRepository, cookingTimeDelayRepository, onTopFareService, proxyGroupTransactionRepository, configValidator, rewardService, rainSituationServiceImpl, featureflagService, priceInterventionClient, orderHeartbeatServiceImpl, redisLocker, translationServiceImpl, userPolygonServiceClient, missionLogEventService, formService, orderService, matchrateService, uwterrorService, throttledDispatchDetailRepository, distributionService, unAcknowledgeReassignReposity, distributionExperimentPlatformClient)
	foodProviderImpl := order.ProvideFoodProvider(providerDeps, txnHelper, featureflagService, orderDistributionEventManager, serviceOptInReminderService)
	deliveryPortalAPI := order.ProvideDeliveryAPIPortal(foodProviderImpl)
	orderPortalAPI := order.ProvideOrderPortalAPI(orderAPIConfig, foodProviderImpl, proxyOrderRepository, proxyDriverRepository, redisLocker, tripService, driverService, unAcknowledgeReassignReposity)
	assignmentRejectionDataStore := persistence.ProvideAssignmentRejectionDataStore(conn)
	proxyAssignmentRejectionRepository := persistence.ProvideDataStoreAssignmentRejectionRepository(assignmentRejectionDataStore, prometheusMeter)
	ratingRestaurantDataStore := persistence.ProvideRatingRestaurantDataStore(conn)
	proxyRatingRestaurantRepository := persistence.ProvideRatingRestaurantRepository(ratingRestaurantDataStore, prometheusMeter)
	cancelReasonDataStore := persistence.ProvideCancelReasonDataStore(conn)
	proxyCancelReasonRepository := persistence.ProvideDataStoreCancelReasonRepository(cancelReasonDataStore, prometheusMeter)
	mongoCoinCashConversionRateDataStore := persistence.ProvideCoinCashConversionRateDataStore(conn)
	coinCashConversionRateLocalCacheConfig := cache2.ProvideCoinCashConversionRateLocalCacheConfig()
	coinConversionRateLocalCache := cache2.ProvideCoinConversionRateLocalCache(coinCashConversionRateLocalCacheConfig)
	coinConversionRateRedisCache := cache2.ProvideCoinConversionRateRedisCache(redisConn)
	coinConversionRateMinimalRedisCache := cache2.ProvideCoinConversionRateMinimalRedisCache(redisConn)
	proxyCoinCashConversionRateRepository := persistence.ProvideCoinCashConversionRateRepository(mongoCoinCashConversionRateDataStore, prometheusMeter, coinConversionRateLocalCache, coinConversionRateRedisCache, coinConversionRateMinimalRedisCache)
	awsConfig := aws.ProvideConfig()
	rekognitionImpl := aws.ProvideRekognitionClient(awsConfig)
	awsServiceImpl := service.ProvideAwsServiceImpl(rekognitionImpl)
	chatConfig := chat.ProvideConfig()
	chatServiceClient, cleanup32, err := chat.ProvideChatServiceClient(chatConfig, connectionFactory)
	if err != nil {
		cleanup31()
		cleanup30()
		cleanup29()
		cleanup28()
		cleanup27()
		cleanup26()
		cleanup25()
		cleanup24()
		cleanup23()
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	atomicAdminConfig := config.ProvideAdminConfig(v)
	fraudService := fraud.ProvideFraudService(chatServiceClient, atomicAdminConfig)
	orderAPI := order.ProvideOrderAPI(proxyOrderRepository, proxyAssignmentLogRepository, proxyDriverRepository, assignmentRepository, proxyAssignmentRejectionRepository, orderAPIConfig, proxyDriverTransactionRepository, proxyRatingRestaurantRepository, proxyCancelReasonRepository, proxyServiceAreaRepository, proxyCoinCashConversionRateRepository, driverService, banServiceImpl, pushNotifier, driverService, repEventBus, kafkaEventBus, vosServiceImpl, deliveryDelivery, vosConfig, v7, shiftServices, awsServiceImpl, autoAssignOrderDistributor, cancellerImpl, tripService, fraudService, featureflagService)
	tripAPI := trip.ProvideTripAPI(tripService, deliveryDelivery, orderAPIConfig, proxyDriverRepository, proxyTripRepository, proxyOrderRepository, proxyAuditLogRepository, proxyServiceAreaRepository, proxyCoinCashConversionRateRepository, proxyAssignmentLogRepository, assignmentRepository, proxyDriverTransactionRepository, proxyRatingRestaurantRepository, featureflagService)
	internalCancelReasonDataStore := persistence.ProvideInternalCancelReasonDataStore(conn)
	cancelReasonConfig := config.ProvideCancelReasonConfig()
	proxyInternalCancelReasonRepository := persistence.ProvideInternalCancelReasonRepository(internalCancelReasonDataStore, prometheusMeter, redisClient, cancelReasonConfig)
	atomicCancelReasonConfig := service.ProvideAtomicCancelReasonConfig(v)
	bcpOrderDataStore := persistence.ProvideBCPOrderDataStore(conn)
	bcpOrderRepository := persistence.ProvideBCPOrderRepository(bcpOrderDataStore, prometheusMeter)
	adminAPI := order.ProvideAdminAPI(proxyOrderRepository, proxyTripRepository, tripService, proxyDriverRepository, driverTransactionService, orderAPIConfig, deliveryFeeService, proxyTransactionRepository, proxyInternalCancelReasonRepository, proxyAuditLogRepository, cancellerImpl, atomicCancelReasonConfig, onTopFareService, bcpOrderRepository)
	driverRegistrationDataStore := persistence.ProvideDriverRegistrationDataStore(conn)
	proxyDriverRegistrationRepository := persistence.ProvideDriverRegistrationRepository(driverRegistrationDataStore, prometheusMeter)
	attendances := service.ProvideAttendances(proxyShiftRepository, proxyDriverRepository, proxyDriverOrderInfoRepository, attendanceRateConfig)
	driverOrderConfig := driver.ProvideOrderConfig()
	driverUpdateLocationEventServiceConfig := service.ProvideUpdateDriverLocationEventConfig()
	driverUpdateLocationEventService := service.ProvideDriverLocationEventServiceImpl(driverUpdateLocationEventServiceConfig, imfKafkaProducer)
	financialRiskConfig := driver.ProvideFinancialRiskConfig()
	configRiderLevel := riderlevel.ProvideRiderLevelConnectorConfig()
	riderLevelClient := riderlevel.ProvideRiderLevelConnector(featureplatformClient, configRiderLevel)
	driverAdminAPI := driver.ProvideDriverAdminAPI(proxyDriverRepository, driverService, driverService, proxyServiceAreaRepository, proxyBanHistoryRepository, proxyAuditLogRepository, repEventBus, redisClient, proxyDriverRegistrationRepository, pushNotifier, attendances, serviceAreaService, proxyShiftRepository, proxyOrderRepository, dedicatedZoneRepository, driverOrderConfig, driverUpdateLocationEventService, financialRiskConfig, riderLevelClient, featureflagService, onTopFareService)
	whitelistPhoneDataStore := persistence.ProvideWhitelistPhoneDataStore(conn)
	proxyWhitelistPhoneRepository := persistence.ProvideWhitelistPhoneRepository(whitelistPhoneDataStore, prometheusMeter)
	phonesConfig := phones.ProvidePhonesConfig()
	phonesAPI := phones.ProvidePhoneAPI(proxyWhitelistPhoneRepository, phonesConfig)
	registerHandlerConfig := driver.ProvideRegisterHandlerConfig()
	messagesConfig := messages.ProvideConfig()
	lineBotNotificationServiceWorker, cleanup33 := messages.ProvideLineBotNotificationServiceWorker(messagesConfig, client)
	questionsConfig := persistence.ProvideQuestionConfigs()
	template := tmpl.ProvideTemplateService()
	proxyQuestionRepository := persistence.ProvideFileQuestionRepository(questionsConfig, prometheusMeter, featureflagService, template)
	provinceDataStore := persistence.ProvideProvinceDataStore(conn)
	proxyProvinceRepository := persistence.ProvideProvinceRepository(provinceDataStore, prometheusMeter)
	driverProfileRequestConfig := config.ProvideDriverProfileRequestConfig()
	linehttpClient := httpclient.ProvideLINEInternalHTTPClient()
	lineInternalConfig := lineinternal.ProvideLINEInternalConfig()
	lineStatelessTokenCacheRepository := persistence.ProvideLINEStatelessTokenCacheRepository(redisClient, prometheusMeter)
	lineinternalLINEClient := lineinternal.ProvideLINEClient(lineClient, lineStatelessTokenCacheRepository)
	lineinternalClient := lineinternal.ProvideLINEInternalClient(linehttpClient, lineInternalConfig, featureflagService, lineinternalLINEClient)
	registrationAPI := driver.ProvideRegistrationAPI(registerHandlerConfig, globalConfig, vosServiceImpl, driverService, proxyDriverRegistrationRepository, lineBotNotificationServiceWorker, proxyQuestionRepository, proxyWhitelistPhoneRepository, proxyServiceAreaRepository, proxyDriverRepository, proxyTransactionRepository, proxyDriverTransactionRepository, localTaskExecutor, proxyProvinceRepository, proxyOTPSessionRepo, repEventBus, vosConfig, driverProfileRequestConfig, lineClient, lineinternalClient, featureflagService)
	transactionService := payment.ProvideTransactionService(proxyTransactionRepository, proxyDriverTransactionRepository, proxyDriverRepository, paymentConfig, txnHelper)
	atomicCitiConfig := partners.ProvideCitiApiConfig(v)
	citiAPI := partners.ProvideCitiAPI(pushNotifier, proxyDriverRepository, driverTransactionService, transactionService, atomicCitiConfig)
	partnersConfig := partners.ProvideUobApiConfig()
	uobAPI := partners.ProvideUobAPI(partnersConfig, pushNotifier, proxyDriverRepository, driverTransactionService, transactionService)
	incentiveAPI := incentive.ProvideIncentiveAPI(proxyIncentiveRepository, proxyAuditLogRepository, httpPolygon, proxyRegionRepository)
	v12 := region.ProvideAtomicRegionConfig(v)
	regionAPI := region.ProvideRegionAPI(proxyProvinceRepository, v12)
	regionAdminAPI := region.ProvideRegionAdminAPI(proxyRegionRepository)
	registrationAdminAPI := driver.ProvideRegistrationAdminAPI(proxyDriverRegistrationRepository, lineBotNotificationServiceWorker, driverService, driverService, proxyServiceAreaRepository, proxyDriverRepository, proxyDriverTransactionRepository, proxyTransactionRepository, globalConfig, proxyProvinceRepository, repEventBus, proxyAuditLogRepository, registerHandlerConfig, featureflagService)
	deliveryFeeAPI := deliveryfee.ProvideDeliveryFeeAPI(proxyDeliveryFeeSettingRepository, proxyRegionRepository, proxySettingDeliveryFeePriceSchemesRepository)
	emailConfig := email.ProvideEmailConfig()
	emailService := email.ProvideEmailService(emailConfig)
	uobclientConfig := uobclient.ProvideUobConfig()
	uob, cleanup34 := uobclient.ProvideUobClient(uobclientConfig)
	withdrawalTransactionResultsDataStoreDataStore := persistence.ProvideWithdrawalTransactionResultsDataStore(conn)
	proxyWithdrawalTransactionResultsRepository := persistence.ProvideWithdrawalTransactionResultsRepository(withdrawalTransactionResultsDataStoreDataStore, prometheusMeter)
	paymentServiceImpl := payment.ProvidePaymentService(proxyTransactionRepository, transactionService, emailService, paymentConfig, uob, vosServiceImpl, proxyWithdrawalTransactionResultsRepository)
	transactionAPI := payment.ProvideTransactionAPI(proxyTransactionRepository, proxyDriverTransactionRepository, transactionService, emailService, proxyDriverRepository, driverTransactionService, pushNotifier, paymentConfig, vosServiceImpl, uob, paymentServiceImpl)
	transactionSchemeDataStore := persistence.ProvideTransactionSchemeDataStore(conn)
	proxyTransactionSchemeRepository := persistence.ProvideMongoTransactionSchemeRepository(transactionSchemeDataStore, prometheusMeter)
	transactionSchemeAPI := payment.ProvideTransactionSchemeAPI(proxyTransactionSchemeRepository, proxyAuditLogRepository)
	approvalDataStore := persistence.ProvideApprovalDataStore(conn)
	proxyApprovalRepository := persistence.ProvideDataStoreApprovalRepository(approvalDataStore, prometheusMeter)
	approvalService := payment.ProvideApprovalService(proxyApprovalRepository, proxyDriverTransactionRepository, proxyTransactionRepository, proxyDriverRepository, driverTransactionServiceV2, txnHelper)
	approvalCreator := payment.ProvideApprovalCreator(proxyApprovalRepository, proxyDriverRepository, proxyDriverTransactionRepository, proxyTransactionRepository, paymentConfig, proxyOrderRepository)
	approvalAPI := payment.ProvideApprovalAPI(proxyApprovalRepository, approvalService, approvalCreator, proxyDriverTransactionRepository, proxyTransactionRepository, paymentConfig)
	driverTransactionAPI := payment.ProvideDriverTransactionAPI(proxyDriverTransactionRepository, proxyBanHistoryRepository, driverTransactionService, paymentConfig, banServiceImpl, vosServiceImpl)
	groupTransactionAPI := payment.ProvideGroupTransactionAPI(proxyGroupTransactionRepository, proxyDriverTransactionRepository, proxyTransactionRepository, proxyOrderRepository, proxyTransactionSchemeRepository, paymentConfig)
	api := &payment.API{
		TransactionAPI:       transactionAPI,
		TransactionSchemeAPI: transactionSchemeAPI,
		ApprovalAPI:          approvalAPI,
		DriverTransactionAPI: driverTransactionAPI,
		GroupTransactionAPI:  groupTransactionAPI,
	}
	serviceAreaSettingAPI := srvarea.ProvideServiceAreaSettingAPI(proxyServiceAreaRepository, proxyRegionRepository, proxyAuditLogRepository, shiftServices, txnHelper, slackSlack, serviceAreaConfig, globalConfig, proxyZoneRepository, serviceAreaService)
	clientAreaAPI := srvarea.ProvideClientAreaAPI(proxyClientAreaRepository, proxyServiceAreaRepository)
	fraudAPI := fraud2.ProvideFraudAPI(proxyTransactionFraudScoreRepository)
	driverprovisionConfig := driverprovision.ProvideConfig()
	driverProvisionClient, cleanup35 := driverprovision.ProvideGRPCDriverProvisionClient(driverprovisionConfig)
	internalAPI := internalapi.ProvideInternalAPI(proxyOrderRepository, proxyAssignmentLogRepository, proxyDriverRepository, driverService, proxyDriverRegistrationRepository, driversDataStore, proxyDriverTransactionRepository, driverTransactionService, driverTransactionServiceV2, proxyIncentiveRepository, proxyTransactionRepository, proxyBanHistoryRepository, proxyServiceAreaRepository, proxyClientAreaRepository, proxyRegionRepository, proxyDriverLocationRepository, cacheCache, proxyOTPSessionRepo, transactionService, locationManagerImpl, repEventBus, kafkaEventBus, redisClient, proxyDriverStatisticRepository, proxyTransactionSchemeRepository, tripService, proxyTripRepository, driverService, vosServiceImpl, proxyDriverActiveTimeRepository, proxyIncomeDailySummaryRepository, incomeSummaryServiceImpl, redisLocker, imfKafkaProducer, proxyGroupTransactionRepository, txnHelper, throttledOrderRepository, deferredOrderRepository, autoAssignOrderDistributor, dispatcherDispatcher, v, priceInterventionClient, driverProvisionClient, featureflagService, proxyPendingTransactionRepository, deliveryDelivery, distributionService, proxyZoneRepository, throttledOrderDBConfig)
	heatMapDataStore := persistence.ProvideHeatMapDataStore(conn)
	proxyHeatMapRepository := persistence.ProvideHeatMapRepository(heatMapDataStore, prometheusMeter)
	heatmapConfig := heatmap.ProvideHeatMapConfig()
	heatMapDemand := heatmapdemand.ProvideHeatMapDemand(client)
	heatMapMatchingRateDataStore := persistence.ProvideMatchingRateHeatMapDataStore(conn)
	proxyMatchingRateHeatMapRepository := persistence.ProvideMatchingRateHeatMapRepository(heatMapMatchingRateDataStore, prometheusMeter)
	heatmapAPI := heatmap.ProvideHeatMapAPI(proxyHeatMapRepository, heatmapConfig, heatMapDemand, proxyDriverRepository, proxyOnTopFareRepository, proxyOrderRepository, proxyMatchingRateHeatMapRepository, proxyServiceAreaRepository, proxyZoneRepository, httpPolygon)
	provinceAPI := province.ProvideProvinceAPI(proxyProvinceRepository)
	provinceAdminAPI := province.ProvideProvinceAdminAPI(proxyProvinceRepository)
	eventAPI := event.ProvideEventAPI()
	ratingOptionDataStore := persistence.ProvideRatingOptionDataStore(conn)
	proxyRatingOptionRepository := persistence.ProvideRatingOptionRepository(ratingOptionDataStore, prometheusMeter)
	ratingAPI := rating.ProvideRatingOptionAPI(proxyRatingOptionRepository, featureflagService)
	ratingAdminAPI := rating.ProvideRatingAdminAPI(proxyRatingOptionRepository)
	pdpaDataStore := persistence.ProvidePdpaDataStore(conn)
	proxyPdpaRepository := persistence.ProvidePdpaRepository(pdpaDataStore, prometheusMeter, redisClient)
	pdpaApi := driver.ProvidePdpaApi(proxyPdpaRepository)
	dbconfigAdminAPI := dbconfig2.ProvideDbConfigAdminAPI(proxyDBConfigRepository, proxyAuditLogRepository, v, v5, txnHelper)
	shiftCancelDataStore := persistence.ProvideShiftCancelDataStore(conn)
	proxyShiftCancelRepository := persistence.ProvideShiftCancelRepository(shiftCancelDataStore, prometheusMeter)
	atomicShiftConfig := shift.ProvideShiftCancelReasonConfig(v)
	shiftConfig := shift.ProvideShiftConfig(atomicShiftConfig)
	shiftAPI := shift.ProvideShiftAPI(proxyShiftRepository, proxyDriverRepository, proxyShiftCancelRepository, txnHelper, shiftConfig, proxyServiceAreaRepository, attendances)
	v13 := config.ProvideIncentiveSourceConfig(v)
	incentiveSourceServiceImpl := service.ProvideIncentiveSourceServiceImpl(v13)
	incentiveSourceAPI := incentivesource.ProvideIncentiveSourceAPI(incentiveSourceServiceImpl)
	withholdingTaxCertificateDataStore := persistence.ProvideWithholdingTaxCertificateDataStore(conn)
	proxyWithholdingTaxCertificateRepository := persistence.ProvideWithholdingTaxCertificateRepository(withholdingTaxCertificateDataStore, prometheusMeter)
	withholdingtaxcertificateConfig := withholdingtaxcertificate.ProvideConfig()
	withholdingTaxCertificateAPI := withholdingtaxcertificate.ProvideWithholdingTaxCertificateAPI(proxyWithholdingTaxCertificateRepository, vosServiceImpl, withholdingtaxcertificateConfig)
	productConfig := product.ProvideProductConfig()
	productDataStore := persistence.ProvideProductDataStore(conn)
	proxyProductRepository := persistence.ProvideProductRepository(productDataStore, prometheusMeter, productServiceClient)
	productAPI := product.ProvideProductAPI(productConfig, proxyProductRepository, proxyProductGroupRepository, txnHelper)
	driverinsuranceConfig := driverinsurance.ProvideInsuranceConfig()
	driverInsuranceDataStore := persistence.ProvideDriverInsuranceDataStore(conn)
	proxyDriverInsuranceRepository := persistence.ProvideInsuranceRepository(driverInsuranceDataStore, prometheusMeter)
	consolidatedInsuranceDataStore := persistence.ProvideConsolidatedInsuranceDataStore(conn)
	proxyConsolidatedInsuranceRepository := persistence.ProvideConsolidatedInsuranceRepository(consolidatedInsuranceDataStore, prometheusMeter)
	driverInsuranceCfg := service.ProvideDriverInsuranceCfg()
	driverInsuranceServiceImpl := service.ProvideDriverInsuranceService(driverInsuranceCfg, proxyDriverInsuranceRepository, vosServiceImpl)
	insuranceAPI := driverinsurance.ProvideInsuranceAPI(driverinsuranceConfig, txnHelper, driverTransactionServiceV2, vosServiceImpl, proxyDriverRepository, proxyDriverInsuranceRepository, proxyConsolidatedInsuranceRepository, driverInsuranceServiceImpl)
	driverDocumentDatastore := persistence.ProvideDriverDocumentDatastore(conn)
	proxyDriverDocumentRepository := persistence.ProvideDriverDocumentRepository(driverDocumentDatastore, prometheusMeter)
	driverDocumentConfig := driver.ProvideDriverDocumentConfig()
	document := driver.ProvideDocument(proxyWithholdingTaxCertificateRepository, proxyDriverDocumentRepository, vosServiceImpl, driverDocumentConfig)
	dedicatedZoneAPI := dedicated_zone.ProvideDedicatedZoneAPI(dedicatedZoneRepository)
	zoneServiceImpl := service.ProvideZoneServiceImpl(proxyZoneRepository)
	zoneAdminAPI := zone.ProvideZoneAdminAPI(zoneServiceImpl)
	throttledDispatchDetailAdminAPI := throttleddispatchdetail.ProvideThrottledDispatchDetailAdminAPI(throttledDispatchDetailRepository, proxyAuditLogRepository, proxyZoneRepository)
	rewardConfig := reward.ProvideConfig()
	rewardAdminAPI := reward.ProvideRewardAdminAPI(proxyRewardBalanceRepository, proxyDailyRewardRepository, rewardService, proxyCoinCashConversionRateRepository, proxyRewardTransactionRepository, rewardConfig)
	rain_situationConfig := rain_situation.ProvideConfig()
	rainSituationAPI := rain_situation.ProvideRainSituationAPI(rain_situationConfig, rainSituationServiceImpl, proxyDriverRepository, httpPolygon, proxyRainSituationRepository, proxyAuditLogRepository, slackSlack, atomicRainSituationConfig, globalConfig, mapService)
	rainSituationAdminAPI := rain_situation.ProvideRainSituationAdminAPI(rain_situationConfig, proxyRainSituationRepository, rainSituationServiceImpl, txnHelper, slackSlack, atomicRainSituationConfig, globalConfig)
	summaryOfChangeDataStore := persistence.ProvideSummaryOfChangeDataStore(conn)
	proxySummaryOfChangeRepository := persistence.ProvideSummaryOfChangeRepository(summaryOfChangeDataStore, redisClient, caches, prometheusMeter)
	summaryOfChangeAPI := summaryofchange.ProvideSummaryOfChangeAPI(proxySummaryOfChangeRepository, proxyAuditLogRepository)
	featureFlagConfigAPI := featureflagconfig.ProvideFeatureFlagConfigAPI(featureflagService, proxyAuditLogRepository)
	assignmentBenchmarkDataStore := persistence.ProvideAssignmentBenchmarkDataStore(conn)
	assignmentBenchmarkRepository := persistence.ProvideAssignmentBenchmarkRepository(assignmentBenchmarkDataStore, prometheusMeter)
	assignmentBenchmarkCfg := service.ProvideAssignmentBenchmarkCfg()
	assignmentBenchmarkService := service.ProvideAssignmentBenchmarkService(proxyDriverRepository, proxyAssignmentLogRepository, assignmentBenchmarkRepository, proxyDriverLocationRepository, atomicDriverLocationConfig, assignmentBenchmarkCfg)
	assignmentBenchmarkAPI := assignmentbenchmarks.ProvideAssignmentBenchmarkAPI(proxyAuditLogRepository, assignmentBenchmarkService)
	orderAssignerHandler := orderassigner.ProvideOrderAssignerHandler(atomicAutoAcceptConfig, v11, v8, proxyAssignmentLogRepository, illegalDriverRepository, redisLocker, proxyDriverRepository, proxyOrderRepository, proxyServiceAreaRepository, rainSituationServiceImpl, onTopFareService, proxyDriverLocationRepository, mapService, orderAPIConfig, assignmentRepository, statisticServiceImpl, kafkaEventBus, driverService, pushNotifier, txnHelper, prometheusMeter, locationManagerImpl, featureflagService, distributionService, dispatcherDispatcher, distributionLogManager, servicePreferenceService, predictionService, acceptor, workerContext, metricsRegistryImpl)
	driverSalesforceAPI := driver.ProvideDriverSalesforceAPI(driverAdminAPI)
	throttledOrderService := service.ProvideThrottledOrderService(throttledOrderRepository)
	throttledOrderAPI := throttled_order.ProvideThrottledOrderAPI(throttledOrderService)
	apiSet := &router.APISet{
		OTPAPI:                          otpapi,
		DeliveryPortalAPI:               deliveryPortalAPI,
		OrderPortalAPI:                  orderPortalAPI,
		OrderAPI:                        orderAPI,
		TripAPI:                         tripAPI,
		OrderAdminAPI:                   adminAPI,
		DriverAdminAPI:                  driverAdminAPI,
		PhonesAPI:                       phonesAPI,
		DriverRegisAPI:                  registrationAPI,
		CitiAPI:                         citiAPI,
		UobAPI:                          uobAPI,
		SettingIncentiveAPI:             incentiveAPI,
		RegionAPI:                       regionAPI,
		RegionAdminAPI:                  regionAdminAPI,
		RegistrationAdminAPI:            registrationAdminAPI,
		DeliveryFee:                     deliveryFeeAPI,
		PaymentAPI:                      api,
		SettingServiceAreaAPI:           serviceAreaSettingAPI,
		ClientAreaAPI:                   clientAreaAPI,
		FraudAPI:                        fraudAPI,
		InternalAPI:                     internalAPI,
		HeatMapAPI:                      heatmapAPI,
		ProvinceAPI:                     provinceAPI,
		ProvinceAdminAPI:                provinceAdminAPI,
		EventAPI:                        eventAPI,
		RatingAPI:                       ratingAPI,
		RatingAdminAPI:                  ratingAdminAPI,
		PdpaApi:                         pdpaApi,
		DbConfigAdminAPI:                dbconfigAdminAPI,
		ShiftAPI:                        shiftAPI,
		IncentiveSourceAPI:              incentiveSourceAPI,
		WithholdingTaxCertificateApi:    withholdingTaxCertificateAPI,
		ProductApi:                      productAPI,
		DriverInsuranceApi:              insuranceAPI,
		DriverDocsAPI:                   document,
		DedicatedZoneAPI:                dedicatedZoneAPI,
		ZoneAdminAPI:                    zoneAdminAPI,
		ThrottledDispatchDetailAdminAPI: throttledDispatchDetailAdminAPI,
		RewardAdminAPI:                  rewardAdminAPI,
		RainSituationAPI:                rainSituationAPI,
		RainSituationAdminAPI:           rainSituationAdminAPI,
		SummaryOfChangeAPI:              summaryOfChangeAPI,
		FeatureFlagConfigAPI:            featureFlagConfigAPI,
		AssignmentBenchmarkAPI:          assignmentBenchmarkAPI,
		OrderAssignerHandler:            orderAssignerHandler,
		SalesforceAdminAPI:              driverSalesforceAPI,
		ThrottledOrderAPI:               throttledOrderAPI,
	}
	termAndConditionDataStore := persistence.ProvideTermAndConditionDataStore(conn)
	proxyTermAndConditionRepository := persistence.ProvideMongoTermAndConditionRepository(termAndConditionDataStore, prometheusMeter)
	repositorySet := &RepositorySet{
		RegionRepo:                proxyRegionRepository,
		DriverRepo:                proxyDriverRepository,
		TermAndConditionRepo:      proxyTermAndConditionRepository,
		IncentiveRepo:             proxyIncentiveRepository,
		QuoteRepo:                 proxyQuoteRepository,
		OrderRepo:                 proxyOrderRepository,
		BCPRepo:                   bcpOrderRepository,
		ApprovalRepo:              proxyApprovalRepository,
		ClientAreaRepo:            proxyClientAreaRepository,
		DeliveryFeeSettingRepo:    proxyDeliveryFeeSettingRepository,
		DriverLocationRepo:        proxyDriverLocationRepository,
		DriverRegistrationRepo:    proxyDriverRegistrationRepository,
		DriverTransactionRepo:     proxyDriverTransactionRepository,
		GroupTransactionRepo:      proxyGroupTransactionRepository,
		QuestionRepo:              proxyQuestionRepository,
		ServiceAreaRepo:           proxyServiceAreaRepository,
		TransactionFraudScoreRepo: proxyTransactionFraudScoreRepository,
		TransactionRepo:           proxyTransactionRepository,
		ProvinceRepo:              proxyProvinceRepository,
		RatingOptionRepo:          proxyRatingOptionRepository,
		RatingRestaurantRepo:      proxyRatingRestaurantRepository,
		DriverOrderInfoRepo:       proxyDriverOrderInfoRepository,
	}
	middlewareConfig := middlewares.ProvideMiddlewareConfig()
	builder := middlewares.ProvideMiddlewareBuilder(redisClient, redisLocker, middlewareConfig)
	apiSpecConfig := config.ProvideAPISpecConfig()
	routerConfig := config.ProvideRouterConfig()
	routerCustomizer := router.ProvideRouterCustomizer(apiSet, builder, apiSpecConfig, routerConfig, featureflagService)
	gin, cleanup36, err := provider3.ProvideGinServer(routerCustomizer, internalServer)
	if err != nil {
		cleanup35()
		cleanup34()
		cleanup33()
		cleanup32()
		cleanup31()
		cleanup30()
		cleanup29()
		cleanup28()
		cleanup27()
		cleanup26()
		cleanup25()
		cleanup24()
		cleanup23()
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	apiContainer := &APIContainer{
		InternalServer:    internalServer,
		init:              containerInitializer,
		APISet:            apiSet,
		RepositorySet:     repositorySet,
		Preload:           preloadExecutor,
		MiddlewareBuilder: builder,
		TaskExecutor:      localTaskExecutor,
		GinEngine:         gin,
		CleanupPriority:   cleanupPriority,
	}
	return apiContainer, func() {
		cleanup36()
		cleanup35()
		cleanup34()
		cleanup33()
		cleanup32()
		cleanup31()
		cleanup30()
		cleanup29()
		cleanup28()
		cleanup27()
		cleanup26()
		cleanup25()
		cleanup24()
		cleanup23()
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
	}, nil
}
