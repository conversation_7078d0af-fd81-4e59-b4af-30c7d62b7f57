package di

import (
	"firebase.google.com/go/v4/messaging"
	"github.com/google/wire"

	"git.wndv.co/go/pillars"
	ppillars "git.wndv.co/go/pillars/provider"
	"git.wndv.co/go/srv/gin"
	psrv "git.wndv.co/go/srv/provider"
	unleashprovider "git.wndv.co/go/unleash/provider"
	"git.wndv.co/lineman/fleet-distribution/cmd/api/http/router"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	partnerAuth "git.wndv.co/lineman/fleet-distribution/internal/apis/partners/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/aws"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/riderlevel"
	"git.wndv.co/lineman/fleet-distribution/internal/di"
	"git.wndv.co/lineman/fleet-distribution/internal/domain"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/featureplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/executor"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
	"git.wndv.co/lineman/fleet-distribution/internal/messages"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/firebase"
	"git.wndv.co/lineman/fleet-distribution/internal/preload"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-shared/dbconfig"
)

var MainBindingSet = wire.NewSet(
	ppillars.WireSetV2,
	psrv.WireSet,
	dbconfig.WireSet,
	router.ProvideRouterCustomizer,
	di.CommonCmdAppInitializer,
	di.ConfigSet,
	di.MainConfigSet,
	di.Main,
	di.ProviderSet,
	di.UnleashWireSet,
	di.ServiceSet,
	di.GRPCClient,
	di.DriverTransactionProvider,
	unleashprovider.DefaultCustomizerWireSet,
	wire.Bind(new(service.DriverServiceInterface), new(*service.DriverService)),
	wire.Bind(new(service.DeviceManager), new(*service.DeviceManagerImpl)),
	wire.Bind(new(service.Assigner), new(*service.DriverService)),
	wire.Bind(new(service.LocationManager), new(*service.LocationManagerImpl)),
	wire.Bind(new(service.Notifier), new(*service.PushNotifier)),
	wire.Bind(new(service.BanService), new(*service.BanServiceImpl)),
	wire.Bind(new(service.TermAndConditionService), new(*service.TermAndConditionServiceImpl)),
	wire.Bind(new(service.VOSService), new(*service.VOSServiceImpl)),
	wire.Bind(new(aws.Rekognition), new(*aws.RekognitionImpl)),
	wire.Bind(new(service.AwsService), new(*service.AwsServiceImpl)),
	wire.Bind(new(service.StatisticService), new(*service.StatisticServiceImpl)),
	wire.Bind(new(order.OrderDistributor), new(*distribution.AutoAssignOrderDistributor)),
	wire.Bind(new(order.DistributionConfigValidator), new(*distribution.ConfigValidator)),
	wire.Bind(new(order.Locker), new(*order.MemLocker)),
	wire.Bind(new(auth.TokenStorage), new(*auth.RedisTokenStorage)),
	wire.Bind(new(locker.Locker), new(*locker.RedisLocker)),
	wire.Bind(new(polygon.Polygon), new(*polygon.HttpPolygon)),
	wire.Bind(new(fraudadvisor.FraudAdvisorService), new(*fraudadvisor.FraudAdvisorServiceImpl)),
	wire.Bind(new(service.AreaService), new(*service.AreaServiceImpl)),
	wire.Bind(new(payment.PaymentService), new(*payment.PaymentServiceImpl)),
	wire.Bind(new(messages.MessageServiceInterface), new(*messages.LineBotNotificationServiceWorker)),
	wire.Bind(new(firebase.FBMessagingClient), new(*messaging.Client)),
	wire.Bind(new(auth.TokenVerifier), new(*line.Client)),
	wire.Bind(new(repository.DeliveryFeeSettingRepository), new(*repository.ProxyDeliveryFeeSettingRepository)),
	wire.Bind(new(repository.ApprovalRepository), new(*repository.ProxyApprovalRepository)),
	wire.Bind(new(repository.DriverTransactionRepository), new(*repository.ProxyDriverTransactionRepository)),
	wire.Bind(new(repository.TransactionRepository), new(*repository.ProxyTransactionRepository)),
	wire.Bind(new(repository.GroupTransactionRepository), new(*repository.ProxyGroupTransactionRepository)),
	wire.Bind(new(repository.RegionRepository), new(*repository.ProxyRegionRepository)),
	wire.Bind(new(repository.DriverRegistrationRepository), new(*repository.ProxyDriverRegistrationRepository)),
	wire.Bind(new(repository.AuditLogRepository), new(*repository.ProxyAuditLogRepository)),
	wire.Bind(new(repository.ServiceAreaRepository), new(*repository.ProxyServiceAreaRepository)),
	wire.Bind(new(repository.ClientAreaRepository), new(*repository.ProxyClientAreaRepository)),
	wire.Bind(new(repository.TransactionSchemeRepository), new(*repository.ProxyTransactionSchemeRepository)),
	wire.Bind(new(repository.UobRefRepository), new(*repository.ProxyUobRefRepository)),
	wire.Bind(new(repository.TransactionFraudScoreRepository), new(*repository.ProxyTransactionFraudScoreRepository)),
	wire.Bind(new(repository.QuoteRepository), new(*repository.ProxyQuoteRepository)),
	wire.Bind(new(repository.OrderRepository), new(*repository.ProxyOrderRepository)),
	wire.Bind(new(repository.MinimalOrderRepository), new(*repository.ProxyMinimalOrderRepository)),
	wire.Bind(new(repository.QuestionRepository), new(*repository.ProxyQuestionRepository)),
	wire.Bind(new(repository.AssignmentLogRepository), new(*repository.ProxyAssignmentLogRepository)),
	wire.Bind(new(repository.BanHistoryRepository), new(*repository.ProxyBanHistoryRepository)),
	wire.Bind(new(repository.BanMetadataRepository), new(*repository.ProxyBanMetadataRepository)),
	wire.Bind(new(repository.DriverRepository), new(*repository.ProxyDriverRepository)),
	wire.Bind(new(repository.TermAndConditionRepository), new(*repository.ProxyTermAndConditionRepository)),
	wire.Bind(new(repository.ProvinceRepository), new(*repository.ProxyProvinceRepository)),
	wire.Bind(new(repository.RatingOptionRepository), new(*repository.ProxyRatingOptionRepository)),
	wire.Bind(new(repository.RatingRestaurantRepository), new(*repository.ProxyRatingRestaurantRepository)),
	wire.Bind(new(repository.AssignmentRejectionRepository), new(*repository.ProxyAssignmentRejectionRepository)),
	wire.Bind(new(repository.DriverStatisticRepository), new(*repository.ProxyDriverStatisticRepository)),
	wire.Bind(new(repository.PdpaRepository), new(*repository.ProxyPdpaRepository)),
	wire.Bind(new(repository.DriverOrderInfoRepository), new(*repository.ProxyDriverOrderInfoRepository)),
	wire.Bind(new(repository.HeatMapRepository), new(*repository.ProxyHeatMapRepository)),
	wire.Bind(new(repository.MatchingRateHeatMapRepository), new(*repository.ProxyMatchingRateHeatMapRepository)),
	wire.Bind(new(repository.DriverLocationRepository), new(*repository.ProxyDriverLocationRepository)),
	wire.Bind(new(repository.ShiftRepository), new(*repository.ProxyShiftRepository)),
	wire.Bind(new(repository.ShiftCancelRepository), new(*repository.ProxyShiftCancelRepository)),
	wire.Bind(new(repository.OTPSessionRepo), new(*repository.ProxyOTPSessionRepo)),
	wire.Bind(new(repository.CancelReasonRepository), new(*repository.ProxyCancelReasonRepository)),
	wire.Bind(new(repository.AttendanceLogHistoryRepository), new(*repository.ProxyAttendanceLogHistoryRepository)),
	wire.Bind(new(repository.SettingDeliveryFeePriceSchemesRepository), new(*repository.ProxySettingDeliveryFeePriceSchemesRepository)),
	wire.Bind(new(repository.InternalCancelReasonRepository), new(*repository.ProxyInternalCancelReasonRepository)),
	wire.Bind(new(repository.OnTopFareRepository), new(*repository.ProxyOnTopFareRepository)),
	wire.Bind(new(repository.WithholdingTaxCertificateRepository), new(*repository.ProxyWithholdingTaxCertificateRepository)),
	wire.Bind(new(repository.WhitelistPhoneRepository), new(*repository.ProxyWhitelistPhoneRepository)),
	wire.Bind(new(incentive.IncentiveRepository), new(*incentive.ProxyIncentiveRepository)),
	wire.Bind(new(incentive.IncentiveProgressRepository), new(*incentive.ProxyIncentiveProgressRepository)),
	wire.Bind(new(repository.WithdrawalTransactionResultsRepository), new(*repository.ProxyWithdrawalTransactionResultsRepository)),
	wire.Bind(new(repository.ProductRepository), new(*repository.ProxyProductRepository)),
	wire.Bind(new(repository.ProductGroupRepository), new(*repository.ProxyProductGroupRepository)),
	wire.Bind(new(dbconfig.ConfigRetriever), new(*config.ProxyDBConfigRepository)),
	wire.Bind(new(config.DBConfigRepository), new(*config.ProxyDBConfigRepository)),
	wire.Bind(new(domain.EventBus), new(*infrastructure.KafkaEventBus)),
	wire.Bind(new(executor.TaskExecutor), new(*executor.LocalTaskExecutor)),
	wire.Bind(new(rep.REPService), new(*infrastructure.RepEventBus)),
	wire.Bind(new(metric.Meter), new(*metric.PrometheusMeter)),
	wire.Struct(new(payment.API), "*"),
	wire.Struct(new(router.APISet), "*"),
	wire.Struct(new(APIContainer), "*"),
	wire.Struct(new(RepositorySet), "*"),
	wire.Bind(new(service.TripServices), new(*service.TripService)),
	wire.Bind(new(repository.TripRepository), new(*repository.ProxyTripRepository)),
	wire.Bind(new(order.Canceller), new(*order.CancellerImpl)),
	wire.Bind(new(order.FoodProvider), new(*order.FoodProviderImpl)),
	wire.Bind(new(repository.RequestUpdateProfileSectionRepository), new(*repository.ProxyRequestUpdateProfileSectionRepository)),
	wire.Bind(new(repository.RequestUpdateProfileRepository), new(*repository.ProxyRequestUpdateProfileRepository)),
	wire.Bind(new(repository.DriverInsuranceRepository), new(*repository.ProxyDriverInsuranceRepository)),
	wire.Bind(new(repository.DriverDocumentRepository), new(*repository.ProxyDriverDocumentRepository)),
	wire.Bind(new(repository.ConsolidatedInsuranceRepository), new(*repository.ProxyConsolidatedInsuranceRepository)),
	wire.Bind(new(repository.DriverActiveTimeRepository), new(*repository.ProxyDriverActiveTimeRepository)),
	wire.Bind(new(income.IncomeSummaryService), new(*income.IncomeSummaryServiceImpl)),
	wire.Bind(new(repository.IncomeDailySummaryRepository), new(*repository.ProxyIncomeDailySummaryRepository)),
	wire.Bind(new(aggregate.IncomeAggregateService), new(*aggregate.IncomeAggregateServiceImpl)),
	wire.Bind(new(repository.ZoneRepository), new(*repository.ProxyZoneRepository)),
	wire.Bind(new(service.ZoneService), new(*service.ZoneServiceImpl)),
	wire.Bind(new(repository.CounterRepository), new(*repository.ProxyCounterRepository)),
	wire.Bind(new(service.IncentiveSourceService), new(*service.IncentiveSourceServiceImpl)),
	wire.Bind(new(service.IncentiveSourceConfigGetter), new(*config.AtomicIncentiveSourceConfig)),
	wire.Bind(new(repository.TopupCreditReportRepository), new(*repository.ProxyTopupCreditReportRepository)),
	wire.Bind(new(repository.RewardBalanceRepository), new(*repository.ProxyRewardBalanceRepository)),
	wire.Bind(new(repository.DailyRewardRepository), new(*repository.ProxyDailyRewardRepository)),
	wire.Bind(new(repository.RewardTransactionRepository), new(*repository.ProxyRewardTransactionRepository)),
	wire.Bind(new(service.RewardTransactionProcessor), new(*service.RewardService)),
	wire.Bind(new(repository.CoinCashConversionRateRepository), new(*repository.ProxyCoinCashConversionRateRepository)),
	wire.Bind(new(config.ConfigUpdater), new(*dbconfig.AtomicConfigUpdater)),
	wire.Bind(new(service.DriverInsuranceService), new(*service.DriverInsuranceServiceImpl)),
	wire.Bind(new(repository.DriverLastUpdateLocationTrackerRepository), new(*repository.ProxyDriverLastUpdateLocationTrackerRepository)),
	wire.Bind(new(repository.RainSituationRepository), new(*repository.ProxyRainSituationRepository)),
	wire.Bind(new(service.RainSituationService), new(*service.RainSituationServiceImpl)),
	wire.Bind(new(partnerAuth.CitiAuthenticationServices), new(*partnerAuth.CitiAuthenticationService)),
	wire.Bind(new(repository.BulkProcessInfoRepository), new(*repository.ProxyBulkProcessInfoRepository)),
	wire.Bind(new(repository.SummaryOfChangeRepository), new(*repository.ProxySummaryOfChangeRepository)),
	wire.Bind(new(featureplatform.GRPCFeaturePlatformClient), new(*featureplatform.Client)),
	wire.Bind(new(riderlevel.RiderLevelConnector), new(*riderlevel.RiderLevelClient)),
	wire.Bind(new(service.OrderHeartbeatService), new(*service.OrderHeartbeatServiceImpl)),
	wire.Bind(new(repository.OrderHeartbeatRepository), new(*repository.ProxyOrderHeartbeatRepository)),
	wire.Bind(new(service.QRIncidentOrderService), new(*service.QRIncidentOrderServiceImpl)),
	wire.Bind(new(service.TranslationService), new(*service.TranslationServiceImpl)),
	wire.Bind(new(repository.PendingTransactionRepository), new(*repository.ProxyPendingTransactionRepository)),
	wire.Bind(new(metric.MetricsRegistry), new(*metric.MetricsRegistryImpl)),
)

type RepositorySet struct {
	RegionRepo                repository.RegionRepository
	DriverRepo                repository.DriverRepository
	TermAndConditionRepo      repository.TermAndConditionRepository
	IncentiveRepo             incentive.IncentiveRepository
	QuoteRepo                 repository.QuoteRepository
	OrderRepo                 repository.OrderRepository
	BCPRepo                   repository.BCPOrderRepository
	ApprovalRepo              repository.ApprovalRepository
	ClientAreaRepo            repository.ClientAreaRepository
	DeliveryFeeSettingRepo    repository.DeliveryFeeSettingRepository
	DriverLocationRepo        repository.DriverLocationRepository
	DriverRegistrationRepo    repository.DriverRegistrationRepository
	DriverTransactionRepo     repository.DriverTransactionRepository
	GroupTransactionRepo      repository.GroupTransactionRepository
	QuestionRepo              repository.QuestionRepository
	ServiceAreaRepo           repository.ServiceAreaRepository
	TransactionFraudScoreRepo repository.TransactionFraudScoreRepository
	TransactionRepo           repository.TransactionRepository
	ProvinceRepo              repository.ProvinceRepository
	RatingOptionRepo          repository.RatingOptionRepository
	RatingRestaurantRepo      repository.RatingRestaurantRepository
	DriverOrderInfoRepo       repository.DriverOrderInfoRepository
}

type APIContainer struct {
	InternalServer    *pillars.InternalServer
	init              di.ContainerInitializer // keep at first line
	APISet            *router.APISet
	RepositorySet     *RepositorySet
	Preload           *preload.PreloadExecutor
	MiddlewareBuilder *middlewares.Builder
	TaskExecutor      executor.TaskExecutor
	GinEngine         *gin.Gin
	CleanupPriority   cleanup.CleanupPriority
}
