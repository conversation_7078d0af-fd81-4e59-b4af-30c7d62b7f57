// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package di

import (
	provider2 "git.wndv.co/go/pillars/provider"
	"git.wndv.co/go/unleash/lmwnunleash"
	"git.wndv.co/go/unleash/provider"
	"git.wndv.co/go/unleash/strategies"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/di"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-shared/dbconfig"
)

// Injectors from container.go:

func InitializeContainer() (*DistributionConsumerContainer, func(), error) {
	initFirst := di.ProvideInit1()
	sentryConfig := config.ProvideSentryConfig()
	initSentry, cleanup := di.ProvideInitSentry(sentryConfig)
	globalConfig := config.ProvideGlobalConfig()
	initVOS := di.ProvideInit2(globalConfig)
	conn, cleanup2 := di.ProvideDBConnection()
	initMetric := di.ProvideInitMetric()
	initModel := di.ProvideInitModel()
	dbConfigDataStore := config.ProvideDBConfigDataStore(conn)
	prometheusMeter := metric.ProvidePrometheusMeter()
	proxyDBConfigRepository := config.ProvideDBConfigRepository(dbConfigDataStore, prometheusMeter)
	configUpdaterConfig := dbconfig.ProvideConfigUpdaterConfig()
	v := dbconfig.ProvideConfigUpdater(proxyDBConfigRepository, configUpdaterConfig)
	initToggle := di.ProvideInitToggle(v, prometheusMeter)
	setupConfig := di.ProvideTopkekConfig()
	initTopkek, cleanup3 := di.ProvideTopkek(setupConfig, conn)
	secondaryDBConnection, cleanup4 := datastore.ProvideSecondaryDBConnection()
	containerInitializer := di.CommonCmdAppInitializer(initFirst, initSentry, initVOS, conn, initMetric, initModel, initToggle, initTopkek, secondaryDBConnection)
	distributionKafkaConsumerConfig := kafcclientdistribution.ProvideDistributionKafkaConsumerConfig()
	distributionKafcConsumerConfig := kafcclientdistribution.ProvideDistributionKafcConsumerConfig()
	dispatcherConfig := dispatcher.ProvideDispatcherConfig()
	client := httpclient.ProvideDefaultClient()
	unleashConfig := featureflag.ProvideUnleashConfig()
	configUnleashConfig := provider.ProvideUnleashConfig()
	v2 := strategies.ProvideStrategies(configUnleashConfig)
	unleashCustomizer := lmwnunleash.ProvideDefaultCustomizer()
	service, cleanup5 := featureflag.ProvideFeatureFlagService(unleashConfig, v2, unleashCustomizer)
	dispatcherDispatcher := dispatcher.ProvideDriverServiceDispatcher(dispatcherConfig, client, service)
	distributionKafkaConsumer, cleanup6 := kafcclientdistribution.ProvideDistributionKafkaConsumer(distributionKafkaConsumerConfig, distributionKafcConsumerConfig, dispatcherDispatcher)
	pillarsCustomizer := provider2.DefaultPillarsCustomizer()
	internalServer, cleanup7, err := provider2.ProvidePillarsV2(pillarsCustomizer)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	distributionConsumerContainer := &DistributionConsumerContainer{
		init:                      containerInitializer,
		DistributionKafkaConsumer: distributionKafkaConsumer,
		PillarInternalServer:      internalServer,
	}
	return distributionConsumerContainer, func() {
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
