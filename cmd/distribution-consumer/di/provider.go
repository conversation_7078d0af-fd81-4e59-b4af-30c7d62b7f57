package di

import (
	"firebase.google.com/go/v4/messaging"
	"github.com/google/wire"

	"git.wndv.co/go/pillars"
	ppillars "git.wndv.co/go/pillars/provider"
	unleashprovider "git.wndv.co/go/unleash/provider"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	kafcclientdistribution "git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/di"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/firebase"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-shared/dbconfig"
)

var MainBindingSet = wire.NewSet(
	ppillars.WireSetV2,
	dbconfig.WireSet,
	di.CommonCmdAppInitializer,
	di.ConfigSet,
	di.MainConfigSet,
	di.Main,
	di.ProviderSet,
	di.ServiceSet,
	di.UnleashWireSet,
	unleashprovider.DefaultCustomizerWireSet,
	di.DriverTransactionProvider,
	wire.Bind(new(dbconfig.ConfigRetriever), new(*config.ProxyDBConfigRepository)),
	wire.Bind(new(metric.Meter), new(*metric.PrometheusMeter)),
	wire.Bind(new(service.Notifier), new(*service.PushNotifier)),
	wire.Bind(new(service.DriverInsuranceService), new(*service.DriverInsuranceServiceImpl)),
	wire.Bind(new(repository.DriverRepository), new(*repository.ProxyDriverRepository)),
	wire.Bind(new(repository.BanHistoryRepository), new(*repository.ProxyBanHistoryRepository)),
	wire.Bind(new(repository.DriverOrderInfoRepository), new(*repository.ProxyDriverOrderInfoRepository)),
	wire.Bind(new(repository.DriverInsuranceRepository), new(*repository.ProxyDriverInsuranceRepository)),
	wire.Bind(new(repository.IncomeDailySummaryRepository), new(*repository.ProxyIncomeDailySummaryRepository)),
	wire.Bind(new(firebase.FBMessagingClient), new(*messaging.Client)),
	wire.Bind(new(service.DeviceManager), new(*service.DeviceManagerImpl)),
	wire.Bind(new(service.VOSService), new(*service.VOSServiceImpl)),
	wire.Bind(new(repository.AuditLogRepository), new(*repository.ProxyAuditLogRepository)),
	wire.Bind(new(repository.DriverLocationRepository), new(*repository.ProxyDriverLocationRepository)),
	wire.Bind(new(repository.TransactionRepository), new(*repository.ProxyTransactionRepository)),
	wire.Bind(new(repository.TripRepository), new(*repository.ProxyTripRepository)),
	wire.Bind(new(repository.OrderRepository), new(*repository.ProxyOrderRepository)),
	wire.Bind(new(repository.UobRefRepository), new(*repository.ProxyUobRefRepository)),
	wire.Bind(new(repository.ServiceAreaRepository), new(*repository.ProxyServiceAreaRepository)),
	wire.Bind(new(repository.DriverActiveTimeRepository), new(*repository.ProxyDriverActiveTimeRepository)),
	wire.Bind(new(locker.Locker), new(*locker.RedisLocker)),
	wire.Bind(new(repository.RainSituationRepository), new(*repository.ProxyRainSituationRepository)),
	wire.Bind(new(service.RainSituationService), new(*service.RainSituationServiceImpl)),
	wire.Bind(new(repository.ProductGroupRepository), new(*repository.ProxyProductGroupRepository)),
	wire.Bind(new(repository.DriverTransactionRepository), new(*repository.ProxyDriverTransactionRepository)),
	wire.Bind(new(incentive.IncentiveRepository), new(*incentive.ProxyIncentiveRepository)),
	wire.Bind(new(repository.RewardTransactionRepository), new(*repository.ProxyRewardTransactionRepository)),
	wire.Bind(new(income.IncomeSummaryService), new(*income.IncomeSummaryServiceImpl)),
	wire.Bind(new(aggregate.IncomeAggregateService), new(*aggregate.IncomeAggregateServiceImpl)),
	wire.Bind(new(repository.PendingTransactionRepository), new(*repository.ProxyPendingTransactionRepository)),
	wire.Bind(new(service.TripServices), new(*service.TripService)), wire.Bind(new(repository.DeliveryFeeSettingRepository), new(*repository.ProxyDeliveryFeeSettingRepository)),
	wire.Bind(new(repository.SettingDeliveryFeePriceSchemesRepository), new(*repository.ProxySettingDeliveryFeePriceSchemesRepository)),
	wire.Bind(new(service.BanService), new(*service.BanServiceImpl)),
	wire.Bind(new(service.DriverServiceInterface), new(*service.DriverService)),
	wire.Bind(new(rep.REPService), new(*infrastructure.RepEventBus)),
	wire.Bind(new(repository.OnTopFareRepository), new(*repository.ProxyOnTopFareRepository)),
	wire.Bind(new(service.RewardTransactionProcessor), new(*service.RewardService)),
	wire.Bind(new(service.LocationManager), new(*service.LocationManagerImpl)),
	wire.Bind(new(repository.BanMetadataRepository), new(*repository.ProxyBanMetadataRepository)),
	wire.Bind(new(repository.ZoneRepository), new(*repository.ProxyZoneRepository)),
	wire.Bind(new(repository.RewardBalanceRepository), new(*repository.ProxyRewardBalanceRepository)),
	wire.Bind(new(repository.DailyRewardRepository), new(*repository.ProxyDailyRewardRepository)),
	wire.Struct(new(DistributionConsumerContainer), "*"),
)

type DistributionConsumerContainer struct {
	init di.ContainerInitializer

	DistributionKafkaConsumer kafcclientdistribution.DistributionKafkaConsumer
	PillarInternalServer      *pillars.InternalServer
}
