//go:build integration_test
// +build integration_test

package driver

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	testutil2 "github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/account"
	"git.wndv.co/lineman/fleet-distribution/internal/auth/token"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestUpdateDriverBenchmarkCustom(t *testing.T) {
	t.SkipNow()

	container := ittest.NewContainer(t)

	wg := sync.WaitGroup{}
	for i := 0; i < 32; i++ {
		wg.Add(1)
		go func() {
			after := time.After(time.Second * 30)
			for {
				select {
				case <-after:
					wg.Done()
					return
				default:
					randomUpdateDriver(container, t)
				}
			}
		}()
	}

	wg.Wait()
	err := testutil2.GatherAndCompare(prometheus.DefaultGatherer, strings.NewReader(""))
	if err != nil {
		fmt.Printf("%s\n", err)
	}
}

func TestTestUpdateDriverLocationSingle(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)
	randomUpdateDriver(container, t)
}

func randomUpdateDriver(container *ittest.IntegrationTestContainer, b testing.TB) {
	token.SetConfigForTest(token.Config{
		Env:            "testing",
		SignedKey:      "PticNKmam2Ql4Di87ltmPkWud6SeQ6I9",
		ExpireDuration: time.Second * 60,
	})

	ctx := testutil.NewContextWithRecorder()
	driverId := "DRV_CHIANG_MAI_2_ONLINE"
	lat := 12.0
	lng := 13.0

	ctx.SetPUT("/v1/account/location").
		Authorized(container.RedisTokenStore, driverId).
		Body().
		JSON(account.UpdateDriverLocationRequest{
			Lat: lat,
			Lng: lng,
		}).Build().
		Send(container.GinEngineRouter).AssertResponseCode(b, http.StatusNoContent)

	assert.Eventually(b, func() bool {
		_, err := container.DriverLocationRepositoryForTest.GetDriverLocation(context.Background(), driverId)
		return err == nil
	}, time.Second*2, time.Millisecond*100)
	location, err := container.DriverLocationRepositoryForTest.GetDriverLocation(context.Background(), driverId)
	require.NoError(b, err)
	assert.InDelta(b, lat, location.Location.Lat, 0.00001)
	assert.InDelta(b, lng, location.Location.Lng, 0.00001)
}
