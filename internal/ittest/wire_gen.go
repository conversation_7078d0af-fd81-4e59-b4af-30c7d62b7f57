// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package ittest

import (
	"git.wndv.co/go/unleash/lmwnunleash"
	"git.wndv.co/go/unleash/provider"
	"git.wndv.co/go/unleash/strategies"
	"git.wndv.co/go/unleash/test"
	"git.wndv.co/lineman/fleet-distribution/cmd/api/http/router"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/assignmentbenchmarks"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/dbconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/dedicated_zone"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/deliveryfee"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverinsurance"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverorderinfo"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/event"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/featureflagconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/fraud"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentivesource"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/internalapi"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/orderassigner"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/otp"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/phones"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/product"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/province"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rain_situation"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rating"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/region"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/reward"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/shift"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/summaryofchange"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttled_order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttleddispatchdetail"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/trip"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/withholdingtaxcertificate"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/zone"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqclient"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/aws"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/configlocator"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/bcp"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/experimentplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetarea"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetorder"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/heatmapdemand"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/inet_client"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/manmap"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/matchrate"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongoprofiler"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongotxn"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mq"
	polygon2 "git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/riderlevel"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/sms"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uobclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uwterror"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/di"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/accountinghub"
	bcp2 "git.wndv.co/lineman/fleet-distribution/internal/domain/service/bcp"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/drivertransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/form"
	fraud2 "git.wndv.co/lineman/fleet-distribution/internal/domain/service/fraud"
	order2 "git.wndv.co/lineman/fleet-distribution/internal/domain/service/order"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/pendingtransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/email"
	event2 "git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/chat"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/coinplan"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/dapfeast"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/driverprovision"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/egs"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/egsbranch"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/egsorder"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/featureplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/fleetpool"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/formservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/inventoryservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/marketplace"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/priceintervention"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/rainservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/translationservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/user"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure"
	cache2 "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/executor"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
	"git.wndv.co/lineman/fleet-distribution/internal/messages"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/firebase"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/socketio"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/stub"
	"git.wndv.co/lineman/fleet-distribution/internal/preload"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/serviceprovider"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/providers"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	"git.wndv.co/lineman/fleet-distribution/internal/tmpl"
	"github.com/golang/mock/gomock"
	gomock2 "go.uber.org/mock/gomock"
	"testing"
)

// Injectors from containers.go:

func InitializeContainer(t *testing.T, legacyController *gomock.Controller, controller *gomock2.Controller) (*IntegrationTestContainer, func(), error) {
	initFirst := di.ProvideInit1()
	initTestDBConn, cleanup2 := di.ProvideInitTestDBConn()
	conn, cleanup3 := di.ProvideDBConnectionForTest(initTestDBConn)
	topkekForTest := providers.ProvideTopkekForTest(conn)
	fixtures := testdata.ProvideFixtures()
	initTestData := di.ProvideInitTestData(fixtures, conn)
	initMetric := di.ProvideInitMetric()
	initModel := di.ProvideInitModel()
	containerInitializer := IntegrationTestInitializer(initFirst, initTestDBConn, topkekForTest, initTestData, initMetric, initModel)
	dataStoreInterface := persistence.ProvideAuditLogDataStore(conn)
	prometheusMeter := metric.ProvidePrometheusMeter()
	proxyAuditLogRepository := persistence.ProvideAuditLogRepository(dataStoreInterface, prometheusMeter)
	driversDataStore := persistence.ProvideDriversDataStore(conn)
	redisConfig, cleanup4, err := providers.ProvideRedisTestConfig()
	if err != nil {
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	redisConn, cleanup5 := datastore.ProvideRedisConn(redisConfig)
	redisClient := datastore.ProvideRedis(redisConn, redisConfig)
	attendanceRateConfig := config.ProvideAttendanceRateConfig()
	proxyDriverRepository := persistence.ProvideDriverRepository(driversDataStore, redisClient, attendanceRateConfig, prometheusMeter)
	driverAssignmentLogsDataStore := persistence.ProvideDriverAssignmentLogsDataStore(conn)
	proxyAssignmentLogRepository := persistence.ProvideMongoAssignmentLogRepository(driverAssignmentLogsDataStore, prometheusMeter)
	assignmentBenchmarkDataStore := persistence.ProvideAssignmentBenchmarkDataStore(conn)
	assignmentBenchmarkRepository := persistence.ProvideAssignmentBenchmarkRepository(assignmentBenchmarkDataStore, prometheusMeter)
	locationRedisConfig, cleanup6, err := providers.ProvideLocationRedisTestConfig()
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	locationRedisClient, cleanup7 := persistence.ProvideLocationRedisClient(locationRedisConfig)
	dbConfigDataStore := config.ProvideDBConfigDataStore(conn)
	proxyDBConfigRepository := config.ProvideDBConfigRepository(dbConfigDataStore, prometheusMeter)
	configUpdaterConfig := config.ProvideConfigUpdaterConfig()
	dbConfigUpdater := config.ProvideConfigUpdater(proxyDBConfigRepository, configUpdaterConfig)
	atomicDriverLocationConfig := persistence.ProvideDriverLocationTestConfig(dbConfigUpdater)
	stubMapService := manmap.ProvideStubMapService()
	proxyDriverLocationRepository, cleanup8 := persistence.ProvideDriverLocationRepositoryForTest(locationRedisClient, atomicDriverLocationConfig, stubMapService, prometheusMeter)
	assignmentBenchmarkCfg := service.ProvideAssignmentBenchmarkCfg()
	assignmentBenchmarkService := service.ProvideAssignmentBenchmarkService(proxyDriverRepository, proxyAssignmentLogRepository, assignmentBenchmarkRepository, proxyDriverLocationRepository, atomicDriverLocationConfig, assignmentBenchmarkCfg)
	assignmentBenchmarkAPI := assignmentbenchmarks.ProvideAssignmentBenchmarkAPI(proxyAuditLogRepository, assignmentBenchmarkService)
	v := config.ProvideGlobalServiceAreaConfig(dbConfigUpdater)
	v2 := config.ProvideAtomicMongoTxnConfig(dbConfigUpdater)
	secondaryDBConnection, cleanup9 := datastore.ProvideSecondaryDBConnection()
	txnHelper := mongotxn.ProvideMongoTxnHelper(prometheusMeter, conn, v2, secondaryDBConnection)
	adminAPI := dbconfig.ProvideDbConfigAdminAPI(proxyDBConfigRepository, proxyAuditLogRepository, dbConfigUpdater, v, txnHelper)
	dedicatedZoneDataStore := persistence.ProvideDedicatedZoneDataStore(conn)
	localcacheConfig := localcache.ProvideLocalCacheConfig()
	caches := localcache.ProvideLocalCache(localcacheConfig)
	dedicatedZoneRepository := persistence.ProvideDedicatedZoneRepository(dedicatedZoneDataStore, caches, prometheusMeter)
	dedicatedZoneAPI := dedicated_zone.ProvideDedicatedZoneAPI(dedicatedZoneRepository)
	deliveryFeeSettingDataStore := persistence.ProvideDeliveryFeeSettingDataStore(conn)
	deliveryFeeSettingRepositoryConfig := config.ProvideDeliveryFeeSettingRepositoryConfig()
	proxyDeliveryFeeSettingRepository := persistence.ProvideDeliveryFeeSettingMongo(deliveryFeeSettingDataStore, redisClient, deliveryFeeSettingRepositoryConfig, prometheusMeter)
	serviceAreaConfig := config.ProvideServiceAreaConfig()
	stubPolygonApi := providers.ProvideStubPolygonApiForTest(serviceAreaConfig)
	preloadExecutor := preload.ProvidePreloadExecutor()
	proxyRegionRepository := persistence.ProvideRegionRepository(stubPolygonApi, serviceAreaConfig, preloadExecutor, prometheusMeter)
	settingDeliveryFeePriceSchemesDataStore := persistence.ProvideSettingDeliveryFeePriceSchemesDataStore(conn)
	deliveryFeeSettingPriceSchemesRepositoryConfig := config.ProvideDeliveryFeeSettingPriceSchemesRepositoryConfig()
	proxySettingDeliveryFeePriceSchemesRepository := persistence.ProvideSettingDeliveryFeePriceSchemesRepository(settingDeliveryFeePriceSchemesDataStore, redisClient, deliveryFeeSettingPriceSchemesRepositoryConfig, prometheusMeter)
	deliveryFeeAPI := deliveryfee.ProvideDeliveryFeeAPI(proxyDeliveryFeeSettingRepository, proxyRegionRepository, proxySettingDeliveryFeePriceSchemesRepository)
	withholdingTaxCertificateDataStore := persistence.ProvideWithholdingTaxCertificateDataStore(conn)
	proxyWithholdingTaxCertificateRepository := persistence.ProvideWithholdingTaxCertificateRepository(withholdingTaxCertificateDataStore, prometheusMeter)
	driverDocumentDatastore := persistence.ProvideDriverDocumentDatastore(conn)
	proxyDriverDocumentRepository := persistence.ProvideDriverDocumentRepository(driverDocumentDatastore, prometheusMeter)
	vosConfig := file.ProvideVosConfig()
	vosInternalConfig := service.ProvideVosInternalConfig()
	vosFleetConfig := service.ProvideVosFleetConfig()
	vosServiceImpl := service.ProvideVOSServiceImpl(vosConfig, vosInternalConfig, vosFleetConfig)
	driverDocumentConfig := driver.ProvideDriverDocumentConfig()
	document := driver.ProvideDocument(proxyWithholdingTaxCertificateRepository, proxyDriverDocumentRepository, vosServiceImpl, driverDocumentConfig)
	transactionDataStore := persistence.ProvideTransactionDataStore(conn)
	proxyTransactionRepository := persistence.ProvideDataStoreTransactionRepository(transactionDataStore, prometheusMeter)
	tripDataStore := persistence.ProvideTripDataStore(conn)
	proxyTripRepository := persistence.ProvideTripRepository(tripDataStore, prometheusMeter)
	orderDataStore := persistence.ProvideOrderDataStore(conn)
	orderRevisionDataStore := persistence.ProvideOrderRevisionDataStore(conn)
	orderConfig := persistence.ProvideOrderConfig()
	atomicRevisionConfig := persistence.ProvideRevisionConfig(dbConfigUpdater)
	proxyOrderRepository := persistence.ProvideMongoOrderRepository(orderDataStore, orderRevisionDataStore, redisClient, orderConfig, prometheusMeter, atomicRevisionConfig, txnHelper)
	uobRefDataStore := persistence.ProvideUobRefDataStore(conn)
	proxyUobRefRepository := persistence.ProvideMongoUobRefRepository(uobRefDataStore, prometheusMeter)
	serviceAreaDataStore := persistence.ProvideServiceAreaDataStore(conn)
	serviceAreaRepositoryConfig := config.ProvideServiceAreaRepositoryConfig()
	fleetAreaClientConfig := fleetarea.ProvideFleetAreaClientConfig()
	fleetAreaClient, cleanup10 := fleetarea.ProvideFleetAreaClient(fleetAreaClientConfig)
	unleashConfig := featureflag.ProvideUnleashConfig()
	configUnleashConfig := provider.ProvideUnleashConfig()
	v3 := strategies.ProvideStrategies(configUnleashConfig)
	unleashCustomizer := lmwnunleash.ProvideDefaultCustomizer()
	simpleUnleasher := test.ProvideSimpleUnleasher()
	featureflagService, cleanup11 := featureflag.ProvideFeatureFlagServiceForIntegration(unleashConfig, v3, unleashCustomizer, simpleUnleasher)
	proxyServiceAreaRepository := persistence.ProvideMongoServiceAreaRepository(serviceAreaDataStore, redisClient, serviceAreaRepositoryConfig, caches, prometheusMeter, fleetAreaClient, featureflagService)
	proxyDriverActiveTimeRepository := persistence.ProvideRedisDriverActiveTimeRepository(redisClient, prometheusMeter)
	driverRatingConfig := config.ProvideDriverRatingConfig()
	paymentConfig := config.ProvidePaymentConfig()
	globalConfig := config.ProvideGlobalConfig()
	v4 := config.ProvideAtomicCancellationRateConfig(dbConfigUpdater)
	redisLocker := locker.ProvideRedisLocker(redisClient)
	atomicDriverServiceConfig := service.ProvideAtomicDriverServiceConfig(dbConfigUpdater)
	deviceManagerImpl := service.ProvideDeviceManagerImpl(driversDataStore, redisClient)
	deliveryConfig := delivery.ProvideDeliveryConfig()
	clientConfig := delivery.ProvideDeliveryClientConfig()
	client := httpclient.ProvideDefaultClient()
	stubDeliveryFleetApi := delivery.ProvideStubDeliveryFleetApi(clientConfig, client)
	deliveryDelivery := delivery.ProvideDelivery(deliveryConfig, stubDeliveryFleetApi)
	driverService := service.ProvideDriverService(driversDataStore, redisClient, proxyAuditLogRepository, proxyDriverLocationRepository, proxyDriverRepository, proxyTransactionRepository, proxyTripRepository, proxyOrderRepository, proxyUobRefRepository, proxyServiceAreaRepository, proxyDriverActiveTimeRepository, driverRatingConfig, paymentConfig, globalConfig, v4, prometheusMeter, redisLocker, atomicDriverServiceConfig, deviceManagerImpl, deliveryDelivery)
	banHistoriesDataStore := persistence.ProvideBanHistoriesDataStore(conn)
	proxyBanHistoryRepository := persistence.ProvideBanHistoryServiceImpl(banHistoriesDataStore, prometheusMeter)
	repConfig := infrastructure.ProvideRepConfig()
	repEventBus, cleanup12 := infrastructure.ProvideRepEventBus(repConfig, prometheusMeter)
	driverRegistrationDataStore := persistence.ProvideDriverRegistrationDataStore(conn)
	proxyDriverRegistrationRepository := persistence.ProvideDriverRegistrationRepository(driverRegistrationDataStore, prometheusMeter)
	consoleLogMessagingClient := stub.ProvideConsoleFBMessagingClient()
	atomicFirebaseConfig := firebase.ProvideFirebaseConfig(dbConfigUpdater)
	firebasePushNotificationService := firebase.ProvideFirebasePushNotificationService(consoleLogMessagingClient, atomicFirebaseConfig)
	socketioConfig := socketio.ProvideConfig()
	socketioClient := socketio.ProvideSocketIOClient(socketioConfig, client)
	atomicSocketIOConfig := socketio.ProvideSocketIOConfig(dbConfigUpdater)
	socketIOPushNotificationService := socketio.ProvideSocketIOPushNotificationService(socketioClient, atomicSocketIOConfig)
	overrideNotifyConfig := service.ProvideConfig()
	pushNotifier, cleanup13 := service.ProvidePushNotifier(firebasePushNotificationService, socketIOPushNotificationService, deviceManagerImpl, overrideNotifyConfig)
	shiftDataStore := persistence.ProvideShiftDataStore(conn)
	proxyShiftRepository := persistence.ProvideShiftRepository(shiftDataStore, prometheusMeter)
	driverOrderInfoDataStore := persistence.ProvideDriverOrderInfoDataStore(conn)
	proxyDriverOrderInfoRepository := persistence.ProvideDriverOrderInfoRepository(driverOrderInfoDataStore, prometheusMeter)
	attendances := service.ProvideAttendances(proxyShiftRepository, proxyDriverRepository, proxyDriverOrderInfoRepository, attendanceRateConfig)
	v5 := config.ProvideAtomicPredictionServiceConfig(dbConfigUpdater)
	servicePreferenceKillSwitchService := service.ProvideServicePreferenceKillSwitchService(featureflagService)
	servicePreferenceService := service.ProvideServicePreferenceService(servicePreferenceKillSwitchService, v)
	serviceAreaService := service.ProvideServiceAreaServiceImpl(v, v5, proxyServiceAreaRepository, servicePreferenceService)
	driverOrderConfig := driver.ProvideOrderConfig()
	driverUpdateLocationEventServiceConfig := service.ProvideUpdateDriverLocationEventConfig()
	imfKafkaProducerConfig := kafcclient.ProvideIMFKafkaProducerConfig()
	kafkaProducerStubClient, cleanup14 := kafcclient.ProvideIMFKafkaProducerClientForTest(imfKafkaProducerConfig)
	driverUpdateLocationEventService := service.ProvideDriverLocationEventServiceImpl(driverUpdateLocationEventServiceConfig, kafkaProducerStubClient)
	financialRiskConfig := driver.ProvideFinancialRiskConfig()
	configRiderLevel := riderlevel.ProvideRiderLevelConnectorConfig()
	stubRiderLevelConnector := riderlevel.ProvideStubRiderLevelConnector(configRiderLevel)
	onTopFareDataStore := persistence.ProvideOnTopFareDataStore(conn)
	zoneDataStore := persistence.ProvideZoneDataStore(conn)
	proxyZoneRepository := persistence.ProvideZoneRepository(zoneDataStore, prometheusMeter, featureflagService, fleetAreaClient)
	proxyOnTopFareRepository := persistence.ProvideOnTopFareRepository(onTopFareDataStore, proxyZoneRepository, prometheusMeter)
	onTopFareService := service.ProvideOnTopFareService(proxyOnTopFareRepository, driverService)
	driverAdminAPI := driver.ProvideDriverAdminAPI(proxyDriverRepository, driverService, driverService, proxyServiceAreaRepository, proxyBanHistoryRepository, proxyAuditLogRepository, repEventBus, redisClient, proxyDriverRegistrationRepository, pushNotifier, attendances, serviceAreaService, proxyShiftRepository, proxyOrderRepository, dedicatedZoneRepository, driverOrderConfig, driverUpdateLocationEventService, financialRiskConfig, stubRiderLevelConnector, featureflagService, onTopFareService)
	driverNotifyAPI := driver.ProvideDriverNotifyAPI(proxyDriverRepository, pushNotifier)
	driverSalesforceAPI := driver.ProvideDriverSalesforceAPI(driverAdminAPI)
	driverStatusAPI := driver.ProvideDriverStatusAPI(proxyDriverRepository, pushNotifier)
	pdpaDataStore := persistence.ProvidePdpaDataStore(conn)
	proxyPdpaRepository := persistence.ProvidePdpaRepository(pdpaDataStore, prometheusMeter, redisClient)
	pdpaApi := driver.ProvidePdpaApi(proxyPdpaRepository)
	stubMessageService := messages.ProvideStubMessageService()
	driverTransactionDataStore := persistence.ProvideDriverTransactionDataStore(conn)
	proxyDriverTransactionRepository := persistence.ProvideDataStoreDriverTransactionRepository(driverTransactionDataStore, prometheusMeter)
	provinceDataStore := persistence.ProvideProvinceDataStore(conn)
	proxyProvinceRepository := persistence.ProvideProvinceRepository(provinceDataStore, prometheusMeter)
	registerHandlerConfig := driver.ProvideRegisterHandlerConfig()
	registrationAdminAPI := driver.ProvideRegistrationAdminAPI(proxyDriverRegistrationRepository, stubMessageService, driverService, driverService, proxyServiceAreaRepository, proxyDriverRepository, proxyDriverTransactionRepository, proxyTransactionRepository, globalConfig, proxyProvinceRepository, repEventBus, proxyAuditLogRepository, registerHandlerConfig, featureflagService)
	questionsConfig := persistence.ProvideQuestionConfigs()
	template := tmpl.ProvideTemplateService()
	proxyQuestionRepository := persistence.ProvideFileQuestionRepository(questionsConfig, prometheusMeter, featureflagService, template)
	whitelistPhoneDataStore := persistence.ProvideWhitelistPhoneDataStore(conn)
	proxyWhitelistPhoneRepository := persistence.ProvideWhitelistPhoneRepository(whitelistPhoneDataStore, prometheusMeter)
	localTaskExecutor, cleanup15 := executor.ProvideInfraLocalTaskExecutor()
	cacheCache := cache.ProvideCache(redisClient)
	otpConfig := model.ProvideOTPConfig()
	timeNowFunc := model.ProvideTimeNow()
	newOTPSession := model.ProvideNewOTPSession(otpConfig, timeNowFunc)
	proxyOTPSessionRepo := persistence.ProvideOTPSessionRepo(cacheCache, newOTPSession, prometheusMeter)
	driverProfileRequestConfig := config.ProvideDriverProfileRequestConfig()
	endpoint := line.ProvideEnvironmentConfig()
	linehttpClientConfig := line.ProvideLINEHTTPClientConfig(endpoint)
	lineClient := line.ProvideClientStub(linehttpClientConfig)
	linehttpClient, cleanup16, err := httpclient.ProvideLINEInternalHTTPClientStub()
	if err != nil {
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	lineInternalConfig := lineinternal.ProvideLINEInternalConfig()
	lineStatelessTokenCacheRepository := persistence.ProvideLINEStatelessTokenCacheRepository(redisClient, prometheusMeter)
	lineinternalLINEClient := lineinternal.ProvideLINEClient(lineClient, lineStatelessTokenCacheRepository)
	lineinternalClient := lineinternal.ProvideLINEInternalClient(linehttpClient, lineInternalConfig, featureflagService, lineinternalLINEClient)
	registrationAPI := driver.ProvideRegistrationAPI(registerHandlerConfig, globalConfig, vosServiceImpl, driverService, proxyDriverRegistrationRepository, stubMessageService, proxyQuestionRepository, proxyWhitelistPhoneRepository, proxyServiceAreaRepository, proxyDriverRepository, proxyTransactionRepository, proxyDriverTransactionRepository, localTaskExecutor, proxyProvinceRepository, proxyOTPSessionRepo, repEventBus, vosConfig, driverProfileRequestConfig, lineClient, lineinternalClient, featureflagService)
	driverinsuranceConfig := driverinsurance.ProvideInsuranceConfig()
	productGroupDataStore := persistence.ProvideProductGroupDataStore(conn)
	inventoryserviceConfig := inventoryservice.ProvideConfig()
	stubGRPCPriorityGroupService := inventoryservice.ProvideStubGRPCPriorityGroupService(inventoryserviceConfig)
	proxyProductGroupRepository := persistence.ProvideProductGroupRepository(productGroupDataStore, prometheusMeter, stubGRPCPriorityGroupService)
	stubGRPCProductService := inventoryservice.ProvideStubGRPCProductService(inventoryserviceConfig)
	driverTransactionConfig := config.ProvideDriverTransactionConfig()
	mongoRewardTransactionDataStore := persistence.ProvideMongoRewardTransactionDataStore(conn)
	proxyRewardTransactionRepository := persistence.ProvideMongoRewardTransactionRepository(mongoRewardTransactionDataStore, prometheusMeter)
	incentiveDataStore := incentive.ProvideIncentiveDataStore(conn)
	proxyIncentiveRepository := incentive.ProvideDataStoreIncentiveRepository(incentiveDataStore, prometheusMeter)
	incomeDailySummaryStore := persistence.ProvideIncomeDailySummaryStore(conn)
	proxyIncomeDailySummaryRepository := persistence.ProvideIncomeDailySummaryRepository(incomeDailySummaryStore, prometheusMeter)
	incomeSummaryServiceImpl := income.ProvideIncomeSummaryService(proxyTransactionRepository, proxyRewardTransactionRepository, proxyIncentiveRepository, proxyOrderRepository, proxyIncomeDailySummaryRepository)
	incomeAggregateServiceImpl := aggregate.ProvideIncomeAggregateService(incomeSummaryServiceImpl, proxyIncomeDailySummaryRepository)
	pendingTransactionDataStore := persistence.ProvidePendingTransactionDataStore(conn)
	proxyPendingTransactionRepository := persistence.ProvideDataStorePendingTransactionRepository(pendingTransactionDataStore, prometheusMeter)
	accountingHubTransactionService := accountinghub.ProvideAccountingHubTransactionService(featureflagService)
	driverTransactionServiceV2 := service.ProvideDriverTransactionServiceV2(proxyDriverTransactionRepository, proxyTransactionRepository, proxyProductGroupRepository, txnHelper, stubGRPCProductService, driverTransactionConfig, incomeAggregateServiceImpl, proxyPendingTransactionRepository, accountingHubTransactionService)
	driverInsuranceDataStore := persistence.ProvideDriverInsuranceDataStore(conn)
	proxyDriverInsuranceRepository := persistence.ProvideInsuranceRepository(driverInsuranceDataStore, prometheusMeter)
	consolidatedInsuranceDataStore := persistence.ProvideConsolidatedInsuranceDataStore(conn)
	proxyConsolidatedInsuranceRepository := persistence.ProvideConsolidatedInsuranceRepository(consolidatedInsuranceDataStore, prometheusMeter)
	driverInsuranceCfg := service.ProvideDriverInsuranceCfg()
	driverInsuranceServiceImpl := service.ProvideDriverInsuranceService(driverInsuranceCfg, proxyDriverInsuranceRepository, vosServiceImpl)
	insuranceAPI := driverinsurance.ProvideInsuranceAPI(driverinsuranceConfig, txnHelper, driverTransactionServiceV2, vosServiceImpl, proxyDriverRepository, proxyDriverInsuranceRepository, proxyConsolidatedInsuranceRepository, driverInsuranceServiceImpl)
	v6 := driverorderinfo.ProvideAutoBanCompleteOrderTooOftenCfg(dbConfigUpdater)
	v7 := driverorderinfo.ProvideAutoBanCompleteOrderTooFarCfg(dbConfigUpdater)
	locationManagerConfig := config.ProvideLocationManagerConfig()
	fleetpoolConfig := fleetpool.ProvideConfig()
	connectionFactory, err := providers.ProvideServiceGRPCConnFactory()
	if err != nil {
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	grpcClient, cleanup17 := fleetpool.ProvideFleetPoolGRPCClient(fleetpoolConfig, connectionFactory)
	riderSearchServiceClient := fleetpool.ProvideRiderSearchServiceClient(grpcClient)
	metricsRegistryImpl := metric.ProvideMetricsRegistry(prometheusMeter)
	locationManagerImpl, cleanup18 := service.ProvideLocationManagerImpl(caches, proxyDriverLocationRepository, deliveryDelivery, proxyDriverRepository, proxyServiceAreaRepository, locationManagerConfig, serviceAreaService, proxyTripRepository, proxyOrderRepository, featureflagService, riderSearchServiceClient, metricsRegistryImpl)
	mongoBanMetadataDataStore := persistence.ProvideBanMetadataDataStore(conn)
	proxyBanMetadataRepository := persistence.ProvideBanMetadataRepository(mongoBanMetadataDataStore, prometheusMeter)
	banServiceImpl := service.ProvideBanServiceImpl(proxyDriverRepository, locationManagerImpl, redisClient, pushNotifier, repEventBus, proxyBanMetadataRepository, proxyBanHistoryRepository, proxyDriverTransactionRepository)
	shiftServices := service.ProvideShiftServices(proxyShiftRepository, proxyDriverRepository, txnHelper)
	driverOrderInfoAPI := driverorderinfo.ProvideDriverOrderInfoAPI(proxyOrderRepository, proxyDriverOrderInfoRepository, proxyDriverRepository, banServiceImpl, stubMapService, v6, v7, shiftServices)
	eventAPI := event.ProvideEventAPI()
	featureFlagConfigAPI := featureflagconfig.ProvideFeatureFlagConfigAPI(featureflagService, proxyAuditLogRepository)
	transactionFraudScoreDataStore := persistence.ProvideTransactionFraudScoreDataStore(conn)
	proxyTransactionFraudScoreRepository := persistence.ProvideMongoTransactionFraudScoreRepository(transactionFraudScoreDataStore, prometheusMeter)
	fraudAPI := fraud.ProvideFraudAPI(proxyTransactionFraudScoreRepository)
	heatMapDataStore := persistence.ProvideHeatMapDataStore(conn)
	proxyHeatMapRepository := persistence.ProvideHeatMapRepository(heatMapDataStore, prometheusMeter)
	heatmapConfig := heatmap.ProvideHeatMapConfig()
	heatMapDemand := heatmapdemand.ProvideHeatMapDemand(client)
	heatMapMatchingRateDataStore := persistence.ProvideMatchingRateHeatMapDataStore(conn)
	proxyMatchingRateHeatMapRepository := persistence.ProvideMatchingRateHeatMapRepository(heatMapMatchingRateDataStore, prometheusMeter)
	api := heatmap.ProvideHeatMapAPI(proxyHeatMapRepository, heatmapConfig, heatMapDemand, proxyDriverRepository, proxyOnTopFareRepository, proxyOrderRepository, proxyMatchingRateHeatMapRepository, proxyServiceAreaRepository, proxyZoneRepository, stubPolygonApi)
	incentiveAPI := incentive.ProvideIncentiveAPI(proxyIncentiveRepository, proxyAuditLogRepository, stubPolygonApi, proxyRegionRepository)
	incentiveProgressDataStore := incentive.ProvideIncentiveProgressDataStore(conn)
	proxyIncentiveProgressRepository := incentive.ProvideDataStoreIncentiveProgressRepository(incentiveProgressDataStore, prometheusMeter)
	v8 := config.ProvideIncentiveSourceConfig(dbConfigUpdater)
	incentiveSourceServiceImpl := service.ProvideIncentiveSourceServiceImpl(v8)
	incentiveSourceAPI := incentivesource.ProvideIncentiveSourceAPI(incentiveSourceServiceImpl)
	fraudConfig := fraudadvisor.ProvideFraudConfig()
	fraudAdvisorHTTPClient, cleanup19, err := httpclient.ProvideFraudAdvisorHTTPClientStub()
	if err != nil {
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	fraudAdvisorServiceImpl := fraudadvisor.ProvideFraudAdvisorServiceImpl(fraudConfig, fraudAdvisorHTTPClient, featureflagService)
	driverTransactionService := payment.ProvideDriverTransactionService(banServiceImpl, proxyDriverTransactionRepository, proxyTransactionRepository, fraudAdvisorServiceImpl, proxyTransactionFraudScoreRepository, driverService, proxyDriverRepository, paymentConfig, txnHelper, proxyProductGroupRepository, driverTransactionServiceV2)
	clientAreaDataStore := persistence.ProvideClientAreaDataStore(conn)
	proxyClientAreaRepository := persistence.ProvideMongoClientAreaRepository(clientAreaDataStore, caches, prometheusMeter)
	transactionService := payment.ProvideTransactionService(proxyTransactionRepository, proxyDriverTransactionRepository, proxyDriverRepository, paymentConfig, txnHelper)
	stubKafkaConnector := infrastructure.ProvideStubKafkaConnector()
	driverStatisticDataStore := persistence.ProvideDriverStatisticDataStore(conn)
	proxyDriverStatisticRepository := persistence.ProvideDataStoreDriverStatisticRepository(driverStatisticDataStore, prometheusMeter)
	transactionSchemeDataStore := persistence.ProvideTransactionSchemeDataStore(conn)
	proxyTransactionSchemeRepository := persistence.ProvideMongoTransactionSchemeRepository(transactionSchemeDataStore, prometheusMeter)
	v9 := config.ProvideAtomicTripConfig(dbConfigUpdater)
	deliveryFeeService := service.ProvideDeliveryFeeService(proxyDeliveryFeeSettingRepository, proxySettingDeliveryFeePriceSchemesRepository)
	mongoRewardBalanceDataStore := persistence.ProvideRewardBalanceDataStore(conn)
	proxyRewardBalanceRepository := persistence.ProvideRewardBalanceRepository(mongoRewardBalanceDataStore, prometheusMeter)
	mongoDailyRewardDataStore := persistence.ProvideMongoDailyRewardDataStore(conn)
	proxyDailyRewardRepository := persistence.ProvideMongoDailyRewardRepository(mongoDailyRewardDataStore, prometheusMeter)
	rewardService := service.ProvideRewardService(proxyRewardBalanceRepository, proxyDailyRewardRepository, proxyRewardTransactionRepository, proxyDriverRepository, txnHelper)
	atomicMapOverrideConfig := config.ProvideAtomicMapOverrideConfig(dbConfigUpdater)
	installmentOnTopTransactionProvider := drivertransaction.ProvideInstallmentOnTopTransactionProvider(proxyDriverRepository)
	onTopSchemeTransactionProviderSelector := drivertransaction.ProvideOnTopSchemeTransactionProviderSelector(installmentOnTopTransactionProvider)
	missionLogEventServiceConfig := service.ProvideMissionLogEventServiceConfig()
	areaServiceImpl := service.ProvideAreaServiceImpl(stubPolygonApi, proxyRegionRepository, proxyClientAreaRepository, caches, servicePreferenceService)
	atomicBackToBackConfig := config.ProvideBackToBackConfig(dbConfigUpdater)
	driverServiceTypeCapacityService := service.ProvideDriverServiceTypeCapacityService(proxyServiceAreaRepository, proxyTripRepository, areaServiceImpl, featureflagService, atomicBackToBackConfig, proxyDriverRepository)
	missionLogEventService := service.ProvideMissionLogEventService(missionLogEventServiceConfig, kafkaProducerStubClient, proxyOrderRepository, proxyTripRepository, driverServiceTypeCapacityService)
	serviceOptInReminderRepository := persistence.ProvideOptInReminderRepository(redisClient, prometheusMeter)
	v10 := service.ProvideAtomicServiceOptInReminderServiceConfig(dbConfigUpdater)
	serviceOptInReminderService := service.ProvideServiceOptInReminderService(proxyDriverRepository, serviceOptInReminderRepository, proxyClientAreaRepository, pushNotifier, v10)
	tripService := service.ProvideTripServices(v9, proxyTripRepository, proxyOrderRepository, proxyDriverRepository, deliveryFeeService, txnHelper, stubMapService, prometheusMeter, banServiceImpl, driverService, driverTransactionServiceV2, repEventBus, proxyServiceAreaRepository, onTopFareService, rewardService, atomicMapOverrideConfig, deliveryDelivery, onTopSchemeTransactionProviderSelector, proxyPendingTransactionRepository, missionLogEventService, featureflagService, serviceOptInReminderService)
	groupTransactionDataStore := persistence.ProvideGroupTransactionDataStore(conn)
	proxyGroupTransactionRepository := persistence.ProvideMongoGroupTransactionRepository(groupTransactionDataStore, prometheusMeter)
	throttledOrderDBConfig := config.ProvideThrottledOrderConfig()
	throttledOrderDataStore := persistence.ProvideThrottledOrderDataStore(conn, secondaryDBConnection, throttledOrderDBConfig)
	throttledOrderRepository := persistence.ProvideThrottledOrderRepository(throttledOrderDataStore, prometheusMeter)
	deferredOrderDataStore := persistence.ProvideDeferredOrderDataStore(conn)
	deferredOrderRepository := persistence.ProvideDeferredOrderRepository(deferredOrderDataStore, prometheusMeter)
	statisticServiceImpl := service.ProvideStatisticServiceImpl(proxyDriverStatisticRepository, proxyAssignmentLogRepository, proxyDriverRepository, proxyIncentiveRepository)
	atomicOrderDBConfig := order.ProvideAtomicOrderDBConfig(dbConfigUpdater)
	orderAPIConfig := order.ProvideOrderAPIConfig(atomicOrderDBConfig)
	v11 := order.ProvideContingencyConfig(dbConfigUpdater)
	v12 := dispatcherconfig.ProvideAtomicDistributionConfig(dbConfigUpdater)
	predictionConfig := prediction.ProvidePredictionConfig()
	dalianClient := httpclient.ProvideDalianClient()
	predictionPrediction := prediction.ProvidePrediction(predictionConfig, dalianClient)
	distributionLogEventServiceConfig := service.ProvideDistributionLogEventServiceConfig()
	secureIMFKafkaProducerConfig := kafcclient.ProvideSecureIMFKafkaProducerConfig()
	secureKafkaProducerStubClient, cleanup20 := kafcclient.ProvideSecureIMFKafkaProducerClientForTest(secureIMFKafkaProducerConfig)
	distributionLogEventService := service.ProvideDistributionLogEventService(distributionLogEventServiceConfig, secureKafkaProducerStubClient)
	distributionLogManager := service.ProvideDistributionLogManager(distributionLogEventService)
	predictionService := service.ProvidePredictionService(serviceAreaService, proxyOrderRepository, predictionPrediction, stubMapService, v5, proxyTripRepository, tripService, v9, atomicMapOverrideConfig, distributionLogManager, proxySettingDeliveryFeePriceSchemesRepository, servicePreferenceService)
	atomicAutoAcceptConfig := dispatcherconfig.ProvideAutoAcceptConfig(dbConfigUpdater)
	atomicAutoAssignDbConfig := dispatcherconfig.ProvideAutoAssignDbConfig(dbConfigUpdater)
	atomicSupplyPositioningConfig := config.ProvideAtomicSupplyPositioningConfig(dbConfigUpdater)
	atomicDedicatedPriorityScorerConfig := config.ProvideAtomicDedicatedPriorityScorerConfig(dbConfigUpdater)
	autoAssignConfig := distribution.ProvideAutoAssignConfig(atomicBackToBackConfig, atomicAutoAcceptConfig, atomicAutoAssignDbConfig, atomicSupplyPositioningConfig, atomicDedicatedPriorityScorerConfig)
	cookingTimeDelayRepository := persistence.ProvideCookingTimeDelayRepository(redisClient, prometheusMeter)
	assignmentDataStore := persistence.ProvideAssignmentDataStore(conn)
	assignmentRepository := persistence.ProvideAssignmentRepository(assignmentDataStore, prometheusMeter)
	orderDistributionEventServiceConfig := service.ProvideOrderDistributionEventServiceConfig()
	orderDistributionEventService := service.ProvideOrderDistributionEventService(orderDistributionEventServiceConfig, secureKafkaProducerStubClient)
	orderDistributionEventManager := service.ProvideOrderDistributionEventManager(orderDistributionEventService)
	unAcknowledgeReassignReposity := persistence.ProvideUnAcknowledgeReassignRepository(redisClient, prometheusMeter)
	uwterrorConfig := uwterror.ProvideConfig()
	stubGRPCFeaturePlatformClient := featureplatform.ProvideStubGPRCFeaturePlatformClient()
	stubUWTErrorService := uwterror.ProvideStubUWTErrorService(uwterrorConfig, stubGRPCFeaturePlatformClient, caches)
	atomicDistributionExperimentPlatformDbConfig := experimentplatform.ProvideDistributionExperimentPlatformDbConfig(dbConfigUpdater)
	distributionExperimentPlatformClient, cleanup21 := experimentplatform.ProvideDistributionExperimentPlatformClient(atomicDistributionExperimentPlatformDbConfig)
	acceptorDeps := order.ProvideAcceptorDeps(proxyOrderRepository, proxyDriverLocationRepository, driverService, proxyDriverRepository, stubMapService, proxyAssignmentLogRepository, pushNotifier, redisLocker, banServiceImpl, driverTransactionService, orderAPIConfig, repEventBus, stubKafkaConnector, proxyServiceAreaRepository, v11, serviceAreaService, tripService, proxyTripRepository, cookingTimeDelayRepository, predictionService, onTopFareService, assignmentRepository, throttledOrderRepository, deliveryDelivery, prometheusMeter, atomicMapOverrideConfig, orderDistributionEventManager, unAcknowledgeReassignReposity, stubUWTErrorService, metricsRegistryImpl, serviceOptInReminderService, distributionExperimentPlatformClient)
	acceptor := order.ProvideAcceptor(acceptorDeps)
	throttledDispatchDetailDataStore := persistence.ProvideThrottledDispatchDetailDataStore(conn)
	throttledDispatchDetailRepository := persistence.ProvideThrottledDispatchDetailRepository(throttledDispatchDetailDataStore, proxyZoneRepository, prometheusMeter)
	dispatcherConfig := dispatcher.ProvideDispatcherConfig()
	dispatcherDispatcher := dispatcher.ProvideDriverServiceDispatcher(dispatcherConfig, client, featureflagService)
	atomicRainSituationConfig := service.ProvideAtomicRainSituationConfig(dbConfigUpdater)
	rainSituationDataStore := persistence.ProvideRainSituationDataStore(conn)
	proxyRainSituationRepository := persistence.ProvideRainSituationRepository(rainSituationDataStore, prometheusMeter)
	priceinterventionConfig := priceintervention.ProvideConfig()
	priceInterventionClientStub := priceintervention.ProvidePriceInterventionServiceClientStub(priceinterventionConfig)
	rainserviceConfig := rainservice.ProvideConfig()
	mockRainServiceClient := rainservice.ProvideRainServiceClientStub(rainserviceConfig, legacyController)
	slackConfig := slack.ProvideSlackConfig()
	slackStub := slack.ProvideSlackStub(slackConfig)
	rainedCache := service.ProvideRainedCache()
	rainSituationServiceImpl := service.ProvideRainSituationService(atomicRainSituationConfig, proxyRainSituationRepository, vosServiceImpl, proxyServiceAreaRepository, priceInterventionClientStub, mockRainServiceClient, globalConfig, slackStub, rainedCache)
	userConfig := user.ProvideConfig()
	mockUserServiceClient := user.ProvideStubGRPCUserService(userConfig, legacyController)
	workerContextConfig := safe.ProvideWorkerContextConfig()
	cleanupPriority := cleanup.ProvideCleanupPriority()
	workerContext := safe.ProvideWorkerContext(workerContextConfig, cleanupPriority)
	atomicOrderHeartbeatServiceDbConfig := service.ProvideOrderHeartbeatServiceConfig()
	proxyOrderHeartbeatRepository := persistence.ProvideOrderHearthBeatRepository(redisClient, prometheusMeter, workerContext)
	orderHeartbeatServiceImpl := service.ProvideOrderHeartbeatService(atomicOrderHeartbeatServiceDbConfig, proxyOrderHeartbeatRepository)
	distributionServiceConfig := service.ProvideDistributionServiceConfig()
	secureIMFKafkaSyncProducerConfig := kafcclientdistribution.ProvideSecureIMFKafkaSyncProducerConfig()
	secureIMFKafkaSyncProducerStubClient, cleanup22 := kafcclientdistribution.ProvideSecureIMFKafkaSyncProducerClientForTest(secureIMFKafkaSyncProducerConfig)
	distributionService := service.ProvideDistributionService(distributionServiceConfig, secureIMFKafkaSyncProducerStubClient)
	fleetOrderClientConfig := fleetorder.ProvideDispatcherConfig()
	fleetOrderClient := fleetorder.ProvideFleetOrderClient(fleetOrderClientConfig, client)
	illegalDriverRepository := persistence.ProvideIllegalDriverRepository(redisClient, prometheusMeter)
	assigningStateManager := distribution.ProvideAssigningStateManager(redisClient)
	autoAssignOrderDistributorDeps := distribution.ProvideAutoAssignOrderDistributorDeps(locationManagerImpl, proxyOrderRepository, proxyDriverRepository, pushNotifier, proxyAssignmentLogRepository, driverTransactionService, proxyDriverStatisticRepository, statisticServiceImpl, proxyDriverOrderInfoRepository, proxyIncentiveRepository, redisLocker, orderAPIConfig, prometheusMeter, repEventBus, v11, stubKafkaConnector, v12, predictionService, txnHelper, autoAssignConfig, acceptor, driverService, stubMapService, dedicatedZoneRepository, proxyDriverLocationRepository, proxyTripRepository, onTopFareService, throttledDispatchDetailRepository, throttledOrderRepository, dispatcherDispatcher, deferredOrderRepository, assignmentRepository, rainSituationServiceImpl, proxyServiceAreaRepository, proxyZoneRepository, mockUserServiceClient, redisClient, deliveryDelivery, workerContext, servicePreferenceService, distributionLogManager, orderHeartbeatServiceImpl, orderDistributionEventManager, distributionExperimentPlatformClient, distributionService, featureflagService, metricsRegistryImpl, fleetOrderClient, illegalDriverRepository, assigningStateManager, throttledOrderDBConfig)
	autoAssignOrderDistributor, cleanup23 := distribution.ProvideAutoAssignOrderDistributor(autoAssignOrderDistributorDeps)
	driverprovisionConfig := driverprovision.ProvideConfig()
	stubGRPCRecommendationService := driverprovision.ProvideStubGRPCRecommendationService(driverprovisionConfig)
	internalAPI := internalapi.ProvideInternalAPI(proxyOrderRepository, proxyAssignmentLogRepository, proxyDriverRepository, driverService, proxyDriverRegistrationRepository, driversDataStore, proxyDriverTransactionRepository, driverTransactionService, driverTransactionServiceV2, proxyIncentiveRepository, proxyTransactionRepository, proxyBanHistoryRepository, proxyServiceAreaRepository, proxyClientAreaRepository, proxyRegionRepository, proxyDriverLocationRepository, cacheCache, proxyOTPSessionRepo, transactionService, locationManagerImpl, repEventBus, stubKafkaConnector, redisClient, proxyDriverStatisticRepository, proxyTransactionSchemeRepository, tripService, proxyTripRepository, driverService, vosServiceImpl, proxyDriverActiveTimeRepository, proxyIncomeDailySummaryRepository, incomeSummaryServiceImpl, redisLocker, kafkaProducerStubClient, proxyGroupTransactionRepository, txnHelper, throttledOrderRepository, deferredOrderRepository, autoAssignOrderDistributor, dispatcherDispatcher, dbConfigUpdater, priceInterventionClientStub, stubGRPCRecommendationService, featureflagService, proxyPendingTransactionRepository, deliveryDelivery, distributionService, proxyZoneRepository, throttledOrderDBConfig)
	middlewareConfig := middlewares.ProvideMiddlewareConfig()
	builder := middlewares.ProvideMiddlewareBuilder(redisClient, redisLocker, middlewareConfig)
	internalCancelReasonDataStore := persistence.ProvideInternalCancelReasonDataStore(conn)
	cancelReasonConfig := config.ProvideCancelReasonConfig()
	proxyInternalCancelReasonRepository := persistence.ProvideInternalCancelReasonRepository(internalCancelReasonDataStore, prometheusMeter, redisClient, cancelReasonConfig)
	cancellerImpl := order.ProvideCanceller(proxyOrderRepository, driverService, proxyAssignmentLogRepository, pushNotifier, proxyDriverRepository, banServiceImpl, repEventBus, stubKafkaConnector, driverService, shiftServices, orderAPIConfig, v4, autoAssignOrderDistributor, txnHelper, tripService, proxyTripRepository, cookingTimeDelayRepository, onTopFareService, proxyDriverLocationRepository, stubMapService, redisLocker, proxyServiceAreaRepository, throttledOrderRepository, deliveryDelivery, prometheusMeter, serviceOptInReminderService, dispatcherDispatcher, featureflagService)
	atomicCancelReasonConfig := service.ProvideAtomicCancelReasonConfig(dbConfigUpdater)
	bcpOrderDataStore := persistence.ProvideBCPOrderDataStore(conn)
	bcpOrderRepository := persistence.ProvideBCPOrderRepository(bcpOrderDataStore, prometheusMeter)
	orderAdminAPI := order.ProvideAdminAPI(proxyOrderRepository, proxyTripRepository, tripService, proxyDriverRepository, driverTransactionService, orderAPIConfig, deliveryFeeService, proxyTransactionRepository, proxyInternalCancelReasonRepository, proxyAuditLogRepository, cancellerImpl, atomicCancelReasonConfig, onTopFareService, bcpOrderRepository)
	quoteDataStore := persistence.ProvideQuoteDataStore(conn)
	proxyQuoteRepository := persistence.ProvideMongoQuoteRepository(quoteDataStore, prometheusMeter)
	manMapConfig := manmap.ProvideManMapConfig()
	experimentalMapService := manmap.ProvideExperimentalMapServiceClient(manMapConfig, client)
	configValidator := distribution.ProvideConfigValidator(proxyServiceAreaRepository, proxyOrderRepository, throttledDispatchDetailRepository, autoAssignConfig, v12, predictionService, distributionExperimentPlatformClient)
	translationConfig := translation.ProvideConfig()
	translationGRPCClient, cleanup24 := translation.ProvideTranslationGRPCClient(translationConfig, connectionFactory)
	translationServiceClient := translation.ProvideTranslationServiceClient(translationGRPCClient, prometheusMeter)
	translationServiceImpl := service.ProvideTranslationServiceImpl(translationServiceClient)
	polygonConfig := polygon.ProvideConfig()
	mockUserPolygonServiceClient := polygon.ProvideStubGRPCUserPolygonService(polygonConfig, legacyController)
	mockFormServiceClient := formservice.ProvideStubGRPCFormService(legacyController)
	formService := form.ProvideService(mockFormServiceClient)
	pendingtransactionService := pendingtransaction.ProvidePendingTransactionService(proxyPendingTransactionRepository)
	orderService := order2.ProvideOrderService(proxyOrderRepository, pendingtransactionService, featureflagService)
	matchrateConfig := matchrate.ProvideConfig()
	matchrateService := matchrate.ProvideFeaturePlatformService(matchrateConfig, stubGRPCFeaturePlatformClient)
	providerDeps := order.ProvideProviderDeps(proxyQuoteRepository, proxyOrderRepository, proxyDriverLocationRepository, driverService, proxyDriverRepository, stubMapService, experimentalMapService, proxyAssignmentLogRepository, pushNotifier, deliveryDelivery, banServiceImpl, driverTransactionService, orderAPIConfig, fraudAdvisorServiceImpl, proxyBanHistoryRepository, proxyDriverTransactionRepository, repEventBus, stubKafkaConnector, driverService, autoAssignOrderDistributor, deliveryFeeService, areaServiceImpl, localTaskExecutor, statisticServiceImpl, proxyOnTopFareRepository, proxyTransactionRepository, proxyServiceAreaRepository, throttledOrderRepository, prometheusMeter, v11, predictionService, shiftServices, serviceAreaService, cancellerImpl, acceptor, tripService, proxyTripRepository, cookingTimeDelayRepository, onTopFareService, proxyGroupTransactionRepository, configValidator, rewardService, rainSituationServiceImpl, featureflagService, priceInterventionClientStub, orderHeartbeatServiceImpl, redisLocker, translationServiceImpl, mockUserPolygonServiceClient, missionLogEventService, formService, orderService, matchrateService, stubUWTErrorService, throttledDispatchDetailRepository, distributionService, unAcknowledgeReassignReposity, distributionExperimentPlatformClient)
	foodProviderImpl := order.ProvideFoodProvider(providerDeps, txnHelper, featureflagService, orderDistributionEventManager, serviceOptInReminderService)
	deliveryPortalAPI := order.ProvideDeliveryAPIPortal(foodProviderImpl)
	memLocker := order.ProvideMemLocker(redisClient)
	assignmentRejectionDataStore := persistence.ProvideAssignmentRejectionDataStore(conn)
	proxyAssignmentRejectionRepository := persistence.ProvideDataStoreAssignmentRejectionRepository(assignmentRejectionDataStore, prometheusMeter)
	ratingRestaurantDataStore := persistence.ProvideRatingRestaurantDataStore(conn)
	proxyRatingRestaurantRepository := persistence.ProvideRatingRestaurantRepository(ratingRestaurantDataStore, prometheusMeter)
	cancelReasonDataStore := persistence.ProvideCancelReasonDataStore(conn)
	proxyCancelReasonRepository := persistence.ProvideDataStoreCancelReasonRepository(cancelReasonDataStore, prometheusMeter)
	mongoCoinCashConversionRateDataStore := persistence.ProvideCoinCashConversionRateDataStore(conn)
	coinCashConversionRateLocalCacheConfig := cache2.ProvideCoinCashConversionRateLocalCacheConfig()
	coinConversionRateLocalCache := cache2.ProvideCoinConversionRateLocalCache(coinCashConversionRateLocalCacheConfig)
	coinConversionRateRedisCache := cache2.ProvideCoinConversionRateRedisCache(redisConn)
	coinConversionRateMinimalRedisCache := cache2.ProvideCoinConversionRateMinimalRedisCache(redisConn)
	proxyCoinCashConversionRateRepository := persistence.ProvideCoinCashConversionRateRepository(mongoCoinCashConversionRateDataStore, prometheusMeter, coinConversionRateLocalCache, coinConversionRateRedisCache, coinConversionRateMinimalRedisCache)
	awsConfig := aws.ProvideConfig()
	rekognitionImpl := aws.ProvideRekognitionClient(awsConfig)
	awsServiceImpl := service.ProvideAwsServiceImpl(rekognitionImpl)
	mockChatServiceClient := chat.ProvideStubChatGRPCService(controller)
	atomicAdminConfig := config.ProvideAdminConfig(dbConfigUpdater)
	fraudService := fraud2.ProvideFraudService(mockChatServiceClient, atomicAdminConfig)
	orderAPI := order.ProvideOrderAPI(proxyOrderRepository, proxyAssignmentLogRepository, proxyDriverRepository, assignmentRepository, proxyAssignmentRejectionRepository, orderAPIConfig, proxyDriverTransactionRepository, proxyRatingRestaurantRepository, proxyCancelReasonRepository, proxyServiceAreaRepository, proxyCoinCashConversionRateRepository, driverService, banServiceImpl, pushNotifier, driverService, repEventBus, stubKafkaConnector, vosServiceImpl, deliveryDelivery, vosConfig, v11, shiftServices, awsServiceImpl, autoAssignOrderDistributor, cancellerImpl, tripService, fraudService, featureflagService)
	orderPortalAPI := order.ProvideOrderPortalAPI(orderAPIConfig, foodProviderImpl, proxyOrderRepository, proxyDriverRepository, redisLocker, tripService, driverService, unAcknowledgeReassignReposity)
	orderAssignerHandler := orderassigner.ProvideOrderAssignerHandler(atomicAutoAcceptConfig, atomicAutoAssignDbConfig, v12, proxyAssignmentLogRepository, illegalDriverRepository, redisLocker, proxyDriverRepository, proxyOrderRepository, proxyServiceAreaRepository, rainSituationServiceImpl, onTopFareService, proxyDriverLocationRepository, stubMapService, orderAPIConfig, assignmentRepository, statisticServiceImpl, stubKafkaConnector, driverService, pushNotifier, txnHelper, prometheusMeter, locationManagerImpl, featureflagService, distributionService, dispatcherDispatcher, distributionLogManager, servicePreferenceService, predictionService, acceptor, workerContext, metricsRegistryImpl)
	smsConfig := sms.ProvideSMSConfig()
	smsSMS := sms.ProvideSMS(smsConfig, client)
	otpapi := otp.ProvideOTPAPI(smsSMS, proxyOTPSessionRepo, lineClient)
	partnerAuthConfig := partnerAuth.ProvideConfig()
	citiAuthenticationService := partnerAuth.ProvideCitiAuthenticationService(partnerAuthConfig)
	atomicCitiConfig := partners.ProvideCitiApiConfig(dbConfigUpdater)
	citiAPI := partners.ProvideCitiAPI(pushNotifier, proxyDriverRepository, driverTransactionService, transactionService, atomicCitiConfig)
	partnersConfig := partners.ProvideUobApiConfig()
	uobAPI := partners.ProvideUobAPI(partnersConfig, pushNotifier, proxyDriverRepository, driverTransactionService, transactionService)
	approvalDataStore := persistence.ProvideApprovalDataStore(conn)
	proxyApprovalRepository := persistence.ProvideDataStoreApprovalRepository(approvalDataStore, prometheusMeter)
	approvalService := payment.ProvideApprovalService(proxyApprovalRepository, proxyDriverTransactionRepository, proxyTransactionRepository, proxyDriverRepository, driverTransactionServiceV2, txnHelper)
	approvalCreator := payment.ProvideApprovalCreator(proxyApprovalRepository, proxyDriverRepository, proxyDriverTransactionRepository, proxyTransactionRepository, paymentConfig, proxyOrderRepository)
	approvalAPI := payment.ProvideApprovalAPI(proxyApprovalRepository, approvalService, approvalCreator, proxyDriverTransactionRepository, proxyTransactionRepository, paymentConfig)
	driverTransactionAPI := payment.ProvideDriverTransactionAPI(proxyDriverTransactionRepository, proxyBanHistoryRepository, driverTransactionService, paymentConfig, banServiceImpl, vosServiceImpl)
	groupTransactionAPI := payment.ProvideGroupTransactionAPI(proxyGroupTransactionRepository, proxyDriverTransactionRepository, proxyTransactionRepository, proxyOrderRepository, proxyTransactionSchemeRepository, paymentConfig)
	emailConfig := email.ProvideEmailConfig()
	emailService := email.ProvideEmailService(emailConfig)
	uobclientConfig := uobclient.ProvideUobConfig()
	uob, cleanup25 := uobclient.ProvideUobClient(uobclientConfig)
	withdrawalTransactionResultsDataStoreDataStore := persistence.ProvideWithdrawalTransactionResultsDataStore(conn)
	proxyWithdrawalTransactionResultsRepository := persistence.ProvideWithdrawalTransactionResultsRepository(withdrawalTransactionResultsDataStoreDataStore, prometheusMeter)
	paymentServiceImpl := payment.ProvidePaymentService(proxyTransactionRepository, transactionService, emailService, paymentConfig, uob, vosServiceImpl, proxyWithdrawalTransactionResultsRepository)
	uobPayoutMetric := payment.ProvideUOBPayoutMetric(transactionDataStore, paymentConfig, featureflagService)
	transactionAPI := payment.ProvideTransactionAPI(proxyTransactionRepository, proxyDriverTransactionRepository, transactionService, emailService, proxyDriverRepository, driverTransactionService, pushNotifier, paymentConfig, vosServiceImpl, uob, paymentServiceImpl)
	transactionSchemeAPI := payment.ProvideTransactionSchemeAPI(proxyTransactionSchemeRepository, proxyAuditLogRepository)
	phonesConfig := phones.ProvidePhonesConfig()
	phonesAPI := phones.ProvidePhoneAPI(proxyWhitelistPhoneRepository, phonesConfig)
	productConfig := product.ProvideProductConfig()
	productDataStore := persistence.ProvideProductDataStore(conn)
	proxyProductRepository := persistence.ProvideProductRepository(productDataStore, prometheusMeter, stubGRPCProductService)
	productAPI := product.ProvideProductAPI(productConfig, proxyProductRepository, proxyProductGroupRepository, txnHelper)
	provinceAdminAPI := province.ProvideProvinceAdminAPI(proxyProvinceRepository)
	provinceAPI := province.ProvideProvinceAPI(proxyProvinceRepository)
	rain_situationConfig := rain_situation.ProvideConfig()
	rainSituationAdminAPI := rain_situation.ProvideRainSituationAdminAPI(rain_situationConfig, proxyRainSituationRepository, rainSituationServiceImpl, txnHelper, slackStub, atomicRainSituationConfig, globalConfig)
	rainSituationAPI := rain_situation.ProvideRainSituationAPI(rain_situationConfig, rainSituationServiceImpl, proxyDriverRepository, stubPolygonApi, proxyRainSituationRepository, proxyAuditLogRepository, slackStub, atomicRainSituationConfig, globalConfig, stubMapService)
	ratingOptionDataStore := persistence.ProvideRatingOptionDataStore(conn)
	proxyRatingOptionRepository := persistence.ProvideRatingOptionRepository(ratingOptionDataStore, prometheusMeter)
	ratingAdminAPI := rating.ProvideRatingAdminAPI(proxyRatingOptionRepository)
	ratingAPI := rating.ProvideRatingOptionAPI(proxyRatingOptionRepository, featureflagService)
	regionAdminAPI := region.ProvideRegionAdminAPI(proxyRegionRepository)
	v13 := region.ProvideAtomicRegionConfig(dbConfigUpdater)
	regionAPI := region.ProvideRegionAPI(proxyProvinceRepository, v13)
	rewardConfig := reward.ProvideConfig()
	rewardAdminAPI := reward.ProvideRewardAdminAPI(proxyRewardBalanceRepository, proxyDailyRewardRepository, rewardService, proxyCoinCashConversionRateRepository, proxyRewardTransactionRepository, rewardConfig)
	shiftCancelDataStore := persistence.ProvideShiftCancelDataStore(conn)
	proxyShiftCancelRepository := persistence.ProvideShiftCancelRepository(shiftCancelDataStore, prometheusMeter)
	atomicShiftConfig := shift.ProvideShiftCancelReasonConfig(dbConfigUpdater)
	shiftConfig := shift.ProvideShiftConfig(atomicShiftConfig)
	shiftAPI := shift.ProvideShiftAPI(proxyShiftRepository, proxyDriverRepository, proxyShiftCancelRepository, txnHelper, shiftConfig, proxyServiceAreaRepository, attendances)
	clientAreaAPI := srvarea.ProvideClientAreaAPI(proxyClientAreaRepository, proxyServiceAreaRepository)
	serviceAreaSettingAPI := srvarea.ProvideServiceAreaSettingAPI(proxyServiceAreaRepository, proxyRegionRepository, proxyAuditLogRepository, shiftServices, txnHelper, slackStub, serviceAreaConfig, globalConfig, proxyZoneRepository, serviceAreaService)
	summaryOfChangeDataStore := persistence.ProvideSummaryOfChangeDataStore(conn)
	proxySummaryOfChangeRepository := persistence.ProvideSummaryOfChangeRepository(summaryOfChangeDataStore, redisClient, caches, prometheusMeter)
	summaryOfChangeAPI := summaryofchange.ProvideSummaryOfChangeAPI(proxySummaryOfChangeRepository, proxyAuditLogRepository)
	throttledOrderService := service.ProvideThrottledOrderService(throttledOrderRepository)
	throttledOrderAPI := throttled_order.ProvideThrottledOrderAPI(throttledOrderService)
	throttledDispatchDetailAdminAPI := throttleddispatchdetail.ProvideThrottledDispatchDetailAdminAPI(throttledDispatchDetailRepository, proxyAuditLogRepository, proxyZoneRepository)
	tripAPI := trip.ProvideTripAPI(tripService, deliveryDelivery, orderAPIConfig, proxyDriverRepository, proxyTripRepository, proxyOrderRepository, proxyAuditLogRepository, proxyServiceAreaRepository, proxyCoinCashConversionRateRepository, proxyAssignmentLogRepository, assignmentRepository, proxyDriverTransactionRepository, proxyRatingRestaurantRepository, featureflagService)
	withholdingtaxcertificateConfig := withholdingtaxcertificate.ProvideConfig()
	withholdingTaxCertificateAPI := withholdingtaxcertificate.ProvideWithholdingTaxCertificateAPI(proxyWithholdingTaxCertificateRepository, vosServiceImpl, withholdingtaxcertificateConfig)
	zoneServiceImpl := service.ProvideZoneServiceImpl(proxyZoneRepository)
	zoneAdminAPI := zone.ProvideZoneAdminAPI(zoneServiceImpl)
	asynqClient := asynqclient.ProvideAsynqClient(redisConfig)
	redisTokenStorage := auth.ProvideRedisTokenStore(redisClient)
	atomicConfigLocator := configlocator.ProvideAtomicConfigLocator(atomicDriverLocationConfig)
	bcpTestClientStub := bcp.ProvideBCPTestClientStub()
	bcpConfig := bcp.ProvideBCPTestConfig(t, bcpTestClientStub)
	bcpClient, err := bcp.ProvideBCPClient(bcpConfig)
	if err != nil {
		cleanup25()
		cleanup24()
		cleanup23()
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	inet_clientConfig := inet_client.ProvideINetClientConfig()
	iNetClient := inet_client.ProvideINetClient(inet_clientConfig)
	verdaKafkaCfg := kafcclient.ProvideVerdaKafkaCfg()
	distributionKafkaConsumerConfig := kafcclientdistribution.ProvideDistributionKafkaConsumerConfig()
	distributionKafcConsumerConfig := kafcclientdistribution.ProvideDistributionKafcConsumerConfig()
	distributionKafkaConsumer, cleanup26 := kafcclientdistribution.ProvideDistributionKafkaConsumer(distributionKafkaConsumerConfig, distributionKafcConsumerConfig, dispatcherDispatcher)
	monitorConn, cleanup27 := di.ProvideDBMonitorConnectionForTest(initTestDBConn)
	mongoProfilerConfig := config.ProvideMongoProfilerConfig()
	v14 := config.ProvideAtomicMongoProfilerConfig(dbConfigUpdater)
	mongoProfiler, cleanup28 := mongoprofiler.ProvideMongoProfiler(monitorConn, vosServiceImpl, mongoProfilerConfig, v14, prometheusMeter)
	kafkaConsumerConfig := mq.ProvideKafkaConsumerConfig()
	kafkaConsumer := mq.ProvideKafkaConsumer(kafkaConsumerConfig)
	config2 := polygon2.ProvidePolygonConfig()
	httpPolygon := polygon2.ProvideHttpPolygon(config2, client)
	riderLevelClient := riderlevel.ProvideRiderLevelConnector(stubGRPCFeaturePlatformClient, configRiderLevel)
	paymentAPI := &payment.API{
		TransactionAPI:       transactionAPI,
		TransactionSchemeAPI: transactionSchemeAPI,
		ApprovalAPI:          approvalAPI,
		DriverTransactionAPI: driverTransactionAPI,
		GroupTransactionAPI:  groupTransactionAPI,
	}
	apiSet := &router.APISet{
		OTPAPI:                          otpapi,
		DeliveryPortalAPI:               deliveryPortalAPI,
		OrderPortalAPI:                  orderPortalAPI,
		OrderAPI:                        orderAPI,
		TripAPI:                         tripAPI,
		OrderAdminAPI:                   orderAdminAPI,
		DriverAdminAPI:                  driverAdminAPI,
		PhonesAPI:                       phonesAPI,
		DriverRegisAPI:                  registrationAPI,
		CitiAPI:                         citiAPI,
		UobAPI:                          uobAPI,
		SettingIncentiveAPI:             incentiveAPI,
		RegionAPI:                       regionAPI,
		RegionAdminAPI:                  regionAdminAPI,
		RegistrationAdminAPI:            registrationAdminAPI,
		DeliveryFee:                     deliveryFeeAPI,
		PaymentAPI:                      paymentAPI,
		SettingServiceAreaAPI:           serviceAreaSettingAPI,
		ClientAreaAPI:                   clientAreaAPI,
		FraudAPI:                        fraudAPI,
		InternalAPI:                     internalAPI,
		HeatMapAPI:                      api,
		ProvinceAPI:                     provinceAPI,
		ProvinceAdminAPI:                provinceAdminAPI,
		EventAPI:                        eventAPI,
		RatingAPI:                       ratingAPI,
		RatingAdminAPI:                  ratingAdminAPI,
		PdpaApi:                         pdpaApi,
		DbConfigAdminAPI:                adminAPI,
		ShiftAPI:                        shiftAPI,
		IncentiveSourceAPI:              incentiveSourceAPI,
		WithholdingTaxCertificateApi:    withholdingTaxCertificateAPI,
		ProductApi:                      productAPI,
		DriverInsuranceApi:              insuranceAPI,
		DriverDocsAPI:                   document,
		DedicatedZoneAPI:                dedicatedZoneAPI,
		ZoneAdminAPI:                    zoneAdminAPI,
		ThrottledDispatchDetailAdminAPI: throttledDispatchDetailAdminAPI,
		RewardAdminAPI:                  rewardAdminAPI,
		RainSituationAPI:                rainSituationAPI,
		RainSituationAdminAPI:           rainSituationAdminAPI,
		SummaryOfChangeAPI:              summaryOfChangeAPI,
		FeatureFlagConfigAPI:            featureFlagConfigAPI,
		AssignmentBenchmarkAPI:          assignmentBenchmarkAPI,
		OrderAssignerHandler:            orderAssignerHandler,
		SalesforceAdminAPI:              driverSalesforceAPI,
		ThrottledOrderAPI:               throttledOrderAPI,
	}
	apiSpecConfig := config.ProvideAPISpecConfig()
	routerConfig := config.ProvideRouterConfig()
	engine := di.ProvideGinEngineRouter(apiSet, builder, apiSpecConfig, routerConfig, featureflagService)
	initToggle := di.ProvideInitToggle(dbConfigUpdater, prometheusMeter)
	proxyMinimalOrderRepository := persistence.ProvideMongoMinimalOrderRepository(orderDataStore, prometheusMeter)
	bcpStatusTransitionerService := bcp2.ProvideBCPStatusTransitionerService(bcpOrderRepository, proxyMinimalOrderRepository, bcpClient)
	qrIncidentOrderServiceImpl := service.ProvideQRIncidentOrderService(proxyOrderRepository, pushNotifier)
	supplyPositioningRecommenderService, cleanup29 := service.ProvideSupplyPositioningRecommenderService(atomicSupplyPositioningConfig, stubGRPCRecommendationService, proxyDriverRepository, proxyServiceAreaRepository, featureflagService, pushNotifier, prometheusMeter, asynqClient)
	termAndConditionDataStore := persistence.ProvideTermAndConditionDataStore(conn)
	proxyTermAndConditionRepository := persistence.ProvideMongoTermAndConditionRepository(termAndConditionDataStore, prometheusMeter)
	termAndConditionServiceImpl := service.ProvideTermAndConditionServiceImpl(proxyTermAndConditionRepository)
	tiersAndBenefitsService := service.ProvideTiersAndBenefitsService(proxyDriverRepository)
	service2 := transaction.ProvideTransactionServiceV2(proxyPendingTransactionRepository)
	driverStatRedisConfig := providers.ProvideDriverStatRedisTestConfig(redisConfig)
	driverStatRedisClient, cleanup30 := event2.ProvideDriverStatRedisClient(driverStatRedisConfig)
	driverMarker := event2.ProvideDriverMarker()
	driverStatisticConfig := event2.ProvideDriverStatisticConfig()
	duplicatePreventionStoreInterface := event2.ProvideDuplicatePreventionStore(redisClient, driverStatisticConfig)
	v15 := event2.ProvideAtomicDriverStatisticConfig(dbConfigUpdater)
	exceedARDetector := event2.ProvideExceedArDetector()
	driverOrderInfoProcessor := event2.ProvideDriverOrderInfoProcessor(driverMarker, duplicatePreventionStoreInterface, driverStatRedisClient, proxyDriverRepository, proxyIncentiveRepository, proxyIncentiveProgressRepository, kafkaConsumerConfig, driverOrderInfoAPI, v15, kafkaConsumer, featureflagService, exceedARDetector)
	gokaStream, cleanup31 := event2.ProvideGokaStream(driverOrderInfoProcessor)
	defaultUnleashAdmin := featureflag.ProvideStubUnleashAdmin()
	defaultLMWNUnleasher := featureflag.ProvideStubLMWNUnleasher()
	mockCoinPlanGRPCClient := coinplan.ProvideStubCoinPlanGRPCClient(legacyController)
	dapfeastConfig := dapfeast.ProvideConfig()
	stubGRPCFeatureV2ServiceClient := dapfeast.ProvideStubGRPCFeatureV2ServiceClient(dapfeastConfig)
	mockEGSServiceClient := egs.ProvideStubGRPCEGSService(legacyController)
	egsbranchConfig := egsbranch.ProvideConfig()
	stubGRPCEGSBranchService := egsbranch.ProvideStubGRPCEGSBranchService(egsbranchConfig)
	egsorderConfig := egsorder.ProvideConfig()
	stubGRPCEGSOrderService := egsorder.ProvideStubGRPCEGSOrderService(egsorderConfig)
	featureplatformConfig := featureplatform.ProvideConfig()
	featureplatformClient, cleanup32 := featureplatform.ProvideGRPCFeaturePlatformClient(featureplatformConfig)
	mockMarketplaceServiceClient := marketplace.ProvideStubGRPCMarketplaceService(legacyController)
	driverAssignmentLogsRevisionsDataStore := persistence.ProvideDriverAssignmentLogsRevisionsDataStore(conn)
	attendanceLogHistoryDataStore := persistence.ProvideAttendanceLogHistoryDataStore(conn)
	proxyAttendanceLogHistoryRepository := persistence.ProvideDataStoreAttendanceLogHistoryRepository(attendanceLogHistoryDataStore, prometheusMeter)
	bulkProcessInfoDataStore := persistence.ProvideBulkProcessInfoDataStore(conn)
	proxyBulkProcessInfoRepository := persistence.ProvideDataStoreBulkProcessInfoRepository(bulkProcessInfoDataStore, prometheusMeter)
	counterDataStore := persistence.ProvideCounterDataStore(conn)
	proxyCounterRepository := persistence.ProvideCounterRepository(counterDataStore, prometheusMeter)
	proxyDriverLastUpdateLocationTrackerRepository := persistence.ProvideRedisLastUpdateLocationTrackerRepository(redisClient, prometheusMeter)
	requestUpdateProfileDataStore := persistence.ProvideRequestUpdateProfileDataStore(conn)
	proxyRequestUpdateProfileRepository := persistence.ProvideRequestUpdateProfileRepository(requestUpdateProfileDataStore, prometheusMeter)
	requestUpdateProfileSectionDataStore := persistence.ProvideRequestUpdateProfileSectionDataStore(conn)
	proxyRequestUpdateProfileSectionRepository := persistence.ProvideRequestUpdateProfileSectionRepository(requestUpdateProfileSectionDataStore, prometheusMeter)
	topupCreditReportDataStore := persistence.ProvideTopupCreditReportDataStore(conn)
	proxyTopupCreditReportRepository := persistence.ProvideTopupCreditReportRepository(topupCreditReportDataStore, prometheusMeter)
	lazyRepEventBus, cleanup33 := infrastructure.ProvideLazyRepEventBus(repConfig, prometheusMeter)
	messagesConfig := messages.ProvideConfig()
	lineBotNotificationServiceWorker, cleanup34 := messages.ProvideLineBotNotificationServiceWorker(messagesConfig, client)
	providerRegistry := serviceprovider.ProvideRegistry()
	serviceSelector := serviceprovider.ProvideServiceSelectorAPI(proxyOrderRepository, providerRegistry)
	driverRegistrationTestData := testdata.ProvideDriverRegistrationTestData(driverRegistrationDataStore)
	otpSessionTestData := testdata.ProvideOTPSessionTestData(proxyOTPSessionRepo)
	provincesTestData := testdata.ProvideProvincesTestData(provinceDataStore)
	ratingRestaurantTestData := testdata.ProvideRatingRestaurantTestData(ratingRestaurantDataStore)
	whitelistPhonesTestData := testdata.ProvideWhitelistPhonesTestData(whitelistPhoneDataStore)
	locator := &di.Locator{
		AssignmentBenchmarkAPI:                   assignmentBenchmarkAPI,
		DbConfigAdminAPI:                         adminAPI,
		DedicatedZoneAPI:                         dedicatedZoneAPI,
		DeliveryFeeAPI:                           deliveryFeeAPI,
		Document:                                 document,
		DriverAdminAPI:                           driverAdminAPI,
		DriverNotifyAPI:                          driverNotifyAPI,
		DriverSalesforceAPI:                      driverSalesforceAPI,
		DriverStatusAPI:                          driverStatusAPI,
		PdpaApi:                                  pdpaApi,
		RegistrationAdminAPI:                     registrationAdminAPI,
		RegistrationAPI:                          registrationAPI,
		InsuranceAPI:                             insuranceAPI,
		AutoBanCompleteOrderTooOftenCfg:          v6,
		AutoBanCompleteOrderTooFarCfg:            v7,
		DriverOrderInfoAPI:                       driverOrderInfoAPI,
		EventAPI:                                 eventAPI,
		FeatureFlagConfigAPI:                     featureFlagConfigAPI,
		FraudAPI:                                 fraudAPI,
		HeatMapAPI:                               api,
		IncentiveAPI:                             incentiveAPI,
		IncentiveProgressDataStore:               incentiveProgressDataStore,
		DataStoreIncentiveProgressRepository:     proxyIncentiveProgressRepository,
		IncentiveDataStore:                       incentiveDataStore,
		DataStoreIncentiveRepository:             proxyIncentiveRepository,
		IncentiveSourceAPI:                       incentiveSourceAPI,
		InternalAPI:                              internalAPI,
		MiddlewareBuilder:                        builder,
		AcceptorDeps:                             acceptorDeps,
		Acceptor:                                 acceptor,
		AdminAPI:                                 orderAdminAPI,
		DeliveryAPIPortal:                        deliveryPortalAPI,
		Canceller:                                cancellerImpl,
		AssigningStateManager:                    assigningStateManager,
		AutoAssignOrderDistributor:               autoAssignOrderDistributor,
		AutoAssignOrderDistributorDeps:           autoAssignOrderDistributorDeps,
		ConfigValidator:                          configValidator,
		MemLocker:                                memLocker,
		OrderAPI:                                 orderAPI,
		OrderPortalAPI:                           orderPortalAPI,
		OrderAssignerHandler:                     orderAssignerHandler,
		FoodProvider:                             foodProviderImpl,
		ProviderDeps:                             providerDeps,
		OTPAPI:                                   otpapi,
		CitiAuthenticationService:                citiAuthenticationService,
		CitiAPI:                                  citiAPI,
		UobAPI:                                   uobAPI,
		ApprovalAPI:                              approvalAPI,
		ApprovalCreator:                          approvalCreator,
		ApprovalService:                          approvalService,
		DriverTransactionAPI:                     driverTransactionAPI,
		DriverTransactionService:                 driverTransactionService,
		GroupTransactionAPI:                      groupTransactionAPI,
		PaymentService:                           paymentServiceImpl,
		UOBPayoutMetric:                          uobPayoutMetric,
		TransactionAPI:                           transactionAPI,
		TransactionSchemeAPI:                     transactionSchemeAPI,
		TransactionService:                       transactionService,
		PhoneAPI:                                 phonesAPI,
		ProductAPI:                               productAPI,
		ProvinceAdminAPI:                         provinceAdminAPI,
		ProvinceAPI:                              provinceAPI,
		RainSituationAdminAPI:                    rainSituationAdminAPI,
		RainSituationAPI:                         rainSituationAPI,
		RatingAdminAPI:                           ratingAdminAPI,
		RatingOptionAPI:                          ratingAPI,
		RegionAdminAPI:                           regionAdminAPI,
		RegionAPI:                                regionAPI,
		RewardAdminAPI:                           rewardAdminAPI,
		ShiftAPI:                                 shiftAPI,
		ClientAreaAPI:                            clientAreaAPI,
		ServiceAreaSettingAPI:                    serviceAreaSettingAPI,
		SummaryOfChangeAPI:                       summaryOfChangeAPI,
		ThrottledOrderAPI:                        throttledOrderAPI,
		ThrottledDispatchDetailAdminAPI:          throttledDispatchDetailAdminAPI,
		TripAPI:                                  tripAPI,
		WithholdingTaxCertificateAPI:             withholdingTaxCertificateAPI,
		ZoneAdminAPI:                             zoneAdminAPI,
		AsynqClient:                              asynqClient,
		RedisTokenStore:                          redisTokenStorage,
		RekognitionClient:                        rekognitionImpl,
		ConfigUpdater:                            dbConfigUpdater,
		DBConfigDataStore:                        dbConfigDataStore,
		DBConfigRepository:                       proxyDBConfigRepository,
		AtomicConfigLocator:                      atomicConfigLocator,
		BCPClient:                                bcpClient,
		BCPTestClientStub:                        bcpTestClientStub,
		Cache:                                    cacheCache,
		Delivery:                                 deliveryDelivery,
		StubDeliveryFleetApi:                     stubDeliveryFleetApi,
		DriverServiceDispatcher:                  dispatcherDispatcher,
		DistributionExperimentPlatformClient:     distributionExperimentPlatformClient,
		FleetAreaClient:                          fleetAreaClient,
		FleetOrderClient:                         fleetOrderClient,
		FraudAdvisorServiceImpl:                  fraudAdvisorServiceImpl,
		HeatMapDemand:                            heatMapDemand,
		INetClient:                               iNetClient,
		VerdaKafkaCfg:                            verdaKafkaCfg,
		DistributionKafkaConsumer:                distributionKafkaConsumer,
		SecureIMFKafkaSyncProducerClientForTest:  secureIMFKafkaSyncProducerStubClient,
		IMFKafkaProducerClientForTest:            kafkaProducerStubClient,
		SecureIMFKafkaProducerClientForTest:      secureKafkaProducerStubClient,
		LINEClient:                               lineinternalLINEClient,
		LINEInternalClient:                       lineinternalClient,
		RedisLocker:                              redisLocker,
		ExperimentalMapServiceClient:             experimentalMapService,
		StubMapService:                           stubMapService,
		FeaturePlatformService:                   matchrateService,
		MongoProfiler:                            mongoProfiler,
		MongoTxnHelper:                           txnHelper,
		KafkaConsumer:                            kafkaConsumer,
		HttpPolygon:                              httpPolygon,
		Prediction:                               predictionPrediction,
		RiderLevelConnector:                      riderLevelClient,
		StubRiderLevelConnector:                  stubRiderLevelConnector,
		Slack:                                    slackStub,
		SlackStub:                                slackStub,
		SMS:                                      smsSMS,
		UobClient:                                uob,
		UWTErrorService:                          stubUWTErrorService,
		StubUWTErrorService:                      stubUWTErrorService,
		RedisConn:                                redisConn,
		Redis:                                    redisClient,
		SecondaryDBConnection:                    secondaryDBConnection,
		DBConnectionForTest:                      conn,
		DBMonitorConnectionForTest:               monitorConn,
		GinEngineRouter:                          engine,
		InitToggle:                               initToggle,
		NewOTPSession:                            newOTPSession,
		TimeNow:                                  timeNowFunc,
		AccountingHubTransactionService:          accountingHubTransactionService,
		AreaServiceImpl:                          areaServiceImpl,
		AssignmentBenchmarkCfg:                   assignmentBenchmarkCfg,
		AssignmentBenchmarkService:               assignmentBenchmarkService,
		Attendances:                              attendances,
		AwsServiceImpl:                           awsServiceImpl,
		BanServiceImpl:                           banServiceImpl,
		BCPStatusTransitionerService:             bcpStatusTransitionerService,
		DeliveryFeeService:                       deliveryFeeService,
		DistributionLogEventService:              distributionLogEventService,
		DistributionLogManager:                   distributionLogManager,
		DistributionService:                      distributionService,
		DriverInsuranceService:                   driverInsuranceServiceImpl,
		DriverInsuranceCfg:                       driverInsuranceCfg,
		QRIncidentOrderService:                   qrIncidentOrderServiceImpl,
		DeviceManagerImpl:                        deviceManagerImpl,
		DriverService:                            driverService,
		DriverServiceTypeCapacityService:         driverServiceTypeCapacityService,
		DriverTransactionServiceV2:               driverTransactionServiceV2,
		DriverLocationEventServiceImpl:           driverUpdateLocationEventService,
		InstallmentOnTopTransactionProvider:      installmentOnTopTransactionProvider,
		OnTopSchemeTransactionProviderSelector:   onTopSchemeTransactionProviderSelector,
		Service:                                  formService,
		FraudService:                             fraudService,
		IncentiveSourceServiceImpl:               incentiveSourceServiceImpl,
		LocationManagerImpl:                      locationManagerImpl,
		MissionLogEventService:                   missionLogEventService,
		PushNotifier:                             pushNotifier,
		OnTopFareService:                         onTopFareService,
		OrderService:                             orderService,
		OrderDistributionEventManager:            orderDistributionEventManager,
		OrderDistributionEventService:            orderDistributionEventService,
		OrderHeartbeatService:                    orderHeartbeatServiceImpl,
		PendingTransactionService:                pendingtransactionService,
		PredictionService:                        predictionService,
		RainSituationService:                     rainSituationServiceImpl,
		RewardService:                            rewardService,
		ServiceAreaServiceImpl:                   serviceAreaService,
		ServiceOptInReminderService:              serviceOptInReminderService,
		ServicePreferenceKillSwitchService:       servicePreferenceKillSwitchService,
		ServicePreferenceService:                 servicePreferenceService,
		ShiftServices:                            shiftServices,
		StatisticServiceImpl:                     statisticServiceImpl,
		SupplyPositioningRecommenderService:      supplyPositioningRecommenderService,
		TermAndConditionServiceImpl:              termAndConditionServiceImpl,
		ThrottledOrderService:                    throttledOrderService,
		TiersAndBenefitsService:                  tiersAndBenefitsService,
		TransactionServiceV2:                     service2,
		TranslationServiceImpl:                   translationServiceImpl,
		TripServices:                             tripService,
		VOSServiceImpl:                           vosServiceImpl,
		ZoneServiceImpl:                          zoneServiceImpl,
		EmailService:                             emailService,
		DriverStatRedisClient:                    driverStatRedisClient,
		DriverOrderInfoProcessor:                 driverOrderInfoProcessor,
		DuplicatePreventionStore:                 duplicatePreventionStoreInterface,
		ExceedArDetector:                         exceedARDetector,
		GokaStream:                               gokaStream,
		StubUnleashAdmin:                         defaultUnleashAdmin,
		StubLMWNUnleasher:                        defaultLMWNUnleasher,
		FeatureFlagService:                       featureflagService,
		ChatServiceClient:                        mockChatServiceClient,
		StubChatGRPCService:                      mockChatServiceClient,
		CoinPlanGRPCClient:                       mockCoinPlanGRPCClient,
		StubCoinPlanGRPCClient:                   mockCoinPlanGRPCClient,
		GRPCFeatureV2ServiceClient:               stubGRPCFeatureV2ServiceClient,
		StubGRPCFeatureV2ServiceClient:           stubGRPCFeatureV2ServiceClient,
		GRPCDriverProvisionClient:                stubGRPCRecommendationService,
		StubGRPCRecommendationService:            stubGRPCRecommendationService,
		GRPCEGSServiceClient:                     mockEGSServiceClient,
		StubGRPCEGSService:                       mockEGSServiceClient,
		GRPCEGSBranchServiceClient:               stubGRPCEGSBranchService,
		StubGRPCEGSBranchService:                 stubGRPCEGSBranchService,
		GRPCEGSOrderServiceClient:                stubGRPCEGSOrderService,
		StubGRPCEGSOrderService:                  stubGRPCEGSOrderService,
		GRPCFeaturePlatformClient:                featureplatformClient,
		StubGPRCFeaturePlatformClient:            stubGRPCFeaturePlatformClient,
		FleetPoolGRPCClient:                      grpcClient,
		RiderSearchServiceClient:                 riderSearchServiceClient,
		GRPCFormServiceClient:                    mockFormServiceClient,
		StubGRPCFormService:                      mockFormServiceClient,
		GRPCPriorityGroupServiceClient:           stubGRPCPriorityGroupService,
		StubGRPCPriorityGroupService:             stubGRPCPriorityGroupService,
		GRPCProductServiceClient:                 stubGRPCProductService,
		StubGRPCProductService:                   stubGRPCProductService,
		GRPCMarketplaceServiceClient:             mockMarketplaceServiceClient,
		StubGRPCMarketplaceService:               mockMarketplaceServiceClient,
		GRPCUserPolygonServiceClient:             mockUserPolygonServiceClient,
		StubGRPCUserPolygonService:               mockUserPolygonServiceClient,
		PriceInterventionClient:                  priceInterventionClientStub,
		PriceInterventionServiceClientStub:       priceInterventionClientStub,
		RainServiceClient:                        mockRainServiceClient,
		RainServiceClientStub:                    mockRainServiceClient,
		TranslationGRPCClient:                    translationGRPCClient,
		TranslationServiceClient:                 translationServiceClient,
		GRPCUserServiceClient:                    mockUserServiceClient,
		StubGRPCUserService:                      mockUserServiceClient,
		FraudAdvisorHTTPClient:                   fraudAdvisorHTTPClient,
		DefaultClient:                            client,
		DalianClient:                             dalianClient,
		IncomeAggregateService:                   incomeAggregateServiceImpl,
		IncomeSummaryService:                     incomeSummaryServiceImpl,
		CoinConversionRateLocalCache:             coinConversionRateLocalCache,
		CoinConversionRateRedisCache:             coinConversionRateRedisCache,
		CoinConversionRateMinimalRedisCache:      coinConversionRateMinimalRedisCache,
		InfraLocalTaskExecutor:                   localTaskExecutor,
		StubKafkaConnector:                       stubKafkaConnector,
		DataStoreApprovalRepository:              proxyApprovalRepository,
		ApprovalDataStore:                        approvalDataStore,
		AssignmentBenchmarkDataStore:             assignmentBenchmarkDataStore,
		AssignmentBenchmarkRepository:            assignmentBenchmarkRepository,
		MongoAssignmentLogRepository:             proxyAssignmentLogRepository,
		DriverAssignmentLogsDataStore:            driverAssignmentLogsDataStore,
		DriverAssignmentLogsRevisionsDataStore:   driverAssignmentLogsRevisionsDataStore,
		DataStoreAssignmentRejectionRepository:   proxyAssignmentRejectionRepository,
		AssignmentRejectionDataStore:             assignmentRejectionDataStore,
		AssignmentDataStore:                      assignmentDataStore,
		AssignmentRepository:                     assignmentRepository,
		DataStoreAttendanceLogHistoryRepository:  proxyAttendanceLogHistoryRepository,
		AttendanceLogHistoryDataStore:            attendanceLogHistoryDataStore,
		AuditLogRepository:                       proxyAuditLogRepository,
		AuditLogDataStore:                        dataStoreInterface,
		BanHistoryServiceImpl:                    proxyBanHistoryRepository,
		BanHistoriesDataStore:                    banHistoriesDataStore,
		BanMetadataDataStore:                     mongoBanMetadataDataStore,
		BanMetadataRepository:                    proxyBanMetadataRepository,
		BCPOrderDataStore:                        bcpOrderDataStore,
		BCPOrderRepository:                       bcpOrderRepository,
		DataStoreBulkProcessInfoRepository:       proxyBulkProcessInfoRepository,
		BulkProcessInfoDataStore:                 bulkProcessInfoDataStore,
		DataStoreCancelReasonRepository:          proxyCancelReasonRepository,
		CancelReasonDataStore:                    cancelReasonDataStore,
		MongoClientAreaRepository:                proxyClientAreaRepository,
		ClientAreaDataStore:                      clientAreaDataStore,
		CoinCashConversionRateRepository:         proxyCoinCashConversionRateRepository,
		CoinCashConversionRateDataStore:          mongoCoinCashConversionRateDataStore,
		ConsolidatedInsuranceRepository:          proxyConsolidatedInsuranceRepository,
		ConsolidatedInsuranceDataStore:           consolidatedInsuranceDataStore,
		CookingTimeDelayRepository:               cookingTimeDelayRepository,
		CounterDataStore:                         counterDataStore,
		CounterRepository:                        proxyCounterRepository,
		MongoDailyRewardRepository:               proxyDailyRewardRepository,
		MongoDailyRewardDataStore:                mongoDailyRewardDataStore,
		DedicatedZoneDataStore:                   dedicatedZoneDataStore,
		DedicatedZoneRepository:                  dedicatedZoneRepository,
		DeferredOrderDataStore:                   deferredOrderDataStore,
		DeferredOrderRepository:                  deferredOrderRepository,
		DeliveryFeeSettingDataStore:              deliveryFeeSettingDataStore,
		DeliveryFeeSettingMongo:                  proxyDeliveryFeeSettingRepository,
		RedisDriverActiveTimeRepository:          proxyDriverActiveTimeRepository,
		DriverDocumentRepository:                 proxyDriverDocumentRepository,
		DriverDocumentDatastore:                  driverDocumentDatastore,
		InsuranceRepository:                      proxyDriverInsuranceRepository,
		DriverInsuranceDataStore:                 driverInsuranceDataStore,
		RedisLastUpdateLocationTrackerRepository: proxyDriverLastUpdateLocationTrackerRepository,
		LocationRedisClient:                      locationRedisClient,
		DriverLocationRepositoryForTest:          proxyDriverLocationRepository,
		DriverOrderInfoRepository:                proxyDriverOrderInfoRepository,
		DriverOrderInfoDataStore:                 driverOrderInfoDataStore,
		DriverRegistrationRepository:             proxyDriverRegistrationRepository,
		DriverRegistrationDataStore:              driverRegistrationDataStore,
		DriverRepository:                         proxyDriverRepository,
		DriversDataStore:                         driversDataStore,
		DataStoreDriverStatisticRepository:       proxyDriverStatisticRepository,
		DriverStatisticDataStore:                 driverStatisticDataStore,
		DataStoreDriverTransactionRepository:     proxyDriverTransactionRepository,
		DriverTransactionDataStore:               driverTransactionDataStore,
		MongoGroupTransactionRepository:          proxyGroupTransactionRepository,
		GroupTransactionDataStore:                groupTransactionDataStore,
		HeatMapDataStore:                         heatMapDataStore,
		HeatMapRepository:                        proxyHeatMapRepository,
		IllegalDriverRepository:                  illegalDriverRepository,
		IncomeDailySummaryStore:                  incomeDailySummaryStore,
		IncomeDailySummaryRepository:             proxyIncomeDailySummaryRepository,
		InternalCancelReasonDataStore:            internalCancelReasonDataStore,
		InternalCancelReasonRepository:           proxyInternalCancelReasonRepository,
		LINEStatelessTokenCacheRepository:        lineStatelessTokenCacheRepository,
		MatchingRateHeatMapDataStore:             heatMapMatchingRateDataStore,
		MatchingRateHeatMapRepository:            proxyMatchingRateHeatMapRepository,
		MongoMinimalOrderRepository:              proxyMinimalOrderRepository,
		OnTopFareDataStore:                       onTopFareDataStore,
		OnTopFareRepository:                      proxyOnTopFareRepository,
		OrderHearthBeatRepository:                proxyOrderHeartbeatRepository,
		MongoOrderRepository:                     proxyOrderRepository,
		OrderRevisionDataStore:                   orderRevisionDataStore,
		OrderDataStore:                           orderDataStore,
		OTPSessionRepo:                           proxyOTPSessionRepo,
		PdpaRepository:                           proxyPdpaRepository,
		PdpaDataStore:                            pdpaDataStore,
		DataStorePendingTransactionRepository:    proxyPendingTransactionRepository,
		PendingTransactionDataStore:              pendingTransactionDataStore,
		ProductGroupRepository:                   proxyProductGroupRepository,
		ProductGroupDataStore:                    productGroupDataStore,
		ProductRepository:                        proxyProductRepository,
		ProductDataStore:                         productDataStore,
		ProvinceRepository:                       proxyProvinceRepository,
		ProvinceDataStore:                        provinceDataStore,
		QuestionConfigs:                          questionsConfig,
		FileQuestionRepository:                   proxyQuestionRepository,
		MongoQuoteRepository:                     proxyQuoteRepository,
		QuoteDataStore:                           quoteDataStore,
		RainSituationDataStore:                   rainSituationDataStore,
		RainSituationRepository:                  proxyRainSituationRepository,
		RatingOptionRepository:                   proxyRatingOptionRepository,
		RatingOptionDataStore:                    ratingOptionDataStore,
		RatingRestaurantRepository:               proxyRatingRestaurantRepository,
		RatingRestaurantDataStore:                ratingRestaurantDataStore,
		RegionRepository:                         proxyRegionRepository,
		RequestUpdateProfileRepository:           proxyRequestUpdateProfileRepository,
		RequestUpdateProfileDataStore:            requestUpdateProfileDataStore,
		RequestUpdateProfileSectionRepository:    proxyRequestUpdateProfileSectionRepository,
		RequestUpdateProfileSectionDataStore:     requestUpdateProfileSectionDataStore,
		RewardBalanceRepository:                  proxyRewardBalanceRepository,
		RewardBalanceDataStore:                   mongoRewardBalanceDataStore,
		MongoRewardTransactionRepository:         proxyRewardTransactionRepository,
		MongoRewardTransactionDataStore:          mongoRewardTransactionDataStore,
		MongoServiceAreaRepository:               proxyServiceAreaRepository,
		ServiceAreaDataStore:                     serviceAreaDataStore,
		OptInReminderRepository:                  serviceOptInReminderRepository,
		SettingDeliveryFeePriceSchemesDataStore:  settingDeliveryFeePriceSchemesDataStore,
		SettingDeliveryFeePriceSchemesRepository: proxySettingDeliveryFeePriceSchemesRepository,
		ShiftCancelRepository:                    proxyShiftCancelRepository,
		ShiftCancelDataStore:                     shiftCancelDataStore,
		ShiftRepository:                          proxyShiftRepository,
		ShiftDataStore:                           shiftDataStore,
		SummaryOfChangeRepository:                proxySummaryOfChangeRepository,
		SummaryOfChangeDataStore:                 summaryOfChangeDataStore,
		MongoTermAndConditionRepository:          proxyTermAndConditionRepository,
		TermAndConditionDataStore:                termAndConditionDataStore,
		ThrottledDispatchDetailDataStore:         throttledDispatchDetailDataStore,
		ThrottledDispatchDetailRepository:        throttledDispatchDetailRepository,
		ThrottledOrderDataStore:                  throttledOrderDataStore,
		ThrottledOrderRepository:                 throttledOrderRepository,
		TopupCreditReportDataStore:               topupCreditReportDataStore,
		TopupCreditReportRepository:              proxyTopupCreditReportRepository,
		MongoTransactionFraudScoreRepository:     proxyTransactionFraudScoreRepository,
		TransactionFraudScoreDataStore:           transactionFraudScoreDataStore,
		DataStoreTransactionRepository:           proxyTransactionRepository,
		TransactionDataStore:                     transactionDataStore,
		MongoTransactionSchemeRepository:         proxyTransactionSchemeRepository,
		TransactionSchemeDataStore:               transactionSchemeDataStore,
		TripDataStore:                            tripDataStore,
		TripRepository:                           proxyTripRepository,
		UnAcknowledgeReassignRepository:          unAcknowledgeReassignReposity,
		MongoUobRefRepository:                    proxyUobRefRepository,
		UobRefDataStore:                          uobRefDataStore,
		WhitelistPhoneRepository:                 proxyWhitelistPhoneRepository,
		WhitelistPhoneDataStore:                  whitelistPhoneDataStore,
		WithdrawalTransactionResultsRepository:   proxyWithdrawalTransactionResultsRepository,
		WithdrawalTransactionResultsDataStore:    withdrawalTransactionResultsDataStoreDataStore,
		WithholdingTaxCertificateRepository:      proxyWithholdingTaxCertificateRepository,
		WithholdingTaxCertificateDataStore:       withholdingTaxCertificateDataStore,
		ZoneDataStore:                            zoneDataStore,
		ZoneRepository:                           proxyZoneRepository,
		LazyRepEventBus:                          lazyRepEventBus,
		RepEventBus:                              repEventBus,
		CleanupPriority:                          cleanupPriority,
		LocalCache:                               caches,
		LineBotNotificationServiceWorker:         lineBotNotificationServiceWorker,
		StubMessageService:                       stubMessageService,
		FirebasePushNotificationService:          firebasePushNotificationService,
		SocketIOPushNotificationService:          socketIOPushNotificationService,
		SocketIOClient:                           socketioClient,
		ConsoleFBMessagingClient:                 consoleLogMessagingClient,
		PreloadExecutor:                          preloadExecutor,
		WorkerContext:                            workerContext,
		Registry:                                 providerRegistry,
		ServiceSelectorAPI:                       serviceSelector,
		MetricsRegistry:                          metricsRegistryImpl,
		PrometheusMeter:                          prometheusMeter,
		ServiceGRPCConnFactory:                   connectionFactory,
		StubPolygonApiForTest:                    stubPolygonApi,
		TopkekForTest:                            topkekForTest,
		DriverRegistrationTestData:               driverRegistrationTestData,
		Fixtures:                                 fixtures,
		OTPSessionTestData:                       otpSessionTestData,
		ProvincesTestData:                        provincesTestData,
		RatingRestaurantTestData:                 ratingRestaurantTestData,
		WhitelistPhonesTestData:                  whitelistPhonesTestData,
		TemplateService:                          template,
	}
	integrationTestContainer := &IntegrationTestContainer{
		init:          containerInitializer,
		Locator:       locator,
		SimpleUnleash: simpleUnleasher,
	}
	return integrationTestContainer, func() {
		cleanup34()
		cleanup33()
		cleanup32()
		cleanup31()
		cleanup30()
		cleanup29()
		cleanup28()
		cleanup27()
		cleanup26()
		cleanup25()
		cleanup24()
		cleanup23()
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
	}, nil
}
