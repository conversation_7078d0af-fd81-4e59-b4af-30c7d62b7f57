package types

import (
	shared_types "git.wndv.co/lineman/fleet-shared/types"
)

// Deprecated: use sets.Of instead
type StringSet = shared_types.StringSet

func NewStringSet(elements ...string) StringSet {
	return shared_types.NewStringSet(elements...)
}

// Set[T] is set of Ts, implemented using Go std map.
// Use SetSafe[T] if you need thread-safe implementation.
// type Set[T comparable] map[T]struct{}
type Set[T comparable] = shared_types.Set[T]

func NewSet[T comparable]() Set[T] {
	return shared_types.NewSet[T]()
}

func NewSetFrom[T comparable](items ...T) Set[T] {
	return shared_types.NewSetFrom[T](items...)
}

// DiffAll returns all the differences in s1 and s2
func DiffAll[T comparable](s1, s2 Set[T]) Set[T] {
	return shared_types.DiffAll(s1, s2)
}

// Diff returns set of s1-s2
func Diff[T comparable](s1, s2 Set[T]) Set[T] {
	return shared_types.Diff(s1, s2)
}

////func (s Set[T]) Contains(item T) bool {
////	_, ok := s[item]
////	return ok
////}
////
////// ContainsAll returns whether s contains all of items,
////// i.e. that items is subset of s
////func (s Set[T]) ContainsAll(items ...T) bool {
////	for i := range items {
////		if !s.Contains(items[i]) {
////			return false
////		}
////	}
////
////	return true
////}
////
////// ContainsExact returns whether s contains only values from items
////func (s Set[T]) ContainsExact(items ...T) bool {
////	itemsSet := NewSetFrom(items...)
////	if len(s) != len(itemsSet) {
////		return false
////	}
////
////	for value := range s {
////		if !itemsSet.Contains(value) {
////			return false
////		}
////	}
////
////	return true
////}
////
////func (s Set[T]) Add(items ...T) {
////	for i := range items {
////		s[items[i]] = struct{}{}
////	}
////}
////
////func (s Set[T]) Remove(targets ...T) {
////	for i := range targets {
////		delete(s, targets[i])
////	}
////}
////
////func (s Set[T]) Diff(other Set[T]) Set[T] {
////	return Diff(s, other)
////}
////
////func (s Set[T]) DiffAll(other Set[T]) Set[T] {
////	return DiffAll(s, other)
////}
////
////func (s Set[T]) Slice() []T {
////	slice := make([]T, len(s))
////	i := 0
////	for item := range s {
////		slice[i] = item
////		i++
////	}
////
////	return slice
////}
//
//func (s Set[T]) Len() int {
//	return len(s)
//}
//
//func (s Set[T]) Clone() Set[T] {
//	cloned := make(Set[T])
//	for v := range s {
//		cloned[v] = struct{}{}
//	}
//
//	return cloned
//}

// SetSafe[T] wraps Set[T] with mutex locks.
// Use Set[T] if it's going to be used in single-threaded use case.
//type SetSafe[T comparable] struct {
//	set Set[T]
//	mut *sync.RWMutex
//}

type SetSafe[T comparable] = shared_types.SetSafe[T]

func NewSetSafe[T comparable]() SetSafe[T] {
	return shared_types.NewSetSafe[T]()
}

func NewSetSafeFrom[T comparable](items ...T) SetSafe[T] {
	return shared_types.NewSetSafeFrom[T](items...)
}

//func (s *SetSafe[T]) Contains(item T) bool {
//	s.mut.RLock()
//	defer s.mut.RUnlock()
//
//	return s.set.Contains(item)
//}
//
//func (s *SetSafe[T]) ContainsAll(items ...T) bool {
//	s.mut.RLock()
//	defer s.mut.RUnlock()
//
//	return s.set.ContainsAll(items...)
//}
//
//func (s *SetSafe[T]) ContainsExact(item ...T) bool {
//	s.mut.RLock()
//	defer s.mut.RUnlock()
//
//	return s.set.ContainsExact(item...)
//}
//
//func (s *SetSafe[T]) Add(items ...T) {
//	s.mut.Lock()
//	defer s.mut.Unlock()
//
//	s.set.Add(items...)
//}
//
//func (s *SetSafe[T]) Remove(targets ...T) {
//	s.mut.Lock()
//	defer s.mut.Unlock()
//
//	s.set.Remove(targets...)
//}
//
//func (s *SetSafe[T]) Slice() []T {
//	s.mut.RLock()
//	defer s.mut.RUnlock()
//
//	return s.set.Slice()
//}
//
//func (s *SetSafe[T]) Len() int {
//	s.mut.RLock()
//	defer s.mut.Unlock()
//
//	return s.set.Len()
//}
//
//func (s *SetSafe[T]) Clone() SetSafe[T] {
//	s.mut.RLock()
//	defer s.mut.RUnlock()
//
//	return SetSafe[T]{
//		set: s.set.Clone(),
//		mut: new(sync.RWMutex),
//	}
//}
