// Code generated by MockGen. DO NOT EDIT.
// Source: ./rating_option_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockRatingOptionRepository is a mock of RatingOptionRepository interface.
type MockRatingOptionRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRatingOptionRepositoryMockRecorder
}

// MockRatingOptionRepositoryMockRecorder is the mock recorder for MockRatingOptionRepository.
type MockRatingOptionRepositoryMockRecorder struct {
	mock *MockRatingOptionRepository
}

// NewMockRatingOptionRepository creates a new mock instance.
func NewMockRatingOptionRepository(ctrl *gomock.Controller) *MockRatingOptionRepository {
	mock := &MockRatingOptionRepository{ctrl: ctrl}
	mock.recorder = &MockRatingOptionRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRatingOptionRepository) EXPECT() *MockRatingOptionRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRatingOptionRepository) Create(ctx context.Context, ro *model.RatingOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, ro)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockRatingOptionRepositoryMockRecorder) Create(ctx, ro interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRatingOptionRepository)(nil).Create), ctx, ro)
}

// Delete mocks base method.
func (m *MockRatingOptionRepository) Delete(ctx context.Context, ro *model.RatingOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, ro)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRatingOptionRepositoryMockRecorder) Delete(ctx, ro interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRatingOptionRepository)(nil).Delete), ctx, ro)
}

// Get mocks base method.
func (m *MockRatingOptionRepository) Get(ctx context.Context, id string) (*model.RatingOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*model.RatingOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRatingOptionRepositoryMockRecorder) Get(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRatingOptionRepository)(nil).Get), ctx, id)
}

// GetAll mocks base method.
func (m *MockRatingOptionRepository) GetAll(ctx context.Context, query repository.RatingOptionQuery) ([]model.RatingOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, query)
	ret0, _ := ret[0].([]model.RatingOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockRatingOptionRepositoryMockRecorder) GetAll(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockRatingOptionRepository)(nil).GetAll), ctx, query)
}

// Update mocks base method.
func (m *MockRatingOptionRepository) Update(ctx context.Context, ro *model.RatingOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, ro)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRatingOptionRepositoryMockRecorder) Update(ctx, ro interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRatingOptionRepository)(nil).Update), ctx, ro)
}
