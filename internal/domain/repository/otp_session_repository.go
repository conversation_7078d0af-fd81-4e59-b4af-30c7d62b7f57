package repository

//go:generate mockgen -source=./otp_session_repository.go -destination=./mock_repository/mock_otp_session_repository.go -package=mock_repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type OTPSessionRepo interface {
	Save(ctx context.Context, ses model.OTPSession) error
	Load(ctx context.Context, lineUID string) (model.OTPSession, error)
	LoadOrNew(ctx context.Context, lineUID string) (model.OTPSession, error)
	Remove(ctx context.Context, lineUID string) error
}
