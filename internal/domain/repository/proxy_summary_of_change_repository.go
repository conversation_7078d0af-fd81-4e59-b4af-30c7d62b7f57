// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxySummaryOfChangeRepository(delegate SummaryOfChangeRepository, meter metric.Meter) *ProxySummaryOfChangeRepository {
	return &ProxySummaryOfChangeRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxySummaryOfChangeRepository-tracer"),
	}
}

type ProxySummaryOfChangeRepository struct {
	Delegate         SummaryOfChangeRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxySummaryOfChangeRepository) Create(i0 context.Context, i1 *model.SummaryOfChange) error {

	_, span := p.Tracer.Start(i0, "SummaryOfChangeRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "SummaryOfChangeRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxySummaryOfChangeRepository) Update(i0 context.Context, i1 *model.SummaryOfChange) error {

	_, span := p.Tracer.Start(i0, "SummaryOfChangeRepository.Update")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Update(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "SummaryOfChangeRepository.Update")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxySummaryOfChangeRepository) Get(i0 context.Context, i1 string) (*model.SummaryOfChange, error) {

	_, span := p.Tracer.Start(i0, "SummaryOfChangeRepository.Get")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Get(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "SummaryOfChangeRepository.Get")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxySummaryOfChangeRepository) FindWithQueryAndSortSummaryOfChange(i0 context.Context, i1 interface{}, i2 int, i3 int, i4 ...Option) ([]model.SummaryOfChange, error) {

	_, span := p.Tracer.Start(i0, "SummaryOfChangeRepository.FindWithQueryAndSortSummaryOfChange")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindWithQueryAndSortSummaryOfChange(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "SummaryOfChangeRepository.FindWithQueryAndSortSummaryOfChange")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxySummaryOfChangeRepository) FindActiveSummaryOfChanges(i0 context.Context, i1 ...Option) ([]model.SummaryOfChange, error) {

	_, span := p.Tracer.Start(i0, "SummaryOfChangeRepository.FindActiveSummaryOfChanges")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindActiveSummaryOfChanges(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "SummaryOfChangeRepository.FindActiveSummaryOfChanges")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxySummaryOfChangeRepository) Archived(i0 context.Context, i1 string, i2 string) error {

	_, span := p.Tracer.Start(i0, "SummaryOfChangeRepository.Archived")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Archived(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "SummaryOfChangeRepository.Archived")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxySummaryOfChangeRepository) GetLatestSummaryOfChange(i0 context.Context, i1 ...Option) (*model.SummaryOfChange, error) {

	_, span := p.Tracer.Start(i0, "SummaryOfChangeRepository.GetLatestSummaryOfChange")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetLatestSummaryOfChange(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "SummaryOfChangeRepository.GetLatestSummaryOfChange")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
