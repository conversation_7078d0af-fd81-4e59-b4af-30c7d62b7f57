package repository

//go:generate mockgen -source=./rating_option_repository.go -destination=./mock_repository/mock_rating_option_repository.go -package=mock_repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type RatingOptionRepository interface {
	Get(ctx context.Context, id string) (*model.RatingOption, error)
	GetAll(ctx context.Context, query RatingOptionQuery) ([]model.RatingOption, error)
	Create(ctx context.Context, ro *model.RatingOption) error
	Update(ctx context.Context, ro *model.RatingOption) error
	Delete(ctx context.Context, ro *model.RatingOption) error
}

type RatingOptionQuery struct {
	Active     *bool
	RatingType model.RatingType
}

var (
	bTrue                        = true
	RatingOptionRestaurantActive = RatingOptionQuery{RatingType: model.Restaurant, Active: &bTrue}
	RatingOptionQueryAll         = RatingOptionQuery{}
)
