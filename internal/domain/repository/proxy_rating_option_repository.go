// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyRatingOptionRepository(delegate RatingOptionRepository, meter metric.Meter) *ProxyRatingOptionRepository {
	return &ProxyRatingOptionRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyRatingOptionRepository-tracer"),
	}
}

type ProxyRatingOptionRepository struct {
	Delegate         RatingOptionRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyRatingOptionRepository) Get(i0 context.Context, i1 string) (*model.RatingOption, error) {

	_, span := p.Tracer.Start(i0, "RatingOptionRepository.Get")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Get(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RatingOptionRepository.Get")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyRatingOptionRepository) GetAll(i0 context.Context, i1 RatingOptionQuery) ([]model.RatingOption, error) {

	_, span := p.Tracer.Start(i0, "RatingOptionRepository.GetAll")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetAll(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RatingOptionRepository.GetAll")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyRatingOptionRepository) Create(i0 context.Context, i1 *model.RatingOption) error {

	_, span := p.Tracer.Start(i0, "RatingOptionRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RatingOptionRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyRatingOptionRepository) Update(i0 context.Context, i1 *model.RatingOption) error {

	_, span := p.Tracer.Start(i0, "RatingOptionRepository.Update")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Update(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RatingOptionRepository.Update")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyRatingOptionRepository) Delete(i0 context.Context, i1 *model.RatingOption) error {

	_, span := p.Tracer.Start(i0, "RatingOptionRepository.Delete")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Delete(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RatingOptionRepository.Delete")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
