//go:generate mockgen -source=./driver_update_location_event_service.go -destination=./mock_service/mock_driver_update_location_event_service.go -package=mock_service

package service

import (
	"context"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/kelseyhightower/envconfig"
	"github.com/rs/xid"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gopkg.in/guregu/null.v4"

	"git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/logging"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/h3util"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var (
	_ DriverUpdateLocationEventService = (*DriverUpdateLocationEventServiceImpl)(nil)
	_ DriverUpdateLocationEventService = (*NoOpsUpdateLocationEventServiceImpl)(nil)
)

type DriverUpdateLocationEventService interface {
	PublishDriverUpdateLocation(ctx context.Context, driverID string, req model.DriverUpdateLocationEvent) error
	PublishDriverTurnOfflineEvent(ctx context.Context, driverID string) error
	PublishDriverInactiveEvents(ctx context.Context, drivers []model.DriverLastUpdateLocationAttempt) error
}

type NoOpsUpdateLocationEventServiceImpl struct{}

// PublishDriverInactiveEvents implements DriverUpdateLocationEventService.
func (*NoOpsUpdateLocationEventServiceImpl) PublishDriverInactiveEvents(ctx context.Context, drivers []model.DriverLastUpdateLocationAttempt) error {
	return nil
}

// PublishDriverTurnOfflineEvent implements DriverUpdateLocationEventService.
func (*NoOpsUpdateLocationEventServiceImpl) PublishDriverTurnOfflineEvent(ctx context.Context, driverID string) error {
	return nil
}

// PublishDriverUpdateLocation implements DriverUpdateLocationEventService.
func (*NoOpsUpdateLocationEventServiceImpl) PublishDriverUpdateLocation(ctx context.Context, driverID string, req model.DriverUpdateLocationEvent) error {
	return nil
}

type DriverUpdateLocationEventServiceImpl struct {
	cfg              DriverUpdateLocationEventServiceConfig
	imfKafkaProducer kafcclient.IMFKafkaProducer
}

type createProtoUpdateLocationEvent struct {
	clientTimestamp time.Time
	now             time.Time
	driverID        string
	lat             float64
	lng             float64
	region          string
	orders          []*driverv1.UpdateLocationOrder
	tripID          string
	tripStatus      string
}

type DriverUpdateLocationEventServiceConfig struct {
	EnablePublishUpdateLocation bool   `envconfig:"ENABLE_PUBLISH_UPDATE_LOCATION"  default:"false"`
	UpdateLocationTopic         string `envconfig:"UPDATE_LOCATION_TOPIC"`
}

func ProvideUpdateDriverLocationEventConfig() (cfg DriverUpdateLocationEventServiceConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func ProvideDriverLocationEventServiceImpl(cfg DriverUpdateLocationEventServiceConfig, p kafcclient.IMFKafkaProducer) DriverUpdateLocationEventService {
	if !cfg.EnablePublishUpdateLocation {
		logrus.Warn("ENABLE_PUBLISH_UPDATE_LOCATION flag is off")
		return &NoOpsUpdateLocationEventServiceImpl{}
	}
	return &DriverUpdateLocationEventServiceImpl{
		cfg:              cfg,
		imfKafkaProducer: p,
	}
}

func (svc *DriverUpdateLocationEventServiceImpl) PublishDriverUpdateLocation(ctx context.Context, driverID string, req model.DriverUpdateLocationEvent) error {
	var events []*driverv1.UpdateLocationEvent
	now := timeutil.BangkokNow()

	savedLocationEvents := savedLocations(req, now, driverID)
	events = append(events, savedLocationEvents...)

	event := currentRequestLocation(req, req.ClientTimestampUnixSecond, now, driverID)
	events = append(events, event)

	headers := map[string]string{}
	addXB3TraceID(ctx, headers)
	addRequestID(ctx, headers)

	var result *multierror.Error
	for _, p := range events {
		message, err := proto.Marshal(p)
		if err != nil {
			result = multierror.Append(result, err)
			continue
		}
		// publish main request body
		err = svc.imfKafkaProducer.SendMessage(ctx, svc.cfg.UpdateLocationTopic, driverID, message, headers)
		if err != nil {
			result = multierror.Append(result, err)
			continue
		}
	}

	return result.ErrorOrNil()
}

func (svc *DriverUpdateLocationEventServiceImpl) PublishDriverTurnOfflineEvent(ctx context.Context, driverID string) error {
	now := timeutil.BangkokNow()

	e := svc.offlineDriverEvent(driverID, now, driverv1.UpdateLocationEvent_INACTIVE_CAUSED_BY_DRIVER_TURN_OFFLINE)

	headers := map[string]string{}
	addXB3TraceID(ctx, headers)
	addRequestID(ctx, headers)

	message, err := proto.Marshal(e)
	if err != nil {
		return err
	}
	// publish main request body
	err = svc.imfKafkaProducer.SendMessage(ctx, svc.cfg.UpdateLocationTopic, driverID, message, headers)
	if err != nil {
		return err
	}

	return nil
}

func (svc *DriverUpdateLocationEventServiceImpl) PublishDriverInactiveEvents(ctx context.Context, drivers []model.DriverLastUpdateLocationAttempt) error {
	events := svc.inactiveDriverEvents(drivers)

	headers := map[string]string{}
	addXB3TraceID(ctx, headers)
	addRequestID(ctx, headers)

	var result *multierror.Error
	for _, event := range events {
		message, err := proto.Marshal(event)
		if err != nil {
			result = multierror.Append(result, err)
			continue
		}
		err = svc.imfKafkaProducer.SendMessage(ctx, svc.cfg.UpdateLocationTopic, event.DriverId, message, headers)
		if err != nil {
			result = multierror.Append(result, err)
			continue
		}
	}

	return result.ErrorOrNil()
}

func (svc *DriverUpdateLocationEventServiceImpl) inactiveDriverEvents(driverIDsWithScore []model.DriverLastUpdateLocationAttempt) []*driverv1.UpdateLocationEvent {
	msgs := make([]*driverv1.UpdateLocationEvent, len(driverIDsWithScore))
	now := timestamppb.Now()
	for idx, driverIDWithScore := range driverIDsWithScore {
		driverID := driverIDWithScore.DriverID
		id := xid.New()
		msg := &driverv1.UpdateLocationEvent{
			EventId:                            id.String(),
			ClientTimestamp:                    now,
			ServerTimestamp:                    now,
			DriverId:                           driverID,
			IsInactive:                         null.BoolFrom(true).Ptr(),
			InactiveCausedBy:                   driverv1.UpdateLocationEvent_INACTIVE_CAUSED_BY_EXCEED_LOCATION_UPDATE_THRESHOLD.Enum(),
			LastUpdateLocationAttemptTimestamp: timestamppb.New(time.Unix(driverIDWithScore.Timestamp, 0)),
			ClientTimestampUnixMicro:           now.AsTime().UnixMicro(),
			ServerTimestampUnixMicro:           now.AsTime().UnixMicro(),
		}
		msgs[idx] = msg
	}
	return msgs
}

func (svc *DriverUpdateLocationEventServiceImpl) offlineDriverEvent(driverID string, now time.Time, caused driverv1.UpdateLocationEvent_InactiveCausedBy) *driverv1.UpdateLocationEvent {
	id := xid.New()
	return &driverv1.UpdateLocationEvent{
		ClientTimestamp:          timestamppb.New(now),
		ServerTimestamp:          timestamppb.New(now),
		ClientTimestampUnixMicro: now.UnixMicro(),
		ServerTimestampUnixMicro: now.UnixMicro(),
		DriverId:                 driverID,
		EventId:                  id.String(),
		IsInactive:               null.BoolFrom(true).Ptr(),
		InactiveCausedBy:         caused.Enum(),
	}
}

func currentRequestLocation(req model.DriverUpdateLocationEvent, timestamp int64, now time.Time, driverID string) *driverv1.UpdateLocationEvent {
	orders := mappingProtoUpdateLocationOrder(req.Orders)
	orderQueue := mappingProtoUpdateLocationOrderQueue(req.OrderQueue)
	availableCapacity := mappingProtoUpdateLocationAvailableCapacity(req.AvailableCapacity)
	event := newUpdateLocationEventBuilder().
		ClientTimestamp(time.Unix(timestamp, 0)).
		Now(now).
		DriverID(driverID).
		Location(req.Lat, req.Lng).
		Region(req.Region).
		Orders(orders).
		TripID(req.TripId).
		TripStatus(req.TripStatus).
		OrderQueue(orderQueue).
		IsOfflineLater(req.IsOfflineLater).
		AvailableCapacity(availableCapacity).
		Build()
	return event
}

func savedLocations(req model.DriverUpdateLocationEvent, now time.Time, driverID string) []*driverv1.UpdateLocationEvent {
	events := []*driverv1.UpdateLocationEvent{}

	for _, s := range req.SavedLocations {
		orders := mappingProtoUpdateLocationOrder(s.Orders)
		orderQueue := mappingProtoUpdateLocationOrderQueue(req.OrderQueue)
		availableCapacity := mappingProtoUpdateLocationAvailableCapacity(req.AvailableCapacity)
		event := newUpdateLocationEventBuilder().
			ClientTimestamp(time.UnixMilli(s.Timestamp)).
			Now(now).
			DriverID(driverID).
			Location(s.Lat, s.Lng).
			Region(req.Region).
			Orders(orders).
			TripID(s.TripId).
			TripStatus(s.TripStatus).
			OrderQueue(orderQueue).
			IsOfflineLater(req.IsOfflineLater).
			AvailableCapacity(availableCapacity).
			Build()
		events = append(events, event)
	}

	return events
}

func mappingProtoUpdateLocationOrder(oo []model.DriverUpdateLocationOrder) []*driverv1.UpdateLocationOrder {
	var orders []*driverv1.UpdateLocationOrder
	for _, o := range oo {
		item := driverv1.UpdateLocationOrder{
			OrderId:     o.OrderId,
			OrderStatus: o.OrderStatus,
		}
		orders = append(orders, &item)
	}
	return orders
}

func mappingProtoUpdateLocationOrderQueue(orderQueue []model.DriverUpdateLocationOrderQueue) []*driverv1.UpdateLocationOrderQueue {
	return lo.Map(orderQueue, func(o model.DriverUpdateLocationOrderQueue, _ int) *driverv1.UpdateLocationOrderQueue {
		return &driverv1.UpdateLocationOrderQueue{
			OrderId: o.OrderID,
		}
	})
}

func mappingProtoUpdateLocationAvailableCapacity(capacity []model.DriverUpdateLocationAvailableCapacity) []*driverv1.UpdateLocationAvailableCapacity {
	return lo.Map(capacity, func(c model.DriverUpdateLocationAvailableCapacity, _ int) *driverv1.UpdateLocationAvailableCapacity {
		return &driverv1.UpdateLocationAvailableCapacity{
			ServiceType: c.ServiceType,
			Capacity:    c.Capacity,
			Enabled:     c.Enabled,
		}
	})
}

func addXB3TraceID(ctx context.Context, headers map[string]string) {
	key := utils.TraceIDKey
	if v, ok := ctx.Value(key).(string); ok {
		if v != "" {
			headers[string(key)] = v
		}
	}
}

func addRequestID(ctx context.Context, headers map[string]string) {
	key := logging.RequestIDKey
	if v, ok := ctx.Value(key).(string); ok {
		if v != "" {
			headers[string(key)] = v
		}
	}
}

type updateLocationEventBuilder struct {
	clientTimestamp   time.Time
	now               time.Time
	driverID          string
	lat               float64
	lng               float64
	region            string
	orders            []*driverv1.UpdateLocationOrder
	orderQueue        []*driverv1.UpdateLocationOrderQueue
	tripID            string
	tripStatus        string
	isOfflineLater    bool
	availableCapacity []*driverv1.UpdateLocationAvailableCapacity
}

func newUpdateLocationEventBuilder() *updateLocationEventBuilder {
	return &updateLocationEventBuilder{}
}

func (b *updateLocationEventBuilder) ClientTimestamp(t time.Time) *updateLocationEventBuilder {
	b.clientTimestamp = t
	return b
}

func (b *updateLocationEventBuilder) Now(t time.Time) *updateLocationEventBuilder {
	b.now = t
	return b
}

func (b *updateLocationEventBuilder) DriverID(driverID string) *updateLocationEventBuilder {
	b.driverID = driverID
	return b
}

func (b *updateLocationEventBuilder) Location(lat, lng float64) *updateLocationEventBuilder {
	b.lat = lat
	b.lng = lng
	return b
}

func (b *updateLocationEventBuilder) Region(region string) *updateLocationEventBuilder {
	b.region = region
	return b
}

func (b *updateLocationEventBuilder) Orders(orders []*driverv1.UpdateLocationOrder) *updateLocationEventBuilder {
	b.orders = orders
	return b
}

func (b *updateLocationEventBuilder) OrderQueue(orderQueue []*driverv1.UpdateLocationOrderQueue) *updateLocationEventBuilder {
	b.orderQueue = orderQueue
	return b
}

func (b *updateLocationEventBuilder) TripID(tripID string) *updateLocationEventBuilder {
	b.tripID = tripID
	return b
}

func (b *updateLocationEventBuilder) TripStatus(tripStatus string) *updateLocationEventBuilder {
	b.tripStatus = tripStatus
	return b
}

func (b *updateLocationEventBuilder) IsOfflineLater(isOfflineLater bool) *updateLocationEventBuilder {
	b.isOfflineLater = isOfflineLater
	return b
}

func (b *updateLocationEventBuilder) AvailableCapacity(availableCapacity []*driverv1.UpdateLocationAvailableCapacity) *updateLocationEventBuilder {
	b.availableCapacity = availableCapacity
	return b
}

func (b *updateLocationEventBuilder) Build() *driverv1.UpdateLocationEvent {
	id := xid.New()
	return &driverv1.UpdateLocationEvent{
		ClientTimestamp:          timestamppb.New(b.clientTimestamp),
		ServerTimestamp:          timestamppb.New(b.now),
		ClientTimestampUnixMicro: b.clientTimestamp.UnixMicro(),
		ServerTimestampUnixMicro: b.now.UnixMicro(),
		EventId:                  id.String(),
		DriverId:                 b.driverID,
		Lat:                      b.lat,
		Lng:                      b.lng,
		H3Index:                  h3util.GetH3Id(b.lat, b.lng, 15),
		DriverRegion:             b.region,
		Orders:                   b.orders,
		TripId:                   b.tripID,
		TripStatus:               b.tripStatus,
		OrderQueue:               b.orderQueue,
		IsOfflineLater:           b.isOfflineLater,
		AvailableCapacity:        b.availableCapacity,
	}
}
