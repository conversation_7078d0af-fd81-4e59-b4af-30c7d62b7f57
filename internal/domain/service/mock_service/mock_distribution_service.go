// Code generated by MockGen. DO NOT EDIT.
// Source: ./distribution_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
	gomock "github.com/golang/mock/gomock"
)

// MockDistributionService is a mock of DistributionService interface.
type MockDistributionService struct {
	ctrl     *gomock.Controller
	recorder *MockDistributionServiceMockRecorder
}

// MockDistributionServiceMockRecorder is the mock recorder for MockDistributionService.
type MockDistributionServiceMockRecorder struct {
	mock *MockDistributionService
}

// NewMockDistributionService creates a new mock instance.
func NewMockDistributionService(ctrl *gomock.Controller) *MockDistributionService {
	mock := &MockDistributionService{ctrl: ctrl}
	mock.recorder = &MockDistributionServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDistributionService) EXPECT() *MockDistributionServiceMockRecorder {
	return m.recorder
}

// DistributeOrder mocks base method.
func (m *MockDistributionService) DistributeOrder(ctx context.Context, event *driverv1.DistributeOrderEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistributeOrder", ctx, event)
	ret0, _ := ret[0].(error)
	return ret0
}

// DistributeOrder indicates an expected call of DistributeOrder.
func (mr *MockDistributionServiceMockRecorder) DistributeOrder(ctx, event interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributeOrder", reflect.TypeOf((*MockDistributionService)(nil).DistributeOrder), ctx, event)
}

// PublishDistributeOrderEvent mocks base method.
func (m *MockDistributionService) PublishDistributeOrderEvent(ctx context.Context, orderID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishDistributeOrderEvent", ctx, orderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishDistributeOrderEvent indicates an expected call of PublishDistributeOrderEvent.
func (mr *MockDistributionServiceMockRecorder) PublishDistributeOrderEvent(ctx, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishDistributeOrderEvent", reflect.TypeOf((*MockDistributionService)(nil).PublishDistributeOrderEvent), ctx, orderID)
}

// PublishRedistributeOrderEvent mocks base method.
func (m *MockDistributionService) PublishRedistributeOrderEvent(ctx context.Context, orderID string, ridersTriedAssigning *int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishRedistributeOrderEvent", ctx, orderID, ridersTriedAssigning)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRedistributeOrderEvent indicates an expected call of PublishRedistributeOrderEvent.
func (mr *MockDistributionServiceMockRecorder) PublishRedistributeOrderEvent(ctx, orderID, ridersTriedAssigning interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRedistributeOrderEvent", reflect.TypeOf((*MockDistributionService)(nil).PublishRedistributeOrderEvent), ctx, orderID, ridersTriedAssigning)
}
