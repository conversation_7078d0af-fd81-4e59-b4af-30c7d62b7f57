// Code generated by MockGen. DO NOT EDIT.
// Source: config.go

// Package mock_config is a generated GoMock package.
package mock_config

import (
	reflect "reflect"

	dbconfig "git.wndv.co/lineman/fleet-shared/dbconfig"
	gomock "github.com/golang/mock/gomock"
)

// MockConfigUpdater is a mock of ConfigUpdater interface.
type MockConfigUpdater struct {
	ctrl     *gomock.Controller
	recorder *MockConfigUpdaterMockRecorder
}

// MockConfigUpdaterMockRecorder is the mock recorder for MockConfigUpdater.
type MockConfigUpdaterMockRecorder struct {
	mock *MockConfigUpdater
}

// NewMockConfigUpdater creates a new mock instance.
func NewMockConfigUpdater(ctrl *gomock.Controller) *MockConfigUpdater {
	mock := &MockConfigUpdater{ctrl: ctrl}
	mock.recorder = &MockConfigUpdaterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfigUpdater) EXPECT() *MockConfigUpdaterMockRecorder {
	return m.recorder
}

// Fetch mocks base method.
func (m *MockConfigUpdater) Fetch() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Fetch")
}

// Fetch indicates an expected call of Fetch.
func (mr *MockConfigUpdaterMockRecorder) Fetch() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Fetch", reflect.TypeOf((*MockConfigUpdater)(nil).Fetch))
}

// Register mocks base method.
func (m *MockConfigUpdater) Register(atomicConfig dbconfig.AtomicConfig) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Register", atomicConfig)
}

// Register indicates an expected call of Register.
func (mr *MockConfigUpdaterMockRecorder) Register(atomicConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Register", reflect.TypeOf((*MockConfigUpdater)(nil).Register), atomicConfig)
}
