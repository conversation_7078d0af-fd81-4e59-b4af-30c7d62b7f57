package config

//go:generate mockgen -source=config.go -destination=./mock_config/mock_config.go -package=mock_config

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/go/pillars/sentry"
	lmredis "git.wndv.co/go/redis"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-shared/dbconfig"
)

// variable
var (
	Instance        GlobalConfig
	KAFKA_PEM_URL   string
	KAFKA_GIT_TOKEN string
)

type GlobalConfig struct {
	RefIDPrefix                string   `envconfig:"DRIVER_REFID_PREFIX"`
	UOBRefIDPrefix             string   `envconfig:"DRIVER_UOB_REFID_PREFIX" default:""`
	GenerateUOBRefIDEnabled    bool     `envconfig:"GENERATE_UOB_REFID_ENABLED" default:"false"`
	WhitelistPhotoURL          []string `envconfig:"WHITELIST_PHOTO_URL"`
	ApplicationCredentialsFile string   `envconfig:"APPLICATION_CREDENTIALS_PATH"`
	EnvName                    string   `envconfig:"ENV_NAME"`
	IsUnleashEnabled           bool     `envconfig:"IS_UNLEASH_ENABLED"  default:"false"`
	EnableBatchOptimizeLog     bool     `envconfig:"ENABLE_BATCH_OPTIMIZE_LOG" default:"false"`

	DisabledDownloadServiceAccountFromVOS bool `envconfig:"DISABLED_DOWNLOAD_SERVICE_ACCOUNT_FROM_VOS" default:"false"`
}

func ProvideGlobalConfig() (cfg GlobalConfig) {
	envconfig.MustProcess("", &cfg)
	Instance = cfg
	return
}

// RedisConfig redis config.
type RedisConfig struct {
	Host     []string `envconfig:"REDIS_HOST"`
	Password string   `envconfig:"REDIS_PASSWORD"`
	DB       int      `envconfig:"REDIS_DB" default:"0"`

	EnableMetricMonitor bool `envconfig:"REDIS_ENABLE_METRIC_MONITORING" default:"false"`
	EnableTracing       bool `envconfig:"REDIS_ENABLE_TRACING" default:"false"`
}

// ManMapConfig is configuration for connecting with lm-map.
type ManMapConfig struct {
	ManMapHost                 string  `envconfig:"LM_MAP_HOST"`
	GoogleDirectionsHost       string  `envconfig:"LM_GOOGLE_DIRECTIONS_HOST"`
	GoogleDirectionsApiKey     string  `envconfig:"LM_GOOGLE_DIRECTIONS_API_KEY"`
	GoogleDirectionsChannel    string  `envconfig:"LM_GOOGLE_DIRECTIONS_CHANNEL"`
	GoogleDirectionsAvoid      string  `envconfig:"LM_GOOGLE_DIRECTIONS_AVOIDS" default:"tolls|highways"`
	DistanceFetchBatchSize     int     `envconfig:"LM_MAP_DISTANCE_FETCH_BATCH_SIZE"`
	FallbackSpeed              int     `envconfig:"LM_MAP_FALLBACK_SPEED" default:"30"`
	FallbackDistanceMultiplier float64 `envconfig:"LM_MAP_FALLBACK_DISTANCT_MULTIPLIER" default:"1.4"`
}

type MapOverrideConfig struct {
	TagsToOverrideDistance        []string `envconfig:"TAGS_TO_OVERRIDE_DISTANCE" default:""`
	OverrideDistanceThresholdInKM float64  `envconfig:"OVERRIDE_DISTANCE_THRESHOLD_KM" default:"0"`
}

func NewAtomicMapOverrideConfig(config MapOverrideConfig) *AtomicMapOverrideConfig {
	return &AtomicMapOverrideConfig{config: config}
}

func ProvideAtomicMapOverrideConfig(configUpdater *DBConfigUpdater) *AtomicMapOverrideConfig {
	var cfg AtomicMapOverrideConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type AtomicMapOverrideConfig struct {
	lock   sync.RWMutex
	config MapOverrideConfig
	Validatable[MapOverrideConfig]
}

func (asmmc *AtomicMapOverrideConfig) Parse() {
	asmmc.lock.Lock()
	defer asmmc.lock.Unlock()
	envconfig.MustProcess("", &asmmc.config)

	// Temporary way to format the tags input from admin
	asmmc.config.TagsToOverrideDistance = stringutil.TrimAll(asmmc.config.TagsToOverrideDistance)
}

func (asmmc *AtomicMapOverrideConfig) Get() MapOverrideConfig {
	asmmc.lock.RLock()
	defer asmmc.lock.RUnlock()
	return asmmc.config
}

type LocationRedisConfig struct {
	RedisHosts    []string `envconfig:"DRIVER_LOCATION_REPOSITORY_REDIS_HOSTS"`
	RedisPassword string   `envconfig:"DRIVER_LOCATION_REPOSITORY_REDIS_PASSWORD"`
	RedisDB       int      `envconfig:"DRIVER_LOCATION_REPOSITORY_REDIS_DB" default:"0"`
	TLSEnabled    bool     `envconfig:"DRIVER_LOCATION_REPOSITORY_REDIS_TLS_ENABLED" default:"false"`
	PoolSize      int      `envconfig:"DRIVER_LOCATION_REPOSITORY_REDIS_POOS_SIZE" default:"10"`
	ReadOnly      bool     `envconfig:"DRIVER_LOCATION_REPOSITORY_REDIS_READ_ONLY" default:"true"`
	RouteRandomly bool     `envconfig:"DRIVER_LOCATION_REPOSITORY_REDIS_ROUTE_RANDOMLY" default:"true"`
}

func (config LocationRedisConfig) ToLmRedisConfig() lmredis.Config {
	return lmredis.Config{
		Hosts:         config.RedisHosts,
		Password:      config.RedisPassword,
		DB:            config.RedisDB,
		PoolSize:      config.PoolSize,
		TLSEnabled:    config.TLSEnabled,
		ReadOnly:      config.ReadOnly,
		RouteRandomly: config.ReadOnly,
	}
}

type DriverStatRedisConfig struct {
	RedisHosts    []string `envconfig:"REDIS_DRIVER_STAT_HOSTS"`
	RedisPassword string   `envconfig:"REDIS_DRIVER_STAT_PASSWORD"`
	RedisDB       int      `envconfig:"REDIS_DRIVER_STAT_DB" default:"0"`
	TLSEnabled    bool     `envconfig:"REDIS_DRIVER_STAT_TLS_ENABLED" default:"false"`
	PoolSize      int      `envconfig:"REDIS_DRIVER_STAT_POOS_SIZE" default:"10"`
	ReadOnly      bool     `envconfig:"REDIS_DRIVER_STAT_READ_ONLY" default:"true"`
	RouteRandomly bool     `envconfig:"REDIS_DRIVER_STAT_ROUTE_RANDOMLY" default:"true"`
}

func (driverStatConfig DriverStatRedisConfig) ToLmRedisConfig() lmredis.Config {
	return lmredis.Config{
		Hosts:         driverStatConfig.RedisHosts,
		Password:      driverStatConfig.RedisPassword,
		DB:            driverStatConfig.RedisDB,
		PoolSize:      driverStatConfig.PoolSize,
		TLSEnabled:    driverStatConfig.TLSEnabled,
		ReadOnly:      driverStatConfig.ReadOnly,
		RouteRandomly: driverStatConfig.ReadOnly,
	}
}

// @@wire-set-name@@ name:"MainConfigSet"
func ProvideLocationRedisConfig() (cfg LocationRedisConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

// @@wire-set-name@@ name:"MainConfigSet"
func ProvideDriverStatRedisConfig() (cfg DriverStatRedisConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type ServiceAreaRepositoryConfig struct {
	Exp time.Duration `envconfig:"SERVICE_AREA_EXPIRE_TIME" default:"4h"`
}

func ProvideServiceAreaRepositoryConfig() (cfg ServiceAreaRepositoryConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type DeliveryFeeSettingRepositoryConfig struct {
	Exp time.Duration `envconfig:"DELIVERY_FEE_SETTING_EXPIRE_TIME" default:"4h"`
}

func ProvideDeliveryFeeSettingRepositoryConfig() (cfg DeliveryFeeSettingRepositoryConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type DeliveryFeeSettingPriceSchemesRepositoryConfig struct {
	Exp time.Duration `envconfig:"PRICE_SCHEME_EXPIRE_TIME" default:"4h"`
}

func ProvideDeliveryFeeSettingPriceSchemesRepositoryConfig() (cfg DeliveryFeeSettingPriceSchemesRepositoryConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type DriverRatingConfig struct {
	Min    int `envconfig:"COLD_BOOT_MIN_RATE" default:"5"`
	MASize int `envconfig:"MOVING_AVERAGE_SIZE" default:"100"`
}

func ProvideDriverRatingConfig() (cfg DriverRatingConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type AttendanceRateConfig struct {
	AttendanceRateEnabled bool `envconfig:"ATTENDANCE_RATE_ENABLED" default:"true"`
}

func ProvideAttendanceRateConfig() (cfg AttendanceRateConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type CancellationRateConfig struct {
	InitialQuotaSize       int `envconfig:"INITIAL_CANCELLATION_RATE_FREE_QUOTA" default:"2"`
	MaxQuotaSize           int `envconfig:"MAX_RATE_FREE_QUOTA" default:"4"`
	CompleteOrdersPerQuota int `envconfig:"COMPLETE_ORDERS_PER_QUOTA" default:"18"`
}

type AtomicCancellationRateConfig = AtomicWrapper[CancellationRateConfig]

func NewAtomicCancellationRateConfig(config CancellationRateConfig) *AtomicCancellationRateConfig {
	return NewAtomicWrapper(config)
}

func ProvideAtomicCancellationRateConfig(configUpdater *DBConfigUpdater) *AtomicCancellationRateConfig {
	var cfg AtomicCancellationRateConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type TripConfig struct {
	// A commission rate fallbacks to this config if `CommissionRateSettings` aren't set
	CommissionRate                 float64            `envconfig:"ORDER_MINIMUM_CREDIT_COMMISSION" required:"true"`
	CommissionRateSettingsEnabled  bool               `envconfig:"COMMISSION_RATE_SETTINGS_ENABLED" default:"false"`
	CommissionRateSettings         map[string]float64 `envconfig:"COMMISSION_RATE_SETTINGS"`
	WithHoldingTaxRate             float64            `envconfig:"ORDER_MINIMUM_CREDIT_WITH_HOLDING_TAX" required:"true"`
	MultipleOrderWeight            []float64          `envconfig:"MULTIPLE_ORDER_WEIGHT" default:"1,0.5,0.33"`
	MultipleOrderMinimumDriverWage types.Money        `envconfig:"MULTIPLE_ORDER_MINIMUM_DRIVER_WAGE" default:"10.0"`
}

func NewAtomicTripConfig(config TripConfig) *AtomicTripConfig {
	return NewAtomicWrapper(config)
}

func ProvideAtomicTripConfig(configUpdater *DBConfigUpdater) *AtomicTripConfig {
	var cfg AtomicTripConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type AtomicTripConfig = AtomicWrapper[TripConfig]

type SupplyPositioningConfig struct {
	SupplyPositioningExpireRecoRetention time.Duration `envconfig:"SUPPLY_POSITIONING_EXPIRE_RECO_RETENTION" default:"48h"`
	SupplyPositioningRegions             []string      `envconfig:"SUPPLY_POSITIONING_REGIONS"`
	SupplyPositioningIdleThreshold       time.Duration `envconfig:"SUPPLY_POSITIONING_IDLE_THRESHOLD" default:"10m"`
	SupplyPositioningEnabled             bool          `envconfig:"SUPPLY_POSITIONING_ENABLED" default:"true"`
	SupplyPositioningWorkerPoolSize      int           `envconfig:"SUPPLY_POSITIONING_WORKER_POOL_SIZE" default:"30"`
	SupplyPositioningExpireCooldownTime  time.Duration `envconfig:"SUPPLY_POSITIONING_EXPIRE_COOLDOWN_TIME" default:"10s"`
	// this map used for enhance idle time calculation in supply positioning
	RegionsMap map[string]bool
}

type AtomicSupplyPositioningConfig struct {
	lock   sync.RWMutex
	Config SupplyPositioningConfig
	Validatable[SupplyPositioningConfig]
}

func (a *AtomicSupplyPositioningConfig) Parse() {
	a.lock.Lock()
	defer a.lock.Unlock()
	envconfig.MustProcess("", &a.Config)

	var regions []string
	rgMap := make(map[string]bool)
	for _, rg := range a.Config.SupplyPositioningRegions {
		rg = strings.TrimSpace(rg)
		rgMap[rg] = true
		regions = append(regions, rg)
	}

	a.Config.SupplyPositioningRegions = regions
	a.Config.RegionsMap = rgMap
}

func (a *AtomicSupplyPositioningConfig) Get() SupplyPositioningConfig {
	a.lock.RLock()
	defer a.lock.RUnlock()
	return a.Config
}

func ProvideAtomicSupplyPositioningConfig(configUpdater *DBConfigUpdater) *AtomicSupplyPositioningConfig {
	var cfg AtomicSupplyPositioningConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type ConfigurableTime time.Time

type DriverPeriodCompletedTripsConfig struct {
	StartTime ConfigurableTime `envconfig:"DRIVER_PERIOD_COMPLETED_TRIPS_START_TIME"`
	PeriodDay int              `envconfig:"DRIVER_PERIOD_COMPLETED_TRIPS_PERIOD_DAY"`
}

func (t *ConfigurableTime) GetTime() time.Time {
	return time.Time(*t)
}

func (t *ConfigurableTime) Decode(value string) error {
	configTime, err := time.Parse(time.RFC3339, value)
	if err != nil {
		panic(fmt.Sprintf("cannot parse %s to time.RFC3339", value))
	}

	*t = ConfigurableTime(configTime)
	return nil
}

func ProvideDriverPeriodCompletedTripsConfig() (cfg DriverPeriodCompletedTripsConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

// AutomaticLookupEnv lookup env file when ENV environment variable is set.
func AutomaticLookupEnv() {
	if env, ok := os.LookupEnv("ENV"); ok {
		if err := LoadEnvFromPath(".env." + env); err != nil {
			logrus.Fatalf("cannot load env from file: %v", err)
		}
		logrus.Infof("Running in environment: %s", env)
	}
}

func LoadEnvFromPath(p string) error {
	return godotenv.Load(p)
}

// LoadConfig for load all config file
func LoadConfig() {
	crypt.EncryptedKey = os.Getenv("ENCRYPTED_KEY")
	crypt.EnableEncrypted, _ = strconv.ParseBool(utils.GetEnv("ENABLE_ENCRYPT", "true"))
	crypt.EnableTopkek, _ = strconv.ParseBool(utils.GetEnv("ENABLE_TOPKEK", "true"))

	KAFKA_PEM_URL = os.Getenv("KAFKA_PEM_URL")
	KAFKA_GIT_TOKEN = os.Getenv("KAFKA_GIT_TOKEN")
}

type APISpecConfig struct {
	BasePath                         string `envconfig:"BASE_PATH" default:""`
	IsEnabled                        bool   `envconfig:"ENABLE" default:"false"`
	IncomeSummaryQueryFeatureEnabled bool   `envconfig:"INCOME_SUMMARY_QUERY_FEATURE_ENABLED" default:"true"`
}

type RouterConfig struct {
	LogMultipartFileContent       bool `envconfig:"LOG_MULTIPART_FILE" default:"false"`
	AcceptOrderAPIMinPatchVersion int  `envconfig:"ACCEPT_ORDER_API_MIN_PATCH_VERSION" default:"17"`
	// Deprecated: Remove after InboundHTTPLogMaskedBodyRegexes is deployed
	HTTPLogMaskedBodyPaths          []string `envconfig:"HTTP_LOG_MASKED_BODY_PATHS"`
	InboundHTTPLogMaskedBodyRegexes []string `envconfig:"INBOUND_HTTP_LOG_MASKED_BODY_REGEXES"`
	IsWithTracerEnabled             bool     `envconfig:"IS_WITH_TRACER_ENABLED" default:"true"`
}

func ProvideAPISpecConfig() (cfg APISpecConfig) {
	envconfig.MustProcess("API_SPEC", &cfg)
	return
}

func ProvideRouterConfig() (cfg RouterConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func ProvideSentryConfig() sentry.Config {
	return *sentry.NewConfig()
}

type DBConfig = dbconfig.DBConfig
type DBConfigUpdater = dbconfig.AtomicConfigUpdater

type ConfigUpdater interface {
	Register(atomicConfig dbconfig.AtomicConfig)
	Fetch()
}

var _ ConfigUpdater = (*dbconfig.AtomicConfigUpdater)(nil)

type LocationManagerConfig struct {
	DriverUpdateLocationFilter bool `envconfig:"LOCATION_MANAGER_DRIVER_UPDATE_LOCATION_FILTER"`
	EnableUpdateLocationOnly   bool `envconfig:"LOCATION_MANAGER_IS_UPDATE_LOCATION_ONLY_ENABLED"`
}

func ProvideLocationManagerConfig() (cfg LocationManagerConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type DriverProfileRequestConfig struct {
	EnableRequireDriverLegislationImageExpireDateFlag bool   `envconfig:"ENABLE_REQUIRE_LEGISLATION_IMAGE_EXPIRE_DATE" default:"false"`
	EnableRequireDriverIdCardImageExpireDateFlag      bool   `envconfig:"ENABLE_REQUIRE_ID_CARD_IMAGE_EXPIRE_DATE" default:"false"`
	IDCardExpRequiredText                             string `envconfig:"ID_CARD_EXP_REQUIRED_TXT" default:"ต้องการวันหมดอายุสำหรับบัตรประจำตัวประชาชน"`
	IDCardImageRequiredText                           string `envconfig:"ID_CARD_IMG_REQUIRED_TXT" default:"ต้องการรูปภาพสำหรับบัตรประจำตัวประชาชน"`
	LegislationExpRequiredText                        string `envconfig:"LEGISLATION_EXP_REQUIRED_TXT" default:"ต้องการวันหมดอายุสำหรับใบ พ.ร.บ."`
	LegislationImageRequiredText                      string `envconfig:"LEGISLATION_IMAGE_REQUIRED_TXT" default:"ต้องการรูปภาพสำหรับใบ พ.ร.บ."`
	InvalidExpirationDateText                         string `envconfig:"INVALID_CARD_EXPIRED_TXT" default:"วันหมดอายุของคุณไม่ถูกต้อง"`
	DisableClientRequestUpdateProfile                 bool   `envconfig:"DISABLE_CLIENT_REQUEST_UPDATE_PROFILE" default:"false"`
}

func ProvideDriverProfileRequestConfig() (cfg DriverProfileRequestConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type AtomicDedicatedPriorityScorerConfig struct {
	lock   sync.RWMutex
	Config DedicatedPriorityScorerConfig
	Validatable[DedicatedPriorityScorerConfig]
}

type DedicatedPriorityScorerConfig struct {
	DedicatedPriorityBaseConfig    string `envconfig:"DEDICATED_PRIORITY_BASE_CONFIG" default:""`
	DedicatedPriorityBaseConfigMap DedicatedPriorityBaseConfigMap
}

type DedicatedPriorityBaseConfigMap map[string]DedicatedPriorityBaseConfig

type DedicatedPriorityBaseConfig struct {
	BaseScores map[int]float64
	Distance   float64
}

func (a *AtomicDedicatedPriorityScorerConfig) Parse() {
	a.lock.Lock()
	defer a.lock.Unlock()
	a.Config = DedicatedPriorityScorerConfig{}
	envconfig.MustProcess("", &a.Config)

	if a.Config.DedicatedPriorityBaseConfig != "" {
		cfg := DedicatedPriorityBaseConfigMap{}
		err := json.Unmarshal([]byte(a.Config.DedicatedPriorityBaseConfig), &cfg)
		if err != nil {
			err := fmt.Errorf("AtomicDedicatedPriorityScorerConfig %s  Error unmarshaling DedicatedPriorityBaseConfig JSON: %v", a.Config.DedicatedPriorityBaseConfig, err)
			logx.Error().Err(err)
			sentry.CaptureException(context.Background(), err)
			a.Config.DedicatedPriorityBaseConfigMap = DedicatedPriorityBaseConfigMap{}
			return
		}
		a.Config.DedicatedPriorityBaseConfigMap = cfg
	} else {
		a.Config.DedicatedPriorityBaseConfigMap = DedicatedPriorityBaseConfigMap{}
	}
}

func (a *AtomicDedicatedPriorityScorerConfig) Get() DedicatedPriorityScorerConfig {
	a.lock.RLock()
	defer a.lock.RUnlock()
	return a.Config
}

func ProvideAtomicDedicatedPriorityScorerConfig(configUpdater *DBConfigUpdater) *AtomicDedicatedPriorityScorerConfig {
	var cfg AtomicDedicatedPriorityScorerConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type NegativeCreditConfig struct {
	// format is HH:mm
	NegativeCreditLunchPeakHourStart  string `envconfig:"NEGATIVE_CREDIT_LUNCH_PEAK_HOUR_START" default:"10:00"`
	NegativeCreditLunchPeakHourEnd    string `envconfig:"NEGATIVE_CREDIT_LUNCH_PEAK_HOUR_END" default:"13:00"`
	NegativeCreditDinnerPeakHourStart string `envconfig:"NEGATIVE_CREDIT_DINNER_PEAK_HOUR_START" default:"17:00"`
	NegativeCreditDinnerPeakHourEnd   string `envconfig:"NEGATIVE_CREDIT_DINNER_PEAK_HOUR_END" default:"20:00"`
	NegativeCreditEnabled             bool   `envconfig:"NEGATIVE_CREDIT_ENABLED" default:"false"`
}

type AtomicNegativeCreditConfig = AtomicWrapper[NegativeCreditConfig]

func NewAtomicNegativeCreditConfig(config NegativeCreditConfig) *AtomicNegativeCreditConfig {
	return NewAtomicWrapper(config)
}

func ProvideAtomicNegativeCreditConfig(configUpdater *DBConfigUpdater) *AtomicNegativeCreditConfig {
	var cfg AtomicNegativeCreditConfig

	configUpdater.Register(&cfg)
	return &cfg
}

func formatValueString(value interface{}) string {
	return fmt.Sprintf("%v", value)
}

type MongoTxnConfig struct {
	EnableTxnCommitTime     bool          `envconfig:"ENABLE_TXN_COMMIT_TIME" default:"false"`
	EnableTxnRetryWindow    bool          `envconfig:"ENABLE_TXN_RETRY_WINDOW" default:"false"`
	TxnCommitTime           time.Duration `envconfig:"TXN_COMMIT_TIME" default:"30s"`
	TxnRetryWindow          time.Duration `envconfig:"TXN_RETRY_WINDOW" default:"15s"`
	UseSecondaryDBByDefault bool          `envconfig:"USE_SECONDARY_DB_BY_DEFAULT" default:"false"`
}

type AtomicMongoTxnConfig = AtomicWrapper[MongoTxnConfig]

func NewAtomicMongoTxnConfig(config MongoTxnConfig) *AtomicMongoTxnConfig {
	return NewAtomicWrapper(config)
}

func ProvideAtomicMongoTxnConfig(configUpdater *DBConfigUpdater) *AtomicMongoTxnConfig {
	var cfg AtomicMongoTxnConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type MongoProfilerDBConfig struct {
	EnableMongoProfiler                        bool          `envconfig:"ENABLE_MONGO_PROFILER" default:"false"`
	EnableMongoProfilerLoggingOnlyIfLockQueued bool          `envconfig:"ENABLE_MONGO_PROFILER_LOGGING_ONLY_IF_LOCK_QUEUED" default:"true"`
	MongoProfilerInterval                      time.Duration `envconfig:"MONGO_PROFILER_INTERVAL" default:"500ms"`
	MongoProfilerRuntimeLowerThreshold         time.Duration `envconfig:"MONGO_PROFILER_RUNTIME_LOWER_THRESHOLD" default:"300ms"`
	MongoProfilerFileUploadInterval            time.Duration `envconfig:"MONGO_PROFILER_FILE_UPLOAD_INTERVAL" default:"10m"`
	MongoProfilerMaxStoredInMemoryMB           float64       `envconfig:"MONGO_PROFILER_MAX_STORED_IN_MEMORY_MB" default:"10.0"`
	MongoProfilerMaxQueryTime                  time.Duration `envconfig:"MONGO_PROFILER_MAX_QUERY_TIME" default:"2s"`
	MongoProfilerMaxOperationsPerQuery         int           `envconfig:"MONGO_PROFILER_MAX_OPERATIONS_PER_QUERY" default:"300"`
}

type AtomicMongoProfilerConfig = AtomicWrapper[MongoProfilerDBConfig]

func NewMongoProfilerConfig(config MongoProfilerDBConfig) *AtomicMongoProfilerConfig {
	return NewAtomicWrapper(config)
}

func ProvideAtomicMongoProfilerConfig(configUpdater *DBConfigUpdater) *AtomicMongoProfilerConfig {
	var cfg AtomicMongoProfilerConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type MongoProfilerConfig struct {
	MongoProfilerS3Bucket string `envconfig:"MONGO_PROFILER_S3_BUCKET"`
}

func ProvideMongoProfilerConfig() (cfg MongoProfilerConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type ThrottledOrderDBConfig struct {
	IsUseSecondaryDB bool `envconfig:"IS_USE_SECONDARY_DB" default:"false"`
}

func ProvideThrottledOrderConfig() ThrottledOrderDBConfig {
	var cfg ThrottledOrderDBConfig
	envconfig.MustProcess("THROTTLED_ORDERS", &cfg)
	return cfg
}
