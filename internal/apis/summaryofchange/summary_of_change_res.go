package summaryofchange

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/xgo/timeutil"
)

type SummaryOfChangeRes struct {
	ID                  string    `json:"id"`
	Name                string    `json:"name"`
	Status              string    `json:"status"`
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`
	SummaryOfChangeDate time.Time `json:"summaryOfChangeDate"`
	Content             string    `json:"content"`
	CreatedBy           string    `json:"createdBy"`
	UpdatedBy           string    `json:"updatedBy"`
}

func NewSummaryOfChangeListRes(summaries []model.SummaryOfChange) []SummaryOfChangeRes {
	res := make([]SummaryOfChangeRes, len(summaries))
	for i, s := range summaries {
		res[i] = NewSummaryOfChangeRes(s)
	}
	return res
}
func NewSummaryOfChangeRes(summary model.SummaryOfChange) SummaryOfChangeRes {
	return SummaryOfChangeRes{
		ID:                  summary.ID.Hex(),
		Name:                summary.Name,
		Status:              summary.Status.ToString(),
		CreatedAt:           summary.CreatedAt.In(timeutil.BangkokLocation()),
		UpdatedAt:           summary.UpdatedAt.In(timeutil.BangkokLocation()),
		SummaryOfChangeDate: summary.SummaryOfChangeDate.In(timeutil.BangkokLocation()),
		Content:             summary.Content,
		CreatedBy:           summary.CreatedBy,
		UpdatedBy:           summary.UpdatedBy,
	}
}

type ClientSummaryOfChangeRes struct {
	SummaryOfChanges []ClientSummaryOfChangeItem `json:"summaryOfChanges"`
}

type ClientSummaryOfChangeItem struct {
	Name                string    `json:"name"`
	Status              string    `json:"status"`
	SummaryOfChangeDate time.Time `json:"summaryOfChangeDate"`
	Content             string    `json:"content"`
}

func NewClientSummaryOfChangeListRes(summaries []model.SummaryOfChange) ClientSummaryOfChangeRes {
	res := make([]ClientSummaryOfChangeItem, len(summaries))
	for i, s := range summaries {
		res[i] = NewClientSummaryOfChangeRes(s)
	}
	return ClientSummaryOfChangeRes{
		SummaryOfChanges: res,
	}
}
func NewClientSummaryOfChangeRes(summary model.SummaryOfChange) ClientSummaryOfChangeItem {
	return ClientSummaryOfChangeItem{
		Name:                summary.Name,
		Status:              summary.Status.ToString(),
		SummaryOfChangeDate: summary.SummaryOfChangeDate.In(timeutil.BangkokLocation()),
		Content:             summary.Content,
	}
}
