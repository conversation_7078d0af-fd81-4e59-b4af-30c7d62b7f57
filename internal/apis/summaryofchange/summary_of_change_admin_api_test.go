package summaryofchange_test

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/summaryofchange"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestSummaryOfChangeAdminAPI_Create(t *testing.T) {
	t.Parallel()

	makeReq := func(req summaryofchange.SummaryOfChangeReq) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/summary-of-change", testutil.JSON(&req))
	}

	summaryReq := summaryofchange.SummaryOfChangeReq{
		Content: "SUMMARY_CONTENT",
	}

	t.Run("should return 201 and create new summary of change correctly", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditRepo.
			EXPECT().Insert(gomock.Any(), gomock.Any()).
			Return(nil)

		gctx, recorder := makeReq(summaryReq)

		api.Create(gctx)

		require.Equal(t, http.StatusCreated, recorder.Code)
	})

	t.Run("should return error when creating new summary of change error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "create summary of change error"

		deps.summaryOfChangeRepo.
			EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(errors.New(errMsg))

		gctx, recorder := makeReq(summaryReq)

		api.Create(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should log error when unable to save audit log", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "save audit log error"

		deps.summaryOfChangeRepo.
			EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditRepo.
			EXPECT().Insert(gomock.Any(), gomock.Any()).
			Return(errors.New(errMsg))

		gctx, recorder := makeReq(summaryReq)

		api.Create(gctx)

		require.Equal(t, http.StatusCreated, recorder.Code)
	})
}

func TestSummaryOfChangeAdminAPI_Update(t *testing.T) {
	t.Parallel()

	makeReq := func(req summaryofchange.SummaryOfChangeReq) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("PUT", "/v1/admin/summary-of-change", testutil.JSON(&req))
	}

	existingSummary := &model.SummaryOfChange{
		Content: "SUMMARY_CONTENT",
	}
	summaryReq := summaryofchange.SummaryOfChangeReq{
		Content: "SUMMARY_CONTENT",
	}

	t.Run("should return 200 and update summary of change correctly", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(existingSummary, nil)

		deps.summaryOfChangeRepo.
			EXPECT().Update(gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditRepo.
			EXPECT().Insert(gomock.Any(), gomock.Any()).
			Return(nil)

		gctx, recorder := makeReq(summaryReq)

		api.Update(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})

	t.Run("should return error when updating summary of change error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "update summary of change error"

		deps.summaryOfChangeRepo.
			EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(existingSummary, nil)

		deps.summaryOfChangeRepo.
			EXPECT().Update(gomock.Any(), gomock.Any()).
			Return(errors.New(errMsg))

		gctx, recorder := makeReq(summaryReq)

		api.Update(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should log error when unable to save audit log", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "save audit log error"

		deps.summaryOfChangeRepo.
			EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(existingSummary, nil)

		deps.summaryOfChangeRepo.
			EXPECT().Update(gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditRepo.
			EXPECT().Insert(gomock.Any(), gomock.Any()).
			Return(errors.New(errMsg))

		gctx, recorder := makeReq(summaryReq)

		api.Update(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})
}

func TestSummaryOfChangeAdminAPI_Get(t *testing.T) {
	t.Parallel()

	makeReq := func() (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", "/v1/admin/summary-of-change", nil)
	}

	existingSummary := &model.SummaryOfChange{
		Content: "SUMMARY_CONTENT",
	}

	t.Run("should return 200 and get summary of change correctly", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(existingSummary, nil)

		gctx, recorder := makeReq()

		api.Get(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})

	t.Run("should return error when getting summary of change error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "get summary of change error"

		deps.summaryOfChangeRepo.
			EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(nil, errors.New(errMsg))

		gctx, recorder := makeReq()

		api.Get(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
	})
}

func TestSummaryOfChangeAdminAPI_GetAll(t *testing.T) {
	t.Parallel()

	makeReq := func() (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", "/v1/admin/summary-of-change", nil)
	}

	existingSummaries := []model.SummaryOfChange{
		{
			Content: "SUMMARY_ID_1",
		},
		{
			Content: "SUMMARY_ID_2",
		},
	}

	t.Run("should return 200 and get all summaries of change correctly", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().FindWithQueryAndSortSummaryOfChange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(existingSummaries, nil)

		gctx, recorder := makeReq()

		api.GetAll(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})

	t.Run("should return error when getting all summaries of change error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "get all summaries of change error"

		deps.summaryOfChangeRepo.
			EXPECT().FindWithQueryAndSortSummaryOfChange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(nil, errors.New(errMsg))

		gctx, recorder := makeReq()

		api.GetAll(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
	})
}

func TestSummaryOfChangeAdminAPI_Archived(t *testing.T) {
	t.Parallel()

	makeReq := func() (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("DELETE", "/v1/admin/summary-of-change", nil)
	}

	t.Run("should return 200 and archive summary of change correctly", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().Archived(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditRepo.
			EXPECT().Insert(gomock.Any(), gomock.Any()).
			Return(nil)

		gctx, recorder := makeReq()

		api.Archived(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})

	t.Run("should return error when archiving summary of change error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "archive summary of change error"

		deps.summaryOfChangeRepo.
			EXPECT().Archived(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(errors.New(errMsg))

		gctx, recorder := makeReq()

		api.Archived(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should log error when unable to save audit log", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "save audit log error"

		deps.summaryOfChangeRepo.
			EXPECT().Archived(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditRepo.
			EXPECT().Insert(gomock.Any(), gomock.Any()).
			Return(errors.New(errMsg))

		gctx, recorder := makeReq()

		api.Archived(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})
}
