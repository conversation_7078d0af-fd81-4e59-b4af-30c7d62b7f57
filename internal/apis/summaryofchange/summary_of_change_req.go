package summaryofchange

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type GetAllSummaryOfChangeAdminReq struct {
	Status model.SummaryOfChangeStatus `json:"status" form:"status"`
}

type SummaryOfChangeReq struct {
	Name                string                      `json:"name"`
	Status              model.SummaryOfChangeStatus `json:"status"`
	CreatedAt           time.Time                   `json:"createdAt"`
	UpdatedAt           time.Time                   `json:"updatedAt"`
	SummaryOfChangeDate time.Time                   `json:"summaryOfChangeDate"`
	Content             string                      `json:"content"`
	CreatedBy           string                      `json:"createdBy"`
}

func (r SummaryOfChangeReq) toModel() model.SummaryOfChange {
	return model.SummaryOfChange{
		ID:                  primitive.NewObjectID(),
		Name:                r.Name,
		Status:              r.Status,
		CreatedAt:           r.<PERSON>,
		UpdatedAt:           r.<PERSON>,
		SummaryOfChangeDate: r.SummaryOfChangeDate,
		Content:             r.Content,
		CreatedBy:           r.CreatedBy,
	}
}
