//go:build integration_test
// +build integration_test

package payment_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	inventoryv1 "git.wndv.co/go/proto/lineman/inventory/v1"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestListApproval(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)

	ctx := testutil.NewContextWithRecorder()
	ctx.SetGET("/v1/admin/approvals").
		Body().
		JSON(payment.ListApproveReq{
			Category: payment.Credit,
		}).
		Build()

	container.GinEngineRouter.HandleContext(ctx.GinCtx())

	ctx.AssertResponseCode(t, 200)
}

func TestCreateApproval(t *testing.T) {
	container := ittest.NewContainer(t)

	freezeDate := time.Date(2024, 4, 1, 0, 0, 0, 0, timeutil.BangkokLocation())

	t.Run("Should create purchase credit approval success", func(t *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/approvals").
			Body().
			JSON(payment.CreateApprovalReq{
				Category:    payment.Credit,
				Action:      payment.Purchase,
				RequestedBy: "ATAdmin",
				Info: &payment.CreditPurchaseInfoReq{
					IsFreeCredit:     true,
					PurchaseDriverID: "DRV_PATTAYA_ONLINE",
					Amount:           200,
				},
			}).
			Build()

		container.GinEngineRouter.HandleContext(ctx.GinCtx())

		var res payment.ApprovalRes
		ctx.DecodeJSONResponse(&res)
		ctx.AssertResponseCode(t, 200)
		info := res.Info.(*payment.CreditPurchaseInfoRes)
		require.Equal(t, "PURCHASE", info.Action)
		require.Equal(t, "CREDIT", info.Category)
		require.Equal(t, "DRV_PATTAYA_ONLINE", info.DriverID)
		require.Equal(t, types.Money(200), info.Amount)
	})

	t.Run("When create purchase credit with non existing driver id should failed with driver not found", func(t *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/approvals").
			Body().
			JSON(payment.CreateApprovalReq{
				Category:    payment.Credit,
				Action:      payment.Purchase,
				RequestedBy: "ATAdmin",
				Info: &payment.CreditPurchaseInfoReq{
					IsFreeCredit:     true,
					PurchaseDriverID: "DRV_ID_NOT_FOUND",
					Amount:           200,
				},
			}).
			Build()

		container.GinEngineRouter.HandleContext(ctx.GinCtx())

		res := ErrorResponse_Test{}
		ctx.DecodeJSONResponse(&res)
		ctx.AssertResponseCode(t, 400)
		require.Equal(t, "DRIVER_NOT_EXISTS", res.Code)
		require.Equal(t, "driver isn't exists", res.Message)
	})

	t.Run("When create purchase credit with over limit amount should failed with purchase amount more than maximum amount", func(t *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/approvals").
			Body().
			JSON(payment.CreateApprovalReq{
				Category:    payment.Credit,
				Action:      payment.Purchase,
				RequestedBy: "ATAdmin",
				Info: &payment.CreditPurchaseInfoReq{
					IsFreeCredit:     true,
					PurchaseDriverID: "DRV_PATTAYA_ONLINE",
					Amount:           201,
				},
			}).
			Build()

		container.GinEngineRouter.HandleContext(ctx.GinCtx())
		res := ErrorResponse_Test{}
		ctx.DecodeJSONResponse(&res)
		ctx.AssertResponseCode(t, 400)
		require.Equal(t, "FREE_CREDIT_PURCHASE_AMOUNT_MORE_THAN_MAXIMUM", res.Code)
		require.Equal(t, "purchase amount more than maximum amount", res.Message)
	})

	t.Run("Should create void credit and wallet approval success", func(t *testing.T) {
		type testcase struct {
			name               string
			req                payment.CreateApprovalReq
			expectApprovalInfo model.ApprovalInfo
		}

		testcases := []testcase{
			{
				name: "void normal credit transaction",
				req: payment.CreateApprovalReq{
					Category:    payment.Credit,
					Action:      payment.Void,
					RequestedBy: "ATAdmin",
					Info: &payment.CreditVoidInfoReq{
						TransactionID: "NORMAL_CHARGE_TXN",
					},
				},
				expectApprovalInfo: model.NewCreditVoidInfo("NORMAL_CHARGE_TXN", "DRV_PATTAYA_ONLINE", 30.0),
			},
			{
				name: "[ADDITION] void custom transaction",
				req: payment.CreateApprovalReq{
					Category:    payment.Credit,
					Action:      payment.Void,
					RequestedBy: "ATAdmin",
					Info: &payment.CreditVoidInfoReq{
						TransactionID: "CUSTOM_ADDITION_CREDIT_TXN",
					},
				},
				expectApprovalInfo: model.NewCreditVoidInfo("CUSTOM_ADDITION_CREDIT_TXN", "DRV_PATTAYA_ONLINE", 30.0),
			},
			{
				name: "[SUBTRACTION] void custom transaction",
				req: payment.CreateApprovalReq{
					Category:    payment.Credit,
					Action:      payment.Void,
					RequestedBy: "ATAdmin",
					Info: &payment.CreditVoidInfoReq{
						TransactionID: "CUSTOM_SUBTRACTION_CREDIT_TXN",
					},
				},
				expectApprovalInfo: model.NewCreditVoidInfo("CUSTOM_SUBTRACTION_CREDIT_TXN", "DRV_PATTAYA_ONLINE", 30.0),
			},
			{
				name: "void normal wallet transaction",
				req: payment.CreateApprovalReq{
					Category:    payment.Wallet,
					Action:      payment.Void,
					RequestedBy: "ATAdmin",
					Info: &payment.CreditVoidInfoReq{
						TransactionID: "NORMAL_COMPENSATION_TXN",
					},
				},
				expectApprovalInfo: model.NewWalletVoidInfo("NORMAL_COMPENSATION_TXN", "DRV_PATTAYA_ONLINE", 30.0),
			},
			{
				name: "[ADDITION] void custom transaction",
				req: payment.CreateApprovalReq{
					Category:    payment.Wallet,
					Action:      payment.Void,
					RequestedBy: "ATAdmin",
					Info: &payment.CreditVoidInfoReq{
						TransactionID: "CUSTOM_ADDITION_WALLET_TXN",
					},
				},
				expectApprovalInfo: model.NewWalletVoidInfo("CUSTOM_ADDITION_WALLET_TXN", "DRV_PATTAYA_ONLINE", 30.0),
			},
			{
				name: "[SUBTRACTION] void custom transaction",
				req: payment.CreateApprovalReq{
					Category:    payment.Wallet,
					Action:      payment.Void,
					RequestedBy: "ATAdmin",
					Info: &payment.CreditVoidInfoReq{
						TransactionID: "CUSTOM_SUBTRACTION_WALLET_TXN",
					},
				},
				expectApprovalInfo: model.NewWalletVoidInfo("CUSTOM_SUBTRACTION_WALLET_TXN", "DRV_PATTAYA_ONLINE", 30.0),
			},
		}

		for _, tc := range testcases {
			t.Run(tc.name, func(tt *testing.T) {
				ctx := testutil.NewContextWithRecorder()
				ctx.SetPOST("/v1/admin/approvals").
					Body().
					JSON(tc.req).
					Build()

				container.GinEngineRouter.HandleContext(ctx.GinCtx())

				var res payment.ApprovalRes
				ctx.DecodeJSONResponse(&res)
				ctx.AssertResponseCode(tt, http.StatusOK)

				approval, err := container.DataStoreApprovalRepository.Get(context.Background(), res.ApprovalID)
				require.NoError(tt, err)
				require.Equal(tt, res.ApprovalID, approval.ApprovalID)
				require.Equal(tt, tc.expectApprovalInfo, approval.Info)
				require.Equal(tt, model.PendingApproval, approval.Status)
			})
		}
	})

	t.Run("Should create free credit approval without expiration date success", func(t *testing.T) {
		timeutils.FreezeWithTime(freezeDate.Unix() * 1000)
		defer timeutils.Unfreeze()

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/approvals").
			Body().
			JSON(payment.CreateApprovalReq{
				Category:    payment.Credit,
				Action:      payment.Purchase,
				RequestedBy: "ATAdmin",
				Info: &payment.CreditPurchaseInfoReq{
					IsFreeCredit:     true,
					PurchaseDriverID: "DRV_PATTAYA_ONLINE",
					Amount:           200,
				},
			}).
			Build()

		container.GinEngineRouter.HandleContext(ctx.GinCtx())

		var res payment.ApprovalRes
		ctx.DecodeJSONResponse(&res)
		ctx.AssertResponseCode(t, 200)
		info := res.Info.(*payment.CreditPurchaseInfoRes)
		require.Equal(t, "PURCHASE", info.Action)
		require.Equal(t, "CREDIT", info.Category)
		require.Equal(t, "DRV_PATTAYA_ONLINE", info.DriverID)
		require.Equal(t, types.Money(200), info.Amount)
		require.Equal(t, true, info.ExpirationDate.In(timeutil.BangkokLocation()).IsZero())
	})

	t.Run("Should create free credit approval with expiration date success", func(t *testing.T) {
		timeutils.FreezeWithTime(freezeDate.Unix() * 1000)
		defer timeutils.Unfreeze()

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/approvals").
			Body().
			JSON(payment.CreateApprovalReq{
				Category:    payment.Credit,
				Action:      payment.Purchase,
				RequestedBy: "ATAdmin",
				Info: &payment.CreditPurchaseInfoReq{
					IsFreeCredit:     true,
					PurchaseDriverID: "DRV_PATTAYA_ONLINE",
					Amount:           200,
					ExpirationDate:   timeutils.Now(),
				},
			}).
			Build()

		container.GinEngineRouter.HandleContext(ctx.GinCtx())

		var res payment.ApprovalRes
		ctx.DecodeJSONResponse(&res)
		ctx.AssertResponseCode(t, 200)
		info := res.Info.(*payment.CreditPurchaseInfoRes)
		require.Equal(t, "PURCHASE", info.Action)
		require.Equal(t, "CREDIT", info.Category)
		require.Equal(t, "DRV_PATTAYA_ONLINE", info.DriverID)
		require.Equal(t, types.Money(200), info.Amount)
		require.Equal(t, timeutil.DateCeiling(timeutils.Now().In(timeutil.BangkokLocation())), info.ExpirationDate.In(timeutil.BangkokLocation()))
	})

	t.Run("Should fail to create free credit approval when expiration date has passed", func(t *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/approvals").
			Body().
			JSON(payment.CreateApprovalReq{
				Category:    payment.Credit,
				Action:      payment.Purchase,
				RequestedBy: "ATAdmin",
				Info: &payment.CreditPurchaseInfoReq{
					IsFreeCredit:     true,
					PurchaseDriverID: "DRV_PATTAYA_ONLINE",
					Amount:           200,
					ExpirationDate:   timeutils.Now().AddDate(0, 0, -1),
				},
			}).
			Build()

		container.GinEngineRouter.HandleContext(ctx.GinCtx())
		ctx.AssertResponseCode(t, 400)
	})

	t.Run("Should fail to create free credit approval when fre credit with amount greater than 200", func(t *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/approvals").
			Body().
			JSON(payment.CreateApprovalReq{
				Category:    payment.Credit,
				Action:      payment.Purchase,
				RequestedBy: "ATAdmin",
				Info: &payment.CreditPurchaseInfoReq{
					IsFreeCredit:     true,
					PurchaseDriverID: "DRV_PATTAYA_ONLINE",
					Amount:           201,
					ExpirationDate:   timeutils.Now(),
				},
			}).
			Build()

		container.GinEngineRouter.HandleContext(ctx.GinCtx())
		ctx.AssertResponseCode(t, 400)
	})
}

func TestBulkApprovalFreeCredit(t *testing.T) {
	makeReq := func(content string) (*testutil.GinContextWithRecorder, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/bulk/approvals/free_credit")
		gctx.Body().MultipartForm().
			File("file", "file.csv", content).
			String("requestedBy", "adminnaja").
			Build()
		return gctx, gctx.ResponseRecorder
	}

	freezeDate := time.Date(2024, 3, 1, 0, 0, 0, 0, timeutil.BangkokLocation())

	t.Run("bulk from csv success", func(t *testing.T) {
		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,
DRV_CHIANG_MAI_2_ONLINE,150,`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)
		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)
		require.Equal(t, 2, len(resp.Success))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 1, c1)

		c2, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_CHIANG_MAI_2_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})
		require.NoError(t, err)
		require.Equal(t, 1, c2)
	})

	t.Run("should fail when bulk import free credit with expiration date value as invalid", func(t *testing.T) {
		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,29May2024`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 0, len(resp.Success))
		require.Equal(t, 1, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 0, c1)
	})

	t.Run("should fail when bulk import free credit with expiration date value in the past", func(t *testing.T) {
		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,01/01/2001`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 0, len(resp.Success))
		require.Equal(t, 1, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 0, c1)
	})

	// 31/06/2024 (30 day in this month)
	t.Run("should fail when bulk import free credit with expiration date that day out of range in month", func(t *testing.T) {
		timeutils.FreezeWithTime(freezeDate.Unix() * 1000)
		defer timeutils.Unfreeze()

		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,31/06/2024`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 0, len(resp.Success))
		require.Equal(t, 1, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 0, c1)
	})

	t.Run("should success when bulk import free credit with expiration date value in today", func(t *testing.T) {
		timeutils.FreezeWithTime(freezeDate.Unix() * 1000)
		defer timeutils.Unfreeze()

		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,01/03/2024`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 1, len(resp.Success))
		require.Equal(t, 0, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 1, c1)
	})

	t.Run("should success when bulk import free credit without expiration date", func(t *testing.T) {
		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 1, len(resp.Success))
		require.Equal(t, 0, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 1, c1)
	})

	t.Run("should success when bulk import free credit with expiration date value and duplicate driver", func(t *testing.T) {
		timeutils.FreezeWithTime(freezeDate.Unix() * 1000)
		defer timeutils.Unfreeze()

		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,01/03/2024
DRV_PATTAYA_ONLINE,200,01/03/2024`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 2, len(resp.Success))
		require.Equal(t, 0, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 2, c1)
	})

	t.Run("should success when bulk import free credit with diff expiration date value and duplicate driver", func(t *testing.T) {
		timeutils.FreezeWithTime(freezeDate.Unix() * 1000)
		defer timeutils.Unfreeze()

		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,01/03/2024
DRV_PATTAYA_ONLINE,200,`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 2, len(resp.Success))
		require.Equal(t, 0, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 2, c1)
	})

	t.Run("should success when bulk import free credit", func(t *testing.T) {
		timeutils.FreezeWithTime(freezeDate.Unix() * 1000)
		defer timeutils.Unfreeze()

		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,100,01/03/2024
DRV_PATTAYA_ONLINE,200,`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)

		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)

		require.Equal(t, 2, len(resp.Success))
		require.Equal(t, 0, len(resp.Fail))

		ctx := context.Background()
		c1, err := container.DataStoreApprovalRepository.Count(ctx, repository.ApprovalQuery{
			PurchaseDriverID: "DRV_PATTAYA_ONLINE",
			Category:         model.CreditCategory,
			Action:           model.PurchaseAction,
			Status:           model.PendingApproval,
			IsFree:           "y",
		})

		require.NoError(t, err)
		require.Equal(t, 2, c1)
	})

	t.Run("should fail when bulk import fre credit with amount greater than 200", func(t *testing.T) {
		container := ittest.NewContainer(t)
		csvContent := `driverId, amount,expiration date
DRV_PATTAYA_ONLINE,210,`
		req, _ := makeReq(csvContent)
		container.GinEngineRouter.HandleContext(req.GinCtx())
		req.AssertResponseCode(t, http.StatusOK)
		var resp payment.BulkApprovalFreeCreditResp
		req.DecodeJSONResponse(&resp)
		require.Equal(t, 0, len(resp.Success))
		require.Equal(t, 1, len(resp.Fail))
	})
}

func Test_ApproveApproval(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)
	if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_approval"); err != nil {
		panic(err)
	}

	t.Run("Should approve void credit and wallet approval success", func(tt *testing.T) {
		type response struct {
			Transactions []string `json:"approvedTransactions"`
		}

		type expect struct {
			credit   types.Money
			wallet   types.Money
			voidTxns []model.Transaction
		}

		type testcase struct {
			name         string
			approvalID   string
			beforeCredit types.Money
			beforeWallet types.Money
			expect       expect
		}

		testcases := []testcase{
			{
				name:         "void normal credit transaction",
				approvalID:   "APPROVAL_VOID_NORMAL_CHARGE_CREDIT",
				beforeCredit: 200.0,
				beforeWallet: 0.0,
				expect: expect{
					credit: 230.0,
					wallet: 0.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.VoidReturnCreditTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "NORMAL_CHARGE_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[ADDITION] void custom transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_ADDITION_CREDIT_TXN",
				beforeCredit: 200.0,
				beforeWallet: 0.0,
				expect: expect{
					credit: 170.0,
					wallet: 0.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.VoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_ADDITION_CREDIT_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[SUBTRACTION] void custom transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_SUBTRACTION_CREDIT_TXN",
				beforeCredit: 200.0,
				beforeWallet: 0.0,
				expect: expect{
					credit: 230.0,
					wallet: 0.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.VoidReturnCreditTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_SUBTRACTION_CREDIT_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "void normal wallet transaction",
				approvalID:   "APPROVAL_VOID_NORMAL_COMPENSATION_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 0.0,
					wallet: 170.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.WalletTransactionCategory,
								Type:     model.DeductVoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "NORMAL_COMPENSATION_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[ADDITION] void custom transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_ADDITION_WALLET_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 0.0,
					wallet: 170.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.WalletTransactionCategory,
								Type:     model.DeductVoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_ADDITION_WALLET_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[SUBTRACTION] void custom subtraction wallet transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_SUBTRACTION_WALLET_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 0.0,
					wallet: 230.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.WalletTransactionCategory,
								Type:     model.VoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_SUBTRACTION_WALLET_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "void purchase custom transaction",
				approvalID:   "APPROVAL_VOID_PURCHASE_CUSTOM_SUBTRACTION_WALLET_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 30.0,
					wallet: 170.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidPurchaseTransactionAction,
							Info: model.TransactionInfo{
								Category: model.WalletTransactionCategory,
								Type:     model.DeductVoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_ADDITION_WALLET_TXN",
								Amount:   30.0,
							},
						},
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidPurchaseTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.PurchaseTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "void purchase custom subtraction credit transaction",
				approvalID:   "APPROVAL_VOID_PURCHASE_CUSTOM_SUBTRACTION_CREDIT_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 0.0,
					wallet: 200.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidPurchaseTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.VoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_ADDITION_CREDIT_TXN",
								Amount:   30.0,
							},
						},
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidPurchaseTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.OutstandingTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "",
								Amount:   30.0,
							},
						},
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.SuccessTransactionStatus,
							Action:  model.VoidPurchaseTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.PurchaseTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "",
								Amount:   30.0,
							},
						},
					},
				},
			},
		}

		for _, tc := range testcases {
			tt.Run(tc.name, func(ttt *testing.T) {
				// prepare driver credit/wallet
				dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
				require.NoError(ttt, err)
				dt.PurchaseCreditBalance = tc.beforeCredit
				dt.WalletBalance = tc.beforeWallet
				err = container.DataStoreDriverTransactionRepository.Update(context.Background(), dt)
				require.NoError(ttt, err)

				ctx := testutil.NewContextWithRecorder()
				ctx.SetPUT("%s", fmt.Sprintf("/v1/admin/approvals/%s/approve", tc.approvalID))
				ctx.Send(container.GinEngineRouter)

				var res response
				ctx.AssertResponseCode(ttt, http.StatusOK)
				ctx.DecodeJSONResponse(&res)

				require.Equal(ttt, len(tc.expect.voidTxns), len(res.Transactions))
				for i, id := range res.Transactions {
					txn, err := container.DataStoreTransactionRepository.FindByID(context.Background(), id)
					require.NoError(ttt, err)

					expect := tc.expect.voidTxns[i]
					require.Equal(ttt, expect.Channel, txn.Channel)
					require.Equal(ttt, expect.Status, txn.Status)
					require.Equal(ttt, expect.Action, txn.Action)
					require.Equal(ttt, expect.Info.Category, txn.Info.Category)
					require.Equal(ttt, expect.Info.Type, txn.Info.Type)
					require.Equal(ttt, expect.Info.DriverID, txn.Info.DriverID)
					require.Equal(ttt, expect.Info.RefID, txn.Info.RefID)
					require.Equal(ttt, expect.Info.Amount, txn.Info.Amount)
				}

				dt, err = container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
				require.NoError(ttt, err)
				require.Equal(ttt, tc.expect.credit, dt.CreditBalance())
				require.Equal(ttt, tc.expect.wallet, dt.WalletBalance)
			})
		}
	})
}

func Test_RejectApproval(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)
	if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_approval"); err != nil {
		panic(err)
	}

	t.Run("Should reject void credit and wallet approval success", func(tt *testing.T) {
		type response struct {
			Transactions []string `json:"rejectedTransactions"`
		}

		type expect struct {
			credit   types.Money
			wallet   types.Money
			voidTxns []model.Transaction
		}

		type testcase struct {
			name         string
			approvalID   string
			beforeCredit types.Money
			beforeWallet types.Money
			expect       expect
		}

		testcases := []testcase{
			{
				name:         "void normal credit transaction",
				approvalID:   "APPROVAL_VOID_NORMAL_CHARGE_CREDIT",
				beforeCredit: 200.0,
				beforeWallet: 0.0,
				expect: expect{
					credit: 200.0,
					wallet: 0.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.RejectedTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.VoidReturnCreditTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "NORMAL_CHARGE_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[ADDITION] void custom transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_ADDITION_CREDIT_TXN",
				beforeCredit: 200.0,
				beforeWallet: 0.0,
				expect: expect{
					credit: 200.0,
					wallet: 0.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.RejectedTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.VoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_ADDITION_CREDIT_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[SUBTRACTION] void custom transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_SUBTRACTION_CREDIT_TXN",
				beforeCredit: 200.0,
				beforeWallet: 0.0,
				expect: expect{
					credit: 200.0,
					wallet: 0.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.RejectedTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.CreditTransactionCategory,
								Type:     model.VoidReturnCreditTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_SUBTRACTION_CREDIT_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "void normal wallet transaction",
				approvalID:   "APPROVAL_VOID_NORMAL_COMPENSATION_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 0.0,
					wallet: 200.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.RejectedTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.WalletTransactionCategory,
								Type:     model.DeductVoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "NORMAL_COMPENSATION_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[ADDITION] void custom transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_ADDITION_WALLET_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 0.0,
					wallet: 200.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.RejectedTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.WalletTransactionCategory,
								Type:     model.DeductVoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_ADDITION_WALLET_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
			{
				name:         "[SUBTRACTION] void custom transaction",
				approvalID:   "APPROVAL_VOID_CUSTOM_SUBTRACTION_WALLET_TXN",
				beforeCredit: 0.0,
				beforeWallet: 200.0,
				expect: expect{
					credit: 0.0,
					wallet: 200.0,
					voidTxns: []model.Transaction{
						{
							Channel: model.AdminTransactionChannel,
							Status:  model.RejectedTransactionStatus,
							Action:  model.VoidTransactionAction,
							Info: model.TransactionInfo{
								Category: model.WalletTransactionCategory,
								Type:     model.VoidTransactionType,
								DriverID: "DRV_PATTAYA_ONLINE",
								RefID:    "CUSTOM_SUBTRACTION_WALLET_TXN",
								Amount:   30.0,
							},
						},
					},
				},
			},
		}

		for _, tc := range testcases {
			tt.Run(tc.name, func(ttt *testing.T) {
				// prepare driver credit/wallet
				dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
				require.NoError(ttt, err)
				dt.PurchaseCreditBalance = tc.beforeCredit
				dt.WalletBalance = tc.beforeWallet
				err = container.DataStoreDriverTransactionRepository.Update(context.Background(), dt)
				require.NoError(ttt, err)

				ctx := testutil.NewContextWithRecorder()
				ctx.SetPUT("%s", fmt.Sprintf("/v1/admin/approvals/%s/reject", tc.approvalID))
				ctx.Send(container.GinEngineRouter)

				var res response
				ctx.AssertResponseCode(ttt, http.StatusOK)
				ctx.DecodeJSONResponse(&res)

				require.Equal(ttt, len(tc.expect.voidTxns), len(res.Transactions))
				for i, id := range res.Transactions {
					txn, err := container.DataStoreTransactionRepository.FindByID(context.Background(), id)
					require.NoError(ttt, err)

					expect := tc.expect.voidTxns[i]
					require.Equal(ttt, expect.Channel, txn.Channel)
					require.Equal(ttt, expect.Status, txn.Status)
					require.Equal(ttt, expect.Action, txn.Action)
					require.Equal(ttt, expect.Info.Category, txn.Info.Category)
					require.Equal(ttt, expect.Info.Type, txn.Info.Type)
					require.Equal(ttt, expect.Info.DriverID, txn.Info.DriverID)
					require.Equal(ttt, expect.Info.RefID, txn.Info.RefID)
					require.Equal(ttt, expect.Info.Amount, txn.Info.Amount)
				}

				dt, err = container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
				require.NoError(ttt, err)
				require.Equal(ttt, tc.expect.credit, dt.CreditBalance())
				require.Equal(ttt, tc.expect.wallet, dt.WalletBalance)
			})
		}
	})
}

func Test_ApproveApprovalThenRebalanceInstallment(t *testing.T) {
	targetDriverID := "TEST_DRIVER_ID"
	timeNow := timeutil.BangkokNow()

	type response struct {
		Transactions []string `json:"approvedTransactions"`
	}

	type expect struct {
		credit            types.Money
		wallet            types.Money
		installmentAmount types.Money
		installments      []model.Installment
		transactions      []model.Transaction
	}

	type testData struct {
		name                    string
		beforeCredit            types.Money
		beforeWallet            types.Money
		beforeInstallmentAmount types.Money
		approvalInfo            model.ApprovalInfo
		transaction             *model.Transaction
		installments            []model.Installment
		expect                  expect
	}

	testSet := []testData{
		{
			name:                    "approve_void_purchase_must_rebalance_installment",
			beforeCredit:            -100,
			beforeWallet:            100,
			beforeInstallmentAmount: -100,
			approvalInfo:            model.NewCreditVoidPurchaseInfo("ADD_WALLET_TXN", targetDriverID, targetDriverID, 100),
			transaction: model.NewTransaction(
				"ADD_WALLET_TXN",
				model.AdminTransactionChannel,
				model.WalletTopUpTransactionAction,
				model.SuccessTransactionStatus, model.TransactionInfo{
					DriverID: targetDriverID,
					Type:     "NEW_SUB_TYPE",
					Category: model.WalletTransactionCategory,
					Operator: model.AdditionOperator,
					Amount:   100,
				}),
			installments: []model.Installment{
				{
					ProductName:       "INSTALLMENT_PRODUCT",
					DriverID:          targetDriverID,
					Start:             timeNow.AddDate(0, 0, -1),
					End:               timeNow.AddDate(0, 0, 1),
					InitialAmount:     300,
					OutstandingAmount: 300,
					OverdueAmount:     100,
					Status:            model.InstallmentActive,
					DPD:               1,
					InstallmentLogs: []model.InstallmentLog{
						{
							Amount:                   100,
							InstallmentPaymentStatus: model.InstallmentPaymentStatusPending,
							CreatedAt:                timeNow.AddDate(0, 0, -1),
						},
					},
				},
			},
			expect: expect{
				transactions: []model.Transaction{
					{
						Channel: model.AdminTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.VoidPurchaseTransactionAction,
						Info: model.TransactionInfo{
							DriverID: targetDriverID,
							Category: model.WalletTransactionCategory,
							Type:     model.DeductVoidTransactionType,
							RefID:    "ADD_WALLET_TXN",
							Amount:   types.NewMoney(100),
						},
					},
					{
						Channel: model.AdminTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.VoidPurchaseTransactionAction,
						Info: model.TransactionInfo{
							DriverID: targetDriverID,
							Category: model.CreditTransactionCategory,
							Type:     model.PurchaseTransactionType,
							Amount:   types.NewMoney(100),
						},
					},
				}, // VOID_PURCHASE,WALLET,DEDUCT_VOID and VOID_PURHCASE,CREDIT,PURCHASE
				installments: []model.Installment{
					{
						OutstandingAmount: 200,
						OverdueAmount:     0,
						DPD:               0,
						InstallmentLogs: []model.InstallmentLog{
							{
								InstallmentPaymentStatus: model.InstallmentPaymentStatusSuccessful,
								ActualAmountLogs: []model.ActualAmountLog{
									{
										ActualAmount: 100,
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name:                    "approve_purchase_must_rebalance_installment",
			beforeWallet:            100,
			beforeCredit:            -100,
			beforeInstallmentAmount: -100,
			approvalInfo:            model.NewCreditPurchaseInfo(targetDriverID, crypt.EncryptedString("PURCHASE_TXN_REF_ID"), 100),
			transaction: model.NewTransaction(
				"PURCHASE_CREDIT_TXN",
				model.AdminTransactionChannel,
				model.PurchaseTransactionAction,
				model.SuccessTransactionStatus, model.TransactionInfo{
					DriverID: targetDriverID,
					Type:     "NEW_SUB_TYPE",
					Category: model.WalletTransactionCategory,
					Operator: model.AdditionOperator,
					Amount:   100,
				}),
			installments: []model.Installment{
				{
					ProductName:       "INSTALLMENT_PRODUCT",
					DriverID:          targetDriverID,
					Start:             timeNow.AddDate(0, 0, -1),
					End:               timeNow.AddDate(0, 0, 1),
					InitialAmount:     300,
					OutstandingAmount: 300,
					OverdueAmount:     100,
					Status:            model.InstallmentActive,
					DPD:               1,
					InstallmentLogs: []model.InstallmentLog{
						{
							Amount:                   100,
							InstallmentPaymentStatus: model.InstallmentPaymentStatusPending,
							CreatedAt:                timeNow.AddDate(0, 0, -1),
						},
					},
				},
			},
			expect: expect{
				transactions: []model.Transaction{
					{
						Channel: model.AdminTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchaseTransactionAction,
						Info: model.TransactionInfo{
							DriverID:   targetDriverID,
							Category:   model.CreditTransactionCategory,
							Type:       model.PurchaseTransactionType,
							TransRefID: "PURCHASE_TXN_REF_ID",
							Amount:     types.NewMoney(100),
						},
					},
				},
				installments: []model.Installment{
					{
						OutstandingAmount: 200,
						OverdueAmount:     0,
						DPD:               0,
						InstallmentLogs: []model.InstallmentLog{
							{
								InstallmentPaymentStatus: model.InstallmentPaymentStatusSuccessful,
								ActualAmountLogs: []model.ActualAmountLog{
									{
										ActualAmount: 100,
									},
								},
							},
						},
					},
				},
				credit: 0,
				wallet: 100,
			},
		},
		{
			name:                    "approve_purchase_free_credit_must_rebalance_installment",
			beforeWallet:            100,
			beforeCredit:            -100,
			beforeInstallmentAmount: -100,
			approvalInfo:            model.NewCreditPurchaseFreeInfo(targetDriverID, 100, time.Time{}),
			transaction: model.NewTransaction(
				"PURCHASE_CREDIT_TXN",
				model.AdminTransactionChannel,
				model.PurchaseTransactionAction,
				model.SuccessTransactionStatus,
				model.TransactionInfo{
					DriverID: targetDriverID,
					Type:     "NEW_SUB_TYPE",
					Category: model.WalletTransactionCategory,
					Operator: model.AdditionOperator,
					Amount:   100,
				}),
			installments: []model.Installment{
				{
					ProductName:       "INSTALLMENT_PRODUCT",
					DriverID:          targetDriverID,
					Start:             timeNow.AddDate(0, 0, -1),
					End:               timeNow.AddDate(0, 0, 1),
					InitialAmount:     300,
					OutstandingAmount: 300,
					OverdueAmount:     100,
					Status:            model.InstallmentActive,
					DPD:               1,
					InstallmentLogs: []model.InstallmentLog{
						{
							Amount:                   100,
							InstallmentPaymentStatus: model.InstallmentPaymentStatusPending,
							CreatedAt:                timeNow.AddDate(0, 0, -1),
						},
					},
				},
			},
			expect: expect{
				transactions: []model.Transaction{
					{
						Channel: model.AdminTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchaseTransactionAction,
						Info: model.TransactionInfo{
							DriverID:   targetDriverID,
							Category:   model.CreditTransactionCategory,
							Type:       model.PurchaseTransactionType,
							Amount:     types.NewMoney(100),
							CreditType: model.FreeTransactionCreditType,
						},
					},
				},
				installments: []model.Installment{
					{
						OutstandingAmount: 200,
						OverdueAmount:     0,
						DPD:               0,
						InstallmentLogs: []model.InstallmentLog{
							{
								InstallmentPaymentStatus: model.InstallmentPaymentStatusSuccessful,
								ActualAmountLogs: []model.ActualAmountLog{
									{
										ActualAmount: 100,
									},
								},
							},
						},
					},
				},
				credit: 0,
				wallet: 100,
			},
		},
	}

	container := ittest.NewContainer(t)
	if err := container.DataStoreDriverTransactionRepository.Create(context.Background(), &model.DriverTransaction{
		DriverID: targetDriverID,
	}); err != nil {
		require.NoError(t, err)
	}
	container.StubGRPCProductService.SetGetProductWithPriorityGroupResponse("", &inventoryv1.GetProductWithPriorityGroupResponse{})

	t.Parallel()
	for index := range testSet {
		tc := testSet[index]
		t.Run(tc.name, func(tt *testing.T) {
			// prepare driver credit/wallet
			{
				dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), targetDriverID)
				require.NoError(tt, err)
				dt.PurchaseCreditBalance = tc.beforeCredit
				dt.WalletBalance = tc.beforeWallet
				dt.InstallmentAmount = tc.beforeInstallmentAmount
				err = container.DataStoreDriverTransactionRepository.Update(context.Background(), dt)
				require.NoError(tt, err)
			}
			targetApprovalID := fmt.Sprintf("APPROVAL_ID_%d", index)
			// prepare approval transaction
			{
				newApproval := model.NewApproval(targetApprovalID, "<EMAIL>", tc.approvalInfo)
				err := container.DataStoreApprovalRepository.Create(context.Background(), newApproval)
				require.NoError(tt, err)
				err = container.DataStoreTransactionRepository.Create(context.Background(), tc.transaction)
				require.NoError(tt, err)
			}
			// prepare installment
			generatedSku := fmt.Sprintf("TEST_APPROVE_EFFECT_INSTALLMENT_%d", index)
			{
				installmentInterfaces := make([]any, len(tc.installments))
				for index := range tc.installments {
					tc.installments[index].SKU = generatedSku
					installmentInterfaces[index] = tc.installments[index]
				}
				err := container.InstallmentDataStore.InsertMany(context.Background(), installmentInterfaces)
				require.NoError(tt, err)
			}

			ctx := testutil.NewContextWithRecorder()
			ctx.SetPUT("%s", fmt.Sprintf("/v1/admin/approvals/%s/approve", targetApprovalID))
			ctx.Send(container.GinEngineRouter)

			var res response
			ctx.AssertResponseCode(tt, http.StatusOK)
			ctx.DecodeJSONResponse(&res)
			require.Len(tt, res.Transactions, len(tc.expect.transactions))
			resultTransactions, err := container.DataStoreTransactionRepository.FindByIDs(ctx.GinCtx(), res.Transactions)
			require.NoError(tt, err)
			for index := range tc.expect.transactions {
				expect := tc.expect.transactions[index]
				actual := resultTransactions[index]
				require.Equal(tt, expect.Channel, actual.Channel)
				require.Equal(tt, expect.Status, actual.Status)
				require.Equal(tt, expect.Action, actual.Action)
				require.Equal(tt, expect.Info.DriverID, actual.Info.DriverID)
				require.Equal(tt, expect.Info.Category, actual.Info.Category)
				require.Equal(tt, expect.Info.Type, actual.Info.Type)
				require.Equal(tt, expect.Info.CreditType, actual.Info.CreditType)
				require.Equal(tt, expect.Info.RefID, actual.Info.RefID)
				if expect.Info.TransRefID != "" {
					require.Equal(tt, expect.Info.TransRefID, actual.Info.TransRefID)
				}
				require.Equal(tt, expect.Info.Amount, actual.Info.Amount)
			}
			// check driver transaction
			{
				dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), targetDriverID)
				require.NoError(tt, err)
				require.Equal(tt, dt.CreditBalance(), tc.expect.credit)
				require.Equal(tt, dt.WalletBalance, tc.expect.wallet)
			}
			// check installments
			{
				var installments []model.Installment
				err := container.InstallmentDataStore.Find(context.Background(), bson.M{"sku": generatedSku}, 0, 0, &installments)
				require.NoError(tt, err)
				require.Len(tt, installments, len(tc.expect.installments))
				for index := range tc.expect.installments {
					expectInst := tc.expect.installments[index]
					targetInst := installments[index]
					require.Equal(tt, expectInst.OutstandingAmount, targetInst.OutstandingAmount)
					require.Equal(tt, expectInst.OverdueAmount, targetInst.OverdueAmount)
					require.Equal(tt, expectInst.DPD, targetInst.DPD)
					require.Len(tt, targetInst.InstallmentLogs, len(expectInst.InstallmentLogs))
					for index := range expectInst.InstallmentLogs {
						require.Equal(tt, expectInst.InstallmentLogs[index].InstallmentPaymentStatus, targetInst.InstallmentLogs[index].InstallmentPaymentStatus)
						expectALogs := expectInst.InstallmentLogs[index].ActualAmountLogs
						targetALogs := targetInst.InstallmentLogs[index].ActualAmountLogs
						require.Len(tt, targetALogs, len(expectALogs))
						for index := range expectALogs {
							expectALog := expectALogs[index]
							actualALog := targetALogs[index]
							require.Equal(tt, expectALog.ActualAmount, actualALog.ActualAmount)
						}
					}
				}
			}
		})
	}
}

type ErrorResponse_Test struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}
