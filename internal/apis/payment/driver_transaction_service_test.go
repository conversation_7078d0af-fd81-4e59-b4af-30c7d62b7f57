package payment

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor/mock_fraudadvisor"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestWithdrawWallet(t *testing.T) {
	t.<PERSON>()
	bInfo := model.WithdrawInfo{
		Account:       "A",
		BankName:      "B",
		AccountHolder: "C",
	}

	createDriver := func(driverID string, withdrawalCount int) *model.Driver {
		return &model.Driver{
			DriverID:        driverID,
			BaseDriver:      model.BaseDriver{ProfileStatus: model.ProfileStatusCompleted},
			WithdrawalQuota: model.NewWithdrawalQuota(withdrawalCount, config.PaymentConfig{WalletWithdrawMaximumReq: 2}),
		}
	}

	t.Run("should success", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 0)
		walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(0.0)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverRepo.EXPECT().
			SetWithdrawalQuota(gomock.Any(), expectedDriverID, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
				require.Equal(tt, 2, quota.MaxFreeQuotaToday)
				require.Equal(tt, 1, quota.FreeQuotaUsedToday)
				return nil
			})

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(nil, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 0, tfs.UnusualScore)
				return nil
			}).AnyTimes()

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		driverTrans, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.Nil(tt, err)
		require.NotNil(tt, driverTrans)
		require.NotNil(tt, transactions)
		require.Len(tt, transactions, 1)
		require.Equal(tt, types.NewMoney(100.0), driverTrans.WalletBalance)
		require.Equal(tt, model.PendingTransactionStatus, transactions[0].Status)
	})

	t.Run("should charge fee credit if requesting withdrawal transaction is exceeded", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionServiceWithConfig(tt, config.PaymentConfig{
			WalletWithdrawMinimumAmount:   200,
			ProfileStatusInfoExpirePeriod: 7,
			WithdrawalChargeFeeEnabled:    true,
			WithdrawalFee:                 types.Money(2.0),
			WalletWithdrawMaximumReq:      2,
		})
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 2)
		walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(2.0)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.transactionRepo.EXPECT().
			FindWithdrawByID(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]model.Transaction{}, nil).Times(0)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(nil, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 0, tfs.UnusualScore)
				return nil
			}).AnyTimes()

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		driverTrans, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.Nil(tt, err)
		require.NotNil(tt, driverTrans)
		require.NotNil(tt, transactions)
		require.Len(tt, transactions, 2)
		require.Equal(tt, types.NewMoney(100.0), driverTrans.WalletBalance)
		require.Equal(tt, types.NewMoney(0), driverTrans.CreditBalance())
		require.Equal(tt, model.PendingTransactionStatus, transactions[0].Status)
		require.Equal(tt, model.SuccessTransactionStatus, transactions[1].Status)
	})

	t.Run("should not charge fee credit if requesting withdrawal transaction is exceeded and fee paymentConfig is 0", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionServiceWithConfig(tt, config.PaymentConfig{
			WalletWithdrawMinimumAmount:   200,
			ProfileStatusInfoExpirePeriod: 7,
			WithdrawalChargeFeeEnabled:    true,
			WithdrawalFee:                 types.Money(0.0),
			WalletWithdrawMaximumReq:      2,
		})
		defer finish()
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 2)
		walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(0.0)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.transactionRepo.EXPECT().
			FindWithdrawByID(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]model.Transaction{}, nil).Times(0)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(nil, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 0, tfs.UnusualScore)
				return nil
			}).AnyTimes()

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		driverTrans, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.Nil(tt, err)
		require.NotNil(tt, driverTrans)
		require.NotNil(tt, transactions)
		require.Len(tt, transactions, 1)
		require.Equal(tt, types.NewMoney(100.0), driverTrans.WalletBalance)
		require.Equal(tt, model.PendingTransactionStatus, transactions[0].Status)
	})

	t.Run("should return ErrorCreditOutstanding when requesting withdrawal transaction and credit outstanding", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionServiceWithConfig(tt, config.PaymentConfig{
			WalletWithdrawMinimumAmount:       200,
			ProfileStatusInfoExpirePeriod:     7,
			WithdrawalChargeFeeEnabled:        true,
			WithdrawalFee:                     types.Money(2.0),
			WalletWithdrawMaximumReq:          2,
			AllowWithdrawalWithNegativeCredit: false,
		})
		defer finish()
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 2)
		walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(1.0)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.transactionRepo.EXPECT().
			FindWithdrawByID(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]model.Transaction{}, nil).Times(0)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil).Times(0)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil).Times(0)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(nil, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 0, tfs.UnusualScore)
				return nil
			}).AnyTimes()

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.Error(tt, err)
		require.Equal(tt, model.ErrorCreditOutstanding, err)
	})

	t.Run("should return Error when GetProfileWithLatestWithdrawalQuota error", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionServiceWithConfig(tt, config.PaymentConfig{
			WalletWithdrawMinimumAmount:   200,
			WalletWithdrawMaximumReq:      2,
			ProfileStatusInfoExpirePeriod: 7,
			WithdrawalChargeFeeEnabled:    true,
			WithdrawalFee:                 types.Money(2.0),
		})
		defer finish()
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(nil, errors.New("an error occurred"))

		deps.transactionRepo.EXPECT().
			FindWithdrawByID(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]model.Transaction{}, nil).Times(0)

		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.Error(tt, err)
	})

	t.Run("should calculate unusual score", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 0)
		walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(0.0)
		latestTxnTime, _ := time.Parse(time.DateOnly, "2020-09-20")

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverRepo.EXPECT().
			SetWithdrawalQuota(gomock.Any(), expectedDriverID, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
				require.Equal(tt, 2, quota.MaxFreeQuotaToday)
				require.Equal(tt, 1, quota.FreeQuotaUsedToday)
				return nil
			})

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(&model.Transaction{
				CreatedAt: latestTxnTime,
			}, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, latestTxnTime, gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{
				{
					Score: 2,
				},
				{
					Score: 1,
				},
			}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 3, tfs.UnusualScore)
				return nil
			}).
			AnyTimes()

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		driverTrans, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.Nil(tt, err)
		require.NotNil(tt, driverTrans)
		require.NotNil(tt, transactions)
		require.Len(tt, transactions, 1)
		require.Equal(tt, types.NewMoney(100.0), driverTrans.WalletBalance)
		require.Equal(tt, model.PendingTransactionStatus, transactions[0].Status)
	})

	t.Run("should error if amount less than paymentConfig minimum amount", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionServiceWithConfig(tt, config.PaymentConfig{WalletWithdrawMinimumAmount: 200})
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 0)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 100, bInfo)

		require.NotNil(tt, err)
		require.IsType(tt, &ErrWalletWithdrawAmountLessThanMinimum{}, err)
	})

	t.Run("should error if amount higher than wallet amount", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 0)
		walletBalance, creditBalance := types.NewMoney(100.0), types.NewMoney(0.0)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(nil, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 0, tfs.UnusualScore)
				return nil
			}).AnyTimes()

		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.NotNil(tt, err)
		require.IsType(tt, model.ErrorWalletNotEnough, err)
	})

	t.Run("should error if request amount exceed limit", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionServiceWithConfig(tt, config.PaymentConfig{
			WalletWithdrawMinimumAmount: 200,
			WalletWithdrawMaximumReq:    1,
		})
		defer finish()

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 2)

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{}, nil)

		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 220, bInfo)

		require.NotNil(tt, err)
		require.IsType(tt, &ErrWalletWithdrawReqExceedLimit{}, err)
	})

	t.Run("should error if credit is outstanding while disable allow flag", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 0)
		walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(-10.0)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.NotNil(tt, err)
		require.Equal(tt, model.ErrorCreditOutstanding, err)
	})

	t.Run("should not return error if cannot calculate unusual score but can still withdraw wallet", func(tt *testing.T) {
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		driv := createDriver(expectedDriverID, 0)
		walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(0.0)
		latestTxnTime, _ := time.Parse(time.DateOnly, "2020-09-20")

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driv).
			Return(false, nil).
			AnyTimes()

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driv, nil)

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverRepo.EXPECT().
			SetWithdrawalQuota(gomock.Any(), expectedDriverID, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
				require.Equal(tt, 2, quota.MaxFreeQuotaToday)
				require.Equal(tt, 1, quota.FreeQuotaUsedToday)
				return nil
			})

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(&model.Transaction{
				CreatedAt: latestTxnTime,
			}, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, latestTxnTime, gomock.Any()).
			Return(nil, errors.New("call fraud advisor fail")).
			AnyTimes()

		driverTrans, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bInfo)

		require.Nil(tt, err)
		require.NotNil(tt, driverTrans)
		require.NotNil(tt, transactions)
		require.Len(tt, transactions, 1)
		require.Equal(tt, types.NewMoney(100.0), driverTrans.WalletBalance)
		require.Equal(tt, model.PendingTransactionStatus, transactions[0].Status)
	})
}

func TestAddDriverTransaction(t *testing.T) {
	t.Parallel()

	t.Run("should success with commission and withholding transactions", func(tt *testing.T) {
		api, deps, finish := newDriverTransactionService(t)
		defer finish()

		ctx := context.Background()
		expectDriverID, expectOrderID, expectTripID := "driver-1", "order-1", "trip-1"
		expectedInfos := []model.TransactionInfo{
			*model.NewCommissionCreditTransactionInfo(expectDriverID, expectOrderID, expectTripID, 20, ""),
			*model.NewWithholdingCreditTransactionInfo(expectDriverID, expectOrderID, expectTripID, 5),
		}

		driverTrans := model.NewDriverTransaction(expectDriverID)

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		_, actualTrans, err := api.AddDriverTransaction(
			ctx,
			*driverTrans,
			model.SystemTransactionChannel,
			model.CommissionDeductionTransactionAction,
			model.SuccessTransactionStatus,
			expectedInfos,
		)

		require.Nil(tt, err)

		commissionActual := model.TransactionsFilter(actualTrans, model.TransactionConditionByType(model.CommissionTransactionType))
		require.Len(tt, commissionActual, 1)

		withholdingActual := model.TransactionsFilter(actualTrans, model.TransactionConditionByType(model.WithholdingTransactionType))
		require.Len(tt, withholdingActual, 1)
	})

	t.Run("should create new driver transaction when TransactionOptions.TransactionRefID is not empty", func(tt *testing.T) {
		api, deps, finish := newDriverTransactionService(t)
		defer finish()

		ctx := context.Background()
		expectDriverID, expectOrderID, expectTripID := "driver-1", "order-1", "trip-1"
		expectedInfos := []model.TransactionInfo{
			*model.NewCommissionCreditTransactionInfo(expectDriverID, expectOrderID, expectTripID, 20, ""),
			*model.NewWithholdingCreditTransactionInfo(expectDriverID, expectOrderID, expectTripID, 5),
		}

		driverTrans := model.NewDriverTransaction(expectDriverID)

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		_, _, err := api.AddDriverTransaction(
			ctx,
			*driverTrans,
			model.SystemTransactionChannel,
			model.PurchaseTransactionAction,
			model.SuccessTransactionStatus,
			expectedInfos,
			model.WithTransactionRefID("0b54fb130c2548e5b3d8b8bb58c934aa"),
		)

		require.Nil(tt, err)
	})
}

func TestWithdrawWalletDriverProfileStatusNotComplete(t *testing.T) {
	expiredTime := time.Now().Add(-time.Hour * 24 * 30)
	unExpiredTime := time.Now().Add(-time.Hour * 24 * 3)

	type withdrawFunc func(*testing.T, *model.Driver, model.WithdrawInfo) error
	type driverFunc func(time.Time, model.ProfileStatus) *model.Driver

	withdrawFnc := func(tt *testing.T, driver *model.Driver, bankInfo model.WithdrawInfo) error {
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driver, nil)
		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bankInfo)
		return err
	}

	driverFnc := func(infoTime time.Time, status model.ProfileStatus) *model.Driver {
		return &model.Driver{
			BaseDriver: model.BaseDriver{ProfileStatus: status, CreatedAt: infoTime},
		}
	}

	testCases := []struct {
		name          string
		driverFunc    driverFunc
		withdrawFunc  withdrawFunc
		bankInfo      model.WithdrawInfo
		infoTime      time.Time
		profileStatus model.ProfileStatus
		expectErr     error
	}{
		{
			name:          "Should return error `ErrDriverInfoIsExpired` when profileStatus is `PENDING` and `info is expired` with NO `BANK INFO`",
			driverFunc:    driverFnc,
			withdrawFunc:  withdrawFnc,
			bankInfo:      model.WithdrawInfo{},
			infoTime:      expiredTime,
			profileStatus: model.ProfileStatusPending,
			expectErr:     model.ErrDriverInfoIsExpired,
		},
		{
			name:          "Should return error `ErrDriverInfoIsExpired` when profileStatus is `INCOMPLETE` and `InfoExpiredTime` is `expired` with NO `BANK INFO`",
			driverFunc:    driverFnc,
			withdrawFunc:  withdrawFnc,
			bankInfo:      model.WithdrawInfo{},
			infoTime:      expiredTime,
			profileStatus: model.ProfileStatusIncomplete,
			expectErr:     model.ErrDriverInfoIsExpired,
		},
		{
			name:          "Should return error `ErrTransactionBankInfoIsRequired` when profileStatus is `PENDING` and `InfoExpiredTime` NOT `expired` with NO `BANK INFO`",
			driverFunc:    driverFnc,
			withdrawFunc:  withdrawFnc,
			bankInfo:      model.WithdrawInfo{},
			infoTime:      unExpiredTime,
			profileStatus: model.ProfileStatusPending,
			expectErr:     model.ErrTransactionBankInfoIsRequired,
		},
		{
			name:          "Should return error `ErrTransactionBankInfoIsRequired` when profileStatus is `INCOMPLETE` and `InfoExpiredTime` NOT `expired` with NO `BANK INFO`",
			driverFunc:    driverFnc,
			withdrawFunc:  withdrawFnc,
			bankInfo:      model.WithdrawInfo{},
			infoTime:      unExpiredTime,
			profileStatus: model.ProfileStatusIncomplete,
			expectErr:     model.ErrTransactionBankInfoIsRequired,
		},
		{
			name:         "Should return error `ErrInvalidBankName` when profileStatus is not `COMPLETED` and `InfoExpiredTime` NOT `expired` with invalid bank name",
			driverFunc:   driverFnc,
			withdrawFunc: withdrawFnc,
			bankInfo: model.WithdrawInfo{
				Account:       "111111",
				BankName:      "KKK",
				AccountHolder: "123",
			},
			infoTime:      unExpiredTime,
			profileStatus: model.ProfileStatusIncomplete,
			expectErr:     model.ErrInvalidBankName,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			t.Log(tc.infoTime)
			driver := tc.driverFunc(tc.infoTime, tc.profileStatus)
			driver.WithdrawalQuota = model.NewWithdrawalQuota(0, config.PaymentConfig{WalletWithdrawMaximumReq: 2})
			err := tc.withdrawFunc(tt, driver, tc.bankInfo)
			require.Equal(tt, tc.expectErr, err, tc.name)
		})
	}
}

func TestWithdrawWalletDriverProfileStatusComplete(t *testing.T) {
	walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(0.0)
	t.Run("Should use driver bank info if profileStatus is `COMPLETED`", func(tt *testing.T) {
		expiredTime := time.Now().Add(-time.Hour * 24 * 30)
		bankInfo := model.WithdrawInfo{
			Account:       "**********",
			BankName:      "KBANK",
			AccountHolder: "นายน้ำ",
		}
		driver := &model.Driver{
			ProfileExpirationDate: &expiredTime,
			BaseDriver: model.BaseDriver{
				ProfileStatus: model.ProfileStatusCompleted,
				Banking: model.BankingInfo{
					Account:       crypt.NewLazyEncryptedString("**********"),
					BankName:      crypt.NewLazyEncryptedString("SCB"),
					AccountHolder: crypt.NewLazyEncryptedString("นายดิน"),
				},
			},
			WithdrawalQuota: model.NewWithdrawalQuota(0, config.PaymentConfig{WalletWithdrawMaximumReq: 2}),
		}
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		expectedBanking := model.WithdrawInfo{
			Account:       "**********",
			BankName:      "SCB",
			AccountHolder: "นายดิน",
		}

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driver, nil)

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driver).
			Return(false, nil).
			AnyTimes()

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverRepo.EXPECT().
			SetWithdrawalQuota(gomock.Any(), expectedDriverID, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
				require.Equal(tt, 2, quota.MaxFreeQuotaToday)
				require.Equal(tt, 1, quota.FreeQuotaUsedToday)
				return nil
			})

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(nil, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 0, tfs.UnusualScore)
				return nil
			}).AnyTimes()

		_, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bankInfo)

		trans := transactions[0].Info.WithdrawInfo
		require.Equal(tt, expectedBanking.AccountHolder, trans.AccountHolder)
		require.Equal(tt, expectedBanking.Account, trans.Account)
		require.Equal(tt, expectedBanking.BankName, trans.BankName)
		require.NoError(t, err, "Should not error")
	})
}

func TestDeductVoidFraudTransaction(t *testing.T) {
	type expect struct {
		tranLength int
	}

	testCases := []struct {
		name       string
		driverTran model.DriverTransaction
		trans      []model.Transaction
		expect     expect
	}{
		{
			name:       "do not deduct void Credit PurchaseTransactionType",
			driverTran: createDriverTransaction(1000, 2000),
			trans:      []model.Transaction{createCreditTransaction(model.PurchaseTransactionType, 500)},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void Credit ItemFeeTransactionType",
			driverTran: createDriverTransaction(1000, 2000),
			trans:      []model.Transaction{createCreditTransaction(model.ItemFeeTransactionType, 500)},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void Credit CommissionTransactionType",
			driverTran: createDriverTransaction(199, 5000),
			trans:      []model.Transaction{createCreditTransaction(model.CommissionTransactionType, 500)},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void Credit WithholdingTransactionType",
			driverTran: createDriverTransaction(200, 999),
			trans:      []model.Transaction{createCreditTransaction(model.WithholdingTransactionType, 500)},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void Credit ChargeTransactionType",
			driverTran: createDriverTransaction(200, 999),
			trans:      []model.Transaction{createCreditTransaction(model.ChargeTransactionType, 500)},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void Credit PurchaseTransactionType, DeductVoidFraudTransactionType",
			driverTran: createDriverTransaction(1000, 2000),
			trans: []model.Transaction{
				createCreditTransaction(model.PurchaseTransactionType, 500),
				createCreditTransaction(model.DeductVoidFraudTransactionType, 500),
			},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void Credit PurchaseTransactionType, AddVoidFraudTransactionType",
			driverTran: createDriverTransaction(1000, 2000),
			trans: []model.Transaction{
				createCreditTransaction(model.PurchaseTransactionType, 500),
				createCreditTransaction(model.AddVoidFraudTransactionType, 500),
			},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "deduct void Wallet CouponTransactionType success",
			driverTran: createDriverTransaction(500, 1000),
			trans:      []model.Transaction{createWalletTransaction(model.CouponTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "deduct void Wallet SubsidizeTransactionType success",
			driverTran: createDriverTransaction(500, 1500),
			trans:      []model.Transaction{createWalletTransaction(model.SubsidizeTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "deduct void Wallet IncentiveTransactionType success",
			driverTran: createDriverTransaction(500, 1500),
			trans:      []model.Transaction{createWalletTransaction(model.IncentiveTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "deduct void Wallet ClaimTransactionType success",
			driverTran: createDriverTransaction(500, 2000),
			trans:      []model.Transaction{createWalletTransaction(model.ClaimTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "deduct void Wallet CompensationTransactionType success",
			driverTran: createDriverTransaction(500, 2000),
			trans:      []model.Transaction{createWalletTransaction(model.CompensationTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "deduct void Wallet CompensationTransactionType success",
			driverTran: createDriverTransaction(500, 9000),
			trans:      []model.Transaction{createWalletTransaction(model.CompensationTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "deduct void Wallet NewRiderIncentiveTransactionType success",
			driverTran: createDriverTransaction(500, 9000),
			trans:      []model.Transaction{createWalletTransaction(model.NewRiderIncentiveTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "deduct void Wallet CashAdvanceCouponTransactionType success",
			driverTran: createDriverTransaction(4848, 5000),
			trans:      []model.Transaction{createWalletTransaction(model.CashAdvanceCouponTransactionType, 500)},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "do not deduct void PurchaseTransactionType that already voided",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.PurchaseTransactionType, 500, "a1", ""),
				createCreditTransactionWithIdAndRef(model.VoidTransactionType, 500, "a2", "a1"),
			},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void CommissionTransactionType that already voided",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.CommissionTransactionType, 500, "a1", ""),
				createCreditTransactionWithIdAndRef(model.VoidReturnCreditTransactionType, 500, "a2", "a1"),
			},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void CommissionTransactionType that already voided 2 round",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.CommissionTransactionType, 666, "a1", ""),
				createCreditTransactionWithIdAndRef(model.VoidReturnCreditTransactionType, 666, "a2", "a1"),
				createCreditTransactionWithIdAndRef(model.CommissionTransactionType, 666, "a3", ""),
				createCreditTransactionWithIdAndRef(model.VoidReturnCreditTransactionType, 666, "a4", "a3"),
			},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void CouponTransactionType that already voided",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.CouponTransactionType, 500, "a1", ""),
				createWalletTransactionWithIdAndRef(model.DeductVoidTransactionType, 500, "a2", "a1"),
			},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do not deduct void SubsidizeTransactionType that already voided",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.SubsidizeTransactionType, 500, "a1", ""),
				createWalletTransactionWithIdAndRef(model.DeductVoidTransactionType, 500, "a2", "a1"),
			},
			expect: expect{
				tranLength: 0,
			},
		},
		{
			name:       "do deduct void SubsidizeTransactionType that already voided and add void",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.SubsidizeTransactionType, 500, "a1", ""),
				createWalletTransactionWithIdAndRef(model.DeductVoidTransactionType, 500, "a2", "a1"),
				createWalletTransactionWithIdAndRef(model.SubsidizeTransactionType, 500, "a3", "a2"),
			},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "do deduct void IncentiveTransactionType that already voided and add void 2 round",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.IncentiveTransactionType, 666, "a1", ""),
				createWalletTransactionWithIdAndRef(model.DeductVoidTransactionType, 666, "a2", "a1"),
				createWalletTransactionWithIdAndRef(model.IncentiveTransactionType, 666, "a3", ""),
				createWalletTransactionWithIdAndRef(model.DeductVoidTransactionType, 666, "a4", "a3"),
				createWalletTransactionWithIdAndRef(model.IncentiveTransactionType, 666, "a5", ""),
			},
			expect: expect{
				tranLength: 1,
			},
		},
		{
			name:       "do not deduct void ClaimTransactionType that already voided 2 round",
			driverTran: createDriverTransaction(555, 5555),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.ClaimTransactionType, 666, "a1", ""),
				createWalletTransactionWithIdAndRef(model.DeductVoidTransactionType, 666, "a2", "a1"),
				createWalletTransactionWithIdAndRef(model.ClaimTransactionType, 666, "a3", ""),
				createWalletTransactionWithIdAndRef(model.DeductVoidTransactionType, 666, "a4", "a3"),
			},
			expect: expect{
				tranLength: 0,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			service, _, finish := newDriverTransactionService(tt)
			defer finish()

			transList, err := service.DeductVoidFraudTransaction(tc.trans, "test")
			require.NoError(t, err, "Should not error")
			require.Equal(tt, tc.expect.tranLength, len(transList))
		})
	}
}

func TestAddVoidFraudTransaction(t *testing.T) {
	type expect struct {
		credit     float64
		wallet     float64
		tranLength int
	}

	testCases := []struct {
		name       string
		driverTran model.DriverTransaction
		trans      []model.Transaction
		expect     expect
	}{
		{
			name:       "do not add void Credit PurchaseTransactionType",
			driverTran: createDriverTransaction(1000, 2000),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.PurchaseTransactionType, 500, "a1", "a2"),
				createCreditTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},

			expect: expect{
				credit:     1000,
				wallet:     2000,
				tranLength: 0,
			},
		},
		{
			name:       "do not add void Credit ItemFeeTransactionType",
			driverTran: createDriverTransaction(1000, 2000),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.ItemFeeTransactionType, 500, "a1", "a2"),
				createCreditTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     1000,
				wallet:     2000,
				tranLength: 0,
			},
		},
		{
			name:       "do not add void Credit CommissionTransactionType",
			driverTran: createDriverTransaction(8000, 5000),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.CommissionTransactionType, 500, "a1", "a2"),
				createCreditTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     8000,
				wallet:     5000,
				tranLength: 0,
			},
		},
		{
			name:       "do not add void Credit WithholdingTransactionType",
			driverTran: createDriverTransaction(1500, 999),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.WithholdingTransactionType, 500, "a1", "a2"),
				createCreditTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     1500,
				wallet:     999,
				tranLength: 0,
			},
		},
		{
			name:       "do not add void Credit ChargeTransactionType",
			driverTran: createDriverTransaction(800, 999),
			trans: []model.Transaction{
				createCreditTransactionWithIdAndRef(model.ChargeTransactionType, 500, "a1", "a2"),
				createCreditTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     800,
				wallet:     999,
				tranLength: 0,
			},
		},
		{
			name:       "add void Wallet CouponTransactionType success",
			driverTran: createDriverTransaction(500, 1000),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.CouponTransactionType, 500, "a1", "a2"),
				createWalletTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     500,
				wallet:     1500,
				tranLength: 1,
			},
		},
		{
			name:       "add void Wallet SubsidizeTransactionType success",
			driverTran: createDriverTransaction(500, 1500),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.SubsidizeTransactionType, 500, "a1", "a2"),
				createWalletTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     500,
				wallet:     2000,
				tranLength: 1,
			},
		},
		{
			name:       "add void Wallet IncentiveTransactionType success",
			driverTran: createDriverTransaction(500, 1500),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.IncentiveTransactionType, 500, "a1", "a2"),
				createWalletTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     500,
				wallet:     2000,
				tranLength: 1,
			},
		},
		{
			name:       "add void Wallet ClaimTransactionType success",
			driverTran: createDriverTransaction(500, 2000),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.ClaimTransactionType, 500, "a1", "a2"),
				createWalletTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     500,
				wallet:     2500,
				tranLength: 1,
			},
		},
		{
			name:       "add void Wallet CompensationTransactionType success",
			driverTran: createDriverTransaction(500, 2000),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.CompensationTransactionType, 500, "a1", "a2"),
				createWalletTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     500,
				wallet:     2500,
				tranLength: 1,
			},
		},
		{
			name:       "add void Wallet NewRiderIncentiveTransactionType success",
			driverTran: createDriverTransaction(500, 9000),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.NewRiderIncentiveTransactionType, 500, "a1", "a2"),
				createWalletTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     500,
				wallet:     9500,
				tranLength: 1,
			},
		},
		{
			name:       "add void Wallet CashAdvanceCouponTransactionType success",
			driverTran: createDriverTransaction(500, 9000),
			trans: []model.Transaction{
				createWalletTransactionWithIdAndRef(model.CashAdvanceCouponTransactionType, 500, "a1", "a2"),
				createWalletTransactionWithIdAndRef(model.DeductVoidFraudTransactionType, 500, "a3", "a1"),
			},
			expect: expect{
				credit:     500,
				wallet:     9500,
				tranLength: 1,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			service, _, finish := newDriverTransactionService(tt)
			defer finish()

			transList, err, _ := service.AddVoidFraudTransaction(tc.trans, "test")
			require.NoError(t, err, "Should not error")
			require.Equal(tt, tc.expect.tranLength, len(transList))
		})
	}
}

func createCreditTransactionWithIdAndRef(typ model.TransactionType, amount float64, transID string, refID string) model.Transaction {
	t := createTransaction(typ, model.CreditTransactionCategory, amount)
	t.TransactionID = transID
	t.Info.RefID = refID
	return t
}

func createWalletTransactionWithIdAndRef(typ model.TransactionType, amount float64, transID string, refID string) model.Transaction {
	t := createTransaction(typ, model.WalletTransactionCategory, amount)
	t.TransactionID = transID
	t.Info.RefID = refID
	return t
}

func createCreditTransaction(typ model.TransactionType, amount float64) model.Transaction {
	return createTransaction(typ, model.CreditTransactionCategory, amount)
}

func createWalletTransaction(typ model.TransactionType, amount float64) model.Transaction {
	return createTransaction(typ, model.WalletTransactionCategory, amount)
}

func createTransaction(typ model.TransactionType, cat model.TransactionCategory, amount float64) model.Transaction {
	info := model.TransactionInfo{
		Type:       typ,
		Category:   cat,
		TransRefID: "txn-ref-id",
		Amount:     types.NewMoney(amount),
	}
	return *model.NewTransaction("txn-1", model.UserTransactionChannel, model.VoidTransactionAction, model.SuccessTransactionStatus, info)
}

func createDriverTransaction(credit float64, wallet float64) model.DriverTransaction {
	trans := model.NewDriverTransaction("driver-1")
	trans.PurchaseCreditBalance = types.NewMoney(credit)
	trans.WalletBalance = types.NewMoney(wallet)
	return *trans
}

type driverTransDep struct {
	banSvc            *mock_service.MockBanService
	driverTransSvc    *mock_service.MockDriverTransactionServiceV2
	transactionRepo   *mock_repository.MockTransactionRepository
	driverTransRepo   *mock_repository.MockDriverTransactionRepository
	fraudAdvisorSvc   *mock_fraudadvisor.MockFraudAdvisorService
	txnFraudScoreRepo *mock_repository.MockTransactionFraudScoreRepository
	driverSvc         *mock_service.MockDriverServiceInterface
	driverRepo        *mock_repository.MockDriverRepository
	txnHelper         *mock_transaction.MockTxnHelper
	productGroupRepo  *mock_repository.MockProductGroupRepository
}

func newDriverTransactionServiceWithConfig(t gomock.TestReporter, config config.PaymentConfig) (DriverTransactionService, *driverTransDep, func()) {
	ctrl := gomock.NewController(t)

	transactionRepo := mock_repository.NewMockTransactionRepository(ctrl)
	driverTransRepo := mock_repository.NewMockDriverTransactionRepository(ctrl)
	fraudAdvisorSvc := mock_fraudadvisor.NewMockFraudAdvisorService(ctrl)
	txnFraudScoreRepo := mock_repository.NewMockTransactionFraudScoreRepository(ctrl)
	driverSvc := mock_service.NewMockDriverServiceInterface(ctrl)
	driverRepo := mock_repository.NewMockDriverRepository(ctrl)
	banSvc := mock_service.NewMockBanService(ctrl)
	txnHelper := mock_transaction.NewMockTxnHelper(ctrl)
	productGroupRepo := mock_repository.NewMockProductGroupRepository(ctrl)
	driverTransSvcV2 := mock_service.NewMockDriverTransactionServiceV2(ctrl)

	deps := &driverTransDep{
		banSvc:            banSvc,
		driverTransSvc:    driverTransSvcV2,
		transactionRepo:   transactionRepo,
		driverTransRepo:   driverTransRepo,
		fraudAdvisorSvc:   fraudAdvisorSvc,
		txnFraudScoreRepo: txnFraudScoreRepo,
		driverSvc:         driverSvc,
		driverRepo:        driverRepo,
		txnHelper:         txnHelper,
		productGroupRepo:  productGroupRepo,
	}

	driverTransService := ProvideDriverTransactionService(banSvc, driverTransRepo, transactionRepo, fraudAdvisorSvc, txnFraudScoreRepo, driverSvc, driverRepo, config, txnHelper, productGroupRepo, driverTransSvcV2)

	return driverTransService, deps, func() { ctrl.Finish() }
}

func newDriverTransactionService(t gomock.TestReporter) (DriverTransactionService, *driverTransDep, func()) {
	return newDriverTransactionServiceWithConfig(t, config.PaymentConfig{
		WalletWithdrawMinimumAmount:   200,
		WalletWithdrawMaximumReq:      1,
		ProfileStatusInfoExpirePeriod: 7,
	})
}

func TestGetTransactionByOrderID(t *testing.T) {
	t.Parallel()

	t.Run("success", func(tt *testing.T) {
		api, deps, finish := newDriverTransactionService(t)
		defer finish()

		ctx := context.Background()
		t := model.NewTransaction(
			"tran-1",
			model.UserTransactionChannel,
			model.WithdrawTransactionAction,
			model.FailTransactionStatus,
			*model.NewWithdrawWalletTransactionInfo("driver-1", 100.00),
		)
		trans := []model.Transaction{
			*t,
		}

		deps.transactionRepo.EXPECT().
			FindByOrderID(ctx, "orderId").
			Return(trans, nil)

		actualTrans, err := api.GetTransactionByOrderID(
			ctx,
			"orderId",
		)

		require.Nil(tt, err)
		require.Len(tt, actualTrans, 1)
	})

	t.Run("error", func(tt *testing.T) {
		api, deps, finish := newDriverTransactionService(t)
		defer finish()

		ctx := context.Background()

		deps.transactionRepo.EXPECT().
			FindByOrderID(ctx, "orderId").
			Return(nil, errors.New("error"))

		_, err := api.GetTransactionByOrderID(
			ctx,
			"orderId",
		)

		require.Error(tt, err)
	})
}

func TestWithdrawWalletWithNegativeCreditAndReservedInstallment(tt *testing.T) {
	tt.Parallel()

	bankInfo := model.WithdrawInfo{
		Account:       "A",
		BankName:      "B",
		AccountHolder: "C",
	}
	expectedDriverID := "driver-1"
	paymentConfig := config.PaymentConfig{
		WalletWithdrawMinimumAmount:       200,
		WalletWithdrawMaximumReq:          5,
		ProfileStatusInfoExpirePeriod:     7,
		AllowWithdrawalWithNegativeCredit: true,
	}

	type testData struct {
		walletBalance       types.Money
		creditBalance       types.Money
		reservedBalance     types.Money
		toWithdrawAmount    types.Money
		expectedFinalWallet types.Money
		isWithdrawalError   bool
	}

	testTable := map[string]testData{
		"wallet_500_neg_credit_100_reserved_100_withdraw_200": {
			walletBalance:       500,
			creditBalance:       -100,
			reservedBalance:     100,
			toWithdrawAmount:    200,
			expectedFinalWallet: 300,
			isWithdrawalError:   false,
		},
		"wallet_400_neg_credit_100_reserved_100_withdraw_200": {
			walletBalance:       400,
			creditBalance:       -100,
			reservedBalance:     100,
			toWithdrawAmount:    200,
			expectedFinalWallet: 200,
			isWithdrawalError:   false,
		},
		"wallet_200_neg_credit_100_reserved_100_withdraw_200": {
			walletBalance:       200,
			creditBalance:       -100,
			reservedBalance:     100,
			toWithdrawAmount:    200,
			expectedFinalWallet: 0,
			isWithdrawalError:   true,
		},
		"wallet_200_neg_credit_200_reserved_200_withdraw_200": {
			walletBalance:       200,
			creditBalance:       -200,
			reservedBalance:     200,
			toWithdrawAmount:    200,
			expectedFinalWallet: 0,
			isWithdrawalError:   true,
		},
		"wallet_300_neg_credit_0_reserved_100_withdraw_200": {
			walletBalance:       300,
			creditBalance:       0,
			reservedBalance:     100,
			toWithdrawAmount:    200,
			expectedFinalWallet: 100,
			isWithdrawalError:   false,
		},
		"wallet_300_neg_credit_100_reserved_0_withdraw_200": {
			walletBalance:       300,
			creditBalance:       -100,
			reservedBalance:     0,
			toWithdrawAmount:    200,
			expectedFinalWallet: 100,
			isWithdrawalError:   false,
		},
	}

	for testName, data := range testTable {
		testData := data

		tt.Run(testName, func(t *testing.T) {
			t.Parallel()

			driver := &model.Driver{
				BaseDriver:      model.BaseDriver{ProfileStatus: model.ProfileStatusCompleted},
				WithdrawalQuota: model.NewWithdrawalQuota(0, paymentConfig),
			}

			expectedDriverTransaction := model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  testData.creditBalance,
				WalletBalance:          testData.walletBalance,
			}
			ctx := context.Background()
			service, deps, finish := newDriverTransactionServiceWithConfig(t, paymentConfig)
			defer finish()

			deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

			deps.driverSvc.EXPECT().
				GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
				Return(driver, nil)

			// <GetWithdrawableAmount
			// <<GetDriverTransaction
			deps.driverTransSvc.EXPECT().
				GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
				Return(expectedDriverTransaction, nil)

			var callTimes int
			if !testData.isWithdrawalError {
				callTimes = 1
			}

			deps.driverRepo.EXPECT().
				SetWithdrawalQuota(gomock.Any(), expectedDriverID, gomock.Any()).
				DoAndReturn(func(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
					require.Equal(tt, 5, quota.MaxFreeQuotaToday)
					require.Equal(tt, 1, quota.FreeQuotaUsedToday)
					return nil
				}).Times(callTimes)

			deps.driverTransRepo.EXPECT().
				Update(ctx, gomock.Any()).
				Return(nil).
				Times(callTimes)

			deps.transactionRepo.EXPECT().
				CreateAll(ctx, gomock.Any()).
				Return(nil).
				Times(callTimes)

			deps.transactionRepo.EXPECT().
				FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
				Return(nil, nil).
				AnyTimes()

			deps.fraudAdvisorSvc.EXPECT().
				GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
				Return([]fraudadvisor.FraudScoreResponse{}, nil).
				AnyTimes()

			deps.txnFraudScoreRepo.EXPECT().
				Create(ctx, gomock.Any()).
				DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
					require.Equal(tt, 0, tfs.UnusualScore)
					return nil
				}).AnyTimes()

			deps.banSvc.EXPECT().
				IsPermanentBannedDriver(ctx, *driver).
				Return(false, nil).
				AnyTimes()

			driverTrans, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, testData.toWithdrawAmount, bankInfo)

			if !testData.isWithdrawalError {
				require.NoError(tt, err)
				require.NotNil(tt, driverTrans)
				require.NotNil(tt, transactions)
				require.Len(tt, transactions, 1)
				require.Equal(tt, testData.expectedFinalWallet, driverTrans.WalletBalance)
				require.Equal(tt, model.PendingTransactionStatus, transactions[0].Status)
			} else {
				require.NotNil(tt, err)
			}
		})
	}
}

func TestWithdrawWalletWithNegativeCreditAndReservedInstallmentAndFee(tt *testing.T) {
	tt.Parallel()

	bankInfo := model.WithdrawInfo{
		Account:       "A",
		BankName:      "B",
		AccountHolder: "C",
	}
	driver := &model.Driver{
		BaseDriver: model.BaseDriver{ProfileStatus: model.ProfileStatusCompleted},
	}
	expectedDriverID := "driver-1"
	paymentConfig := config.PaymentConfig{
		WalletWithdrawMinimumAmount:       200,
		WalletWithdrawMaximumReq:          1,
		ProfileStatusInfoExpirePeriod:     7,
		AllowWithdrawalWithNegativeCredit: true,
		WithdrawalChargeFeeEnabled:        true,
		WithdrawalFee:                     20,
	}

	type testData struct {
		walletBalance            types.Money
		creditBalance            types.Money
		reservedBalance          types.Money
		toWithdrawAmount         types.Money
		expectedFinalWallet      types.Money
		expectedFinalCredit      types.Money
		withdrawalRequestedCount int
		transactionLength        int
		isWithdrawalError        bool
	}

	testTable := map[string]testData{
		"wallet_500_neg_credit_100_reserved_100_withdraw_200": {
			walletBalance:            500,
			creditBalance:            -100,
			reservedBalance:          100,
			toWithdrawAmount:         200, // withdrawable = 300
			expectedFinalWallet:      300,
			expectedFinalCredit:      -100,
			withdrawalRequestedCount: 0, // no fee
			transactionLength:        1, // no Credit transaction
			isWithdrawalError:        false,
		},
		"wallet_500_neg_credit_100_reserved_100_withdraw_300_fee": {
			walletBalance:            500,
			creditBalance:            -100,
			reservedBalance:          100,
			toWithdrawAmount:         300, // withdrawable = 280
			expectedFinalWallet:      0,
			expectedFinalCredit:      0,
			withdrawalRequestedCount: 1, // has fee
			transactionLength:        0, // error
			isWithdrawalError:        true,
		},
		"wallet_500_credit_100_reserved_100_withdraw_500": {
			walletBalance:            500,
			creditBalance:            100,
			reservedBalance:          100,
			toWithdrawAmount:         400, // withdrawable = 400
			expectedFinalWallet:      100,
			expectedFinalCredit:      100,
			withdrawalRequestedCount: 0, // no fee
			transactionLength:        1, // no Credit transaction
			isWithdrawalError:        false,
		},
		"wallet_500_credit_100_reserved_100_withdraw_500_fee": {
			walletBalance:            500,
			creditBalance:            100,
			reservedBalance:          100,
			toWithdrawAmount:         400, // withdrawable = 400
			expectedFinalWallet:      100,
			expectedFinalCredit:      80,
			withdrawalRequestedCount: 1, // has fee
			transactionLength:        2, // has Credit transaction
			isWithdrawalError:        false,
		},
		"wallet_500_credit_10_reserved_100_withdraw_390_fee": {
			walletBalance:            500,
			creditBalance:            10,
			reservedBalance:          100,
			toWithdrawAmount:         390, // withdrawable = 390
			expectedFinalWallet:      110,
			expectedFinalCredit:      -10,
			withdrawalRequestedCount: 1, // has fee
			transactionLength:        3, // has Credit transaction + Outstanding
			isWithdrawalError:        false,
		},
		"wallet_290_credit_10_reserved_0_withdraw_300_fee": {
			walletBalance:            300,
			creditBalance:            10,
			reservedBalance:          0,
			toWithdrawAmount:         290, // withdrawable = 290
			expectedFinalWallet:      10,
			expectedFinalCredit:      -10,
			withdrawalRequestedCount: 1, // has fee
			transactionLength:        3, // has Credit transaction + Outstanding
			isWithdrawalError:        false,
		},
		"wallet_300_credit_10_reserved_0_withdraw_300_fee": {
			walletBalance:            300,
			creditBalance:            10,
			reservedBalance:          0,
			toWithdrawAmount:         300, // withdrawable = 290
			expectedFinalWallet:      0,
			expectedFinalCredit:      0,
			withdrawalRequestedCount: 1, // has fee
			transactionLength:        0,
			isWithdrawalError:        true,
		},
		"wallet_300_credit_20_reserved_0_withdraw_300_fee": {
			walletBalance:            300,
			creditBalance:            20,
			reservedBalance:          0,
			toWithdrawAmount:         300, // withdrawable = 300
			expectedFinalWallet:      0,
			expectedFinalCredit:      0,
			withdrawalRequestedCount: 1, // has fee
			transactionLength:        2, // has Credit transaction
			isWithdrawalError:        false,
		},
		"wallet_290_credit_30_reserved_0_withdraw_300_fee": {
			walletBalance:            300,
			creditBalance:            30,
			reservedBalance:          0,
			toWithdrawAmount:         290, // withdrawable = 290
			expectedFinalWallet:      10,
			expectedFinalCredit:      10,
			withdrawalRequestedCount: 1, // has fee
			transactionLength:        2, // has Credit transaction
			isWithdrawalError:        false,
		},
	}

	for testName, data := range testTable {
		testData := data
		tcDriver := driver
		tcDriver.WithdrawalQuota = model.NewWithdrawalQuota(testData.withdrawalRequestedCount, paymentConfig)

		tt.Run(testName, func(t *testing.T) {
			// t.Parallel()
			expectedDriverTransaction := model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  testData.creditBalance,
				WalletBalance:          testData.walletBalance,
			}
			ctx := context.Background()
			service, deps, finish := newDriverTransactionServiceWithConfig(t, paymentConfig)
			defer finish()

			deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

			deps.driverSvc.EXPECT().
				GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
				Return(tcDriver, nil)

			// <GetWithdrawableAmount
			// <<GetDriverTransaction
			deps.driverTransSvc.EXPECT().
				GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
				Return(expectedDriverTransaction, nil)
			// GetDriverTransaction>>

			var callTimes int
			if !testData.isWithdrawalError {
				callTimes = 1
			}

			if testData.withdrawalRequestedCount == 0 {
				deps.driverRepo.EXPECT().
					SetWithdrawalQuota(gomock.Any(), expectedDriverID, gomock.Any()).
					DoAndReturn(func(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
						require.Equal(tt, 1, quota.MaxFreeQuotaToday)
						require.Equal(tt, 1, quota.FreeQuotaUsedToday)
						return nil
					})
			}

			deps.driverTransRepo.EXPECT().
				Update(ctx, gomock.Any()).
				Return(nil).
				Times(callTimes)

			deps.transactionRepo.EXPECT().
				CreateAll(ctx, gomock.Any()).
				Return(nil).
				Times(callTimes)

			deps.transactionRepo.EXPECT().
				FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
				Return(nil, nil).
				AnyTimes()

			deps.fraudAdvisorSvc.EXPECT().
				GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
				Return([]fraudadvisor.FraudScoreResponse{}, nil).
				AnyTimes()

			deps.txnFraudScoreRepo.EXPECT().
				Create(ctx, gomock.Any()).
				DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
					require.Equal(tt, 0, tfs.UnusualScore)
					return nil
				}).AnyTimes()

			deps.banSvc.EXPECT().
				IsPermanentBannedDriver(ctx, *driver).
				Return(false, nil).
				AnyTimes()

			driverTrans, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, testData.toWithdrawAmount, bankInfo)
			if !testData.isWithdrawalError {
				require.NoError(tt, err)
				require.NotNil(tt, driverTrans)
				require.NotNil(tt, transactions)
				require.Len(tt, transactions, testData.transactionLength)
				require.Equal(tt, testData.expectedFinalWallet, driverTrans.WalletBalance)
				require.Equal(tt, testData.expectedFinalCredit, driverTrans.CreditBalance())
				require.Equal(tt, model.PendingTransactionStatus, transactions[0].Status)
			} else {
				require.NotNil(tt, err)
			}
		})
	}
}

func TestGetWithdrawableAmount(tt *testing.T) {
	tt.Parallel()

	type testData struct {
		walletBalance                       types.Money
		creditBalance                       types.Money
		installmentAmount                   types.Money
		installmentReservedAmount           types.Money
		expectedWithdrawable                types.Money
		expectedReservedInstallment         types.Money
		isWithdrawalRequestLimited          bool
		withdrawalFee                       types.Money
		expectedWalletFee                   types.Money
		isAllowWithdrawalWithNegativeCredit bool
	}

	testTable := map[string]testData{
		"wallet_100_negative_0_reserved_0": {
			walletBalance:                       100,
			creditBalance:                       0,
			installmentAmount:                   0,
			installmentReservedAmount:           0,
			expectedWithdrawable:                100,
			expectedReservedInstallment:         0,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_200_negative_100_reserved_0": {
			walletBalance:                       200,
			creditBalance:                       -100,
			installmentAmount:                   0,
			installmentReservedAmount:           0,
			expectedWithdrawable:                100,
			expectedReservedInstallment:         0,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_200_negative_0_reserved_100": {
			walletBalance:                       200,
			creditBalance:                       0,
			installmentAmount:                   0,
			installmentReservedAmount:           100,
			expectedWithdrawable:                100,
			expectedReservedInstallment:         100,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_200_negative_50_reserved_50": {
			walletBalance:                       200,
			creditBalance:                       -50,
			installmentAmount:                   0,
			installmentReservedAmount:           50,
			expectedWithdrawable:                100,
			expectedReservedInstallment:         50,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_200_negative_100_reserved_100": {
			walletBalance:                       200,
			creditBalance:                       -100,
			installmentAmount:                   0,
			installmentReservedAmount:           100,
			expectedWithdrawable:                0,
			expectedReservedInstallment:         100,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_400_negative_200_reserved_100_fee_20": {
			walletBalance:                       400,
			creditBalance:                       -200,
			installmentAmount:                   -100,
			installmentReservedAmount:           100,
			withdrawalFee:                       20,
			isWithdrawalRequestLimited:          true,
			expectedWithdrawable:                80,
			expectedReservedInstallment:         100,
			expectedWalletFee:                   20,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_400_negative_0_reserved_100_fee_20": {
			walletBalance:                       400,
			creditBalance:                       0,
			installmentAmount:                   0,
			installmentReservedAmount:           100,
			withdrawalFee:                       20,
			isWithdrawalRequestLimited:          true,
			expectedWithdrawable:                280,
			expectedReservedInstallment:         100,
			expectedWalletFee:                   20,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_400_credit_20_reserved_100_fee_20": {
			walletBalance:                       400,
			creditBalance:                       20,
			installmentAmount:                   0,
			installmentReservedAmount:           100,
			withdrawalFee:                       20,
			isWithdrawalRequestLimited:          true,
			expectedWithdrawable:                300,
			expectedReservedInstallment:         100,
			expectedWalletFee:                   0,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_400_credit_10_reserved_100_fee_20": {
			walletBalance:                       400,
			creditBalance:                       10,
			installmentAmount:                   0,
			installmentReservedAmount:           100,
			withdrawalFee:                       20,
			isWithdrawalRequestLimited:          true,
			expectedWithdrawable:                290,
			expectedReservedInstallment:         100,
			expectedWalletFee:                   10,
			isAllowWithdrawalWithNegativeCredit: true,
		},
		"wallet_400_credit_30_reserved_100_fee_20": {
			walletBalance:                       400,
			creditBalance:                       30,
			installmentAmount:                   0,
			installmentReservedAmount:           100,
			withdrawalFee:                       20,
			isWithdrawalRequestLimited:          true,
			expectedWithdrawable:                300,
			expectedReservedInstallment:         100,
			expectedWalletFee:                   0,
			isAllowWithdrawalWithNegativeCredit: true,
		},
	}

	fixedDriverID := "DRIVER_ID"
	// withdrawalLimitConfig := 2
	for testName, data := range testTable {
		testData := data
		tt.Run(testName, func(t *testing.T) {
			t.Parallel()
			ctx := context.Background()

			paymentConfig := config.PaymentConfig{
				WalletWithdrawMinimumAmount:       200,
				ProfileStatusInfoExpirePeriod:     7,
				AllowWithdrawalWithNegativeCredit: testData.isAllowWithdrawalWithNegativeCredit,
			}
			service, _, finish := newDriverTransactionServiceWithConfig(t, paymentConfig)
			defer finish()

			srcDriverTransaction := model.DriverTransaction{
				DriverID:              fixedDriverID,
				WalletBalance:         testData.walletBalance,
				PurchaseCreditBalance: testData.creditBalance,
				InstallmentAmount:     testData.installmentAmount,
			}
			resultWithdrawableAmount, resultReservedAmount, resultWalletFee, err := service.GetWithdrawableAmount(ctx, srcDriverTransaction, testData.withdrawalFee, repository.WithReadPrimary)

			assert.NoError(t, err)
			assert.Equal(t, testData.expectedWithdrawable, resultWithdrawableAmount)
			assert.Equal(t, testData.expectedReservedInstallment, resultReservedAmount)
			assert.Equal(t, testData.expectedWalletFee, resultWalletFee)
		})
	}
}

func TestWithdrawWalletDriverProfileStatusRequestedUpdate(t *testing.T) {
	walletBalance, creditBalance := types.NewMoney(300.0), types.NewMoney(0.0)
	t.Run("Should use driver bank info if profileStatus is `REQUESTED_UPDATE`", func(tt *testing.T) {
		expiredTime := time.Now().Add(-time.Hour * 24 * 30)
		bankInfo := model.WithdrawInfo{
			Account:       "**********",
			BankName:      "KBANK",
			AccountHolder: "นายน้ำ",
		}
		driver := &model.Driver{
			ProfileExpirationDate: &expiredTime,
			BaseDriver: model.BaseDriver{
				ProfileStatus: model.ProfileStatusCompleted,
				Banking: model.BankingInfo{
					Account:       crypt.NewLazyEncryptedString("**********"),
					BankName:      crypt.NewLazyEncryptedString("SCB"),
					AccountHolder: crypt.NewLazyEncryptedString("นายดิน"),
				},
			},
			WithdrawalQuota: model.NewWithdrawalQuota(0, config.PaymentConfig{WalletWithdrawMaximumReq: 2}),
		}
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"
		expectedBanking := model.WithdrawInfo{
			Account:       "**********",
			BankName:      "SCB",
			AccountHolder: "นายดิน",
		}

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driver, nil)

		deps.banSvc.EXPECT().
			IsPermanentBannedDriver(ctx, *driver).
			Return(false, nil).
			AnyTimes()

		deps.driverTransSvc.EXPECT().
			GetDriverTransaction(ctx, expectedDriverID, gomock.Any()).
			Return(model.DriverTransaction{
				DriverID:               expectedDriverID,
				FreeCreditTransactions: []model.TransactionInfo{},
				PurchaseCreditBalance:  creditBalance,
				WalletBalance:          walletBalance,
			}, nil)

		deps.driverRepo.EXPECT().
			SetWithdrawalQuota(gomock.Any(), expectedDriverID, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
				require.Equal(tt, 2, quota.MaxFreeQuotaToday)
				require.Equal(tt, 1, quota.FreeQuotaUsedToday)
				return nil
			})

		deps.driverTransRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.transactionRepo.EXPECT().
			FindLatestWithdrawTransactionByID(ctx, expectedDriverID).
			Return(nil, nil).
			AnyTimes()

		deps.fraudAdvisorSvc.EXPECT().
			GetDriverFraudScore(ctx, expectedDriverID, gomock.Any(), gomock.Any()).
			Return([]fraudadvisor.FraudScoreResponse{}, nil).
			AnyTimes()

		deps.txnFraudScoreRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(c context.Context, tfs *model.TransactionFraudScore) error {
				require.Equal(tt, 0, tfs.UnusualScore)
				return nil
			}).AnyTimes()

		_, transactions, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bankInfo)

		trans := transactions[0].Info.WithdrawInfo
		require.Equal(tt, expectedBanking.AccountHolder, trans.AccountHolder)
		require.Equal(tt, expectedBanking.Account, trans.Account)
		require.Equal(tt, expectedBanking.BankName, trans.BankName)
		require.NoError(t, err, "Should not error")
	})
}

func TestWithdrawWalletDriverProfileStatusRequestedUpdateButNoBankInfo(t *testing.T) {
	t.Run("Should use driver bank info if profileStatus is `REQUESTED_UPDATE` but no bank info", func(tt *testing.T) {
		expiredTime := time.Now().Add(-time.Hour * 24 * 30)
		bankInfo := model.WithdrawInfo{}
		driver := &model.Driver{
			ProfileExpirationDate: &expiredTime,
			BaseDriver: model.BaseDriver{
				ProfileStatus: model.ProfileStatusRequestUpdate,
				Banking:       model.BankingInfo{},
			},
			WithdrawalQuota: model.NewWithdrawalQuota(0, config.PaymentConfig{WalletWithdrawMaximumReq: 2}),
		}
		service, deps, finish := newDriverTransactionService(tt)
		defer finish()

		ctx := context.Background()
		expectedDriverID := "driver-1"

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.driverSvc.EXPECT().
			GetProfileWithLatestWithdrawalQuota(ctx, expectedDriverID).
			Return(driver, nil)

		_, _, err := service.WithdrawWallet(ctx, expectedDriverID, 200, bankInfo)
		require.ErrorIs(t, err, model.ErrStatusNotAllowed)
	})
}
