package rating

import (
	"encoding/json"

	unleash "github.com/Unleash/unleash-client-go/v3"
	unleashcontext "github.com/Unleash/unleash-client-go/v3/context"
	"github.com/gin-gonic/gin"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
)

type RatingAPI struct {
	ratingOptionRepo   repository.RatingOptionRepository
	featureFlagService featureflag.Service
}

func (r *RatingAPI) GetAll(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()

	variantOpt := unleash.WithVariantContext(unleashcontext.Context{
		UserId: driver.DriverIDFromGinContext(gCtx),
	})

	if r.featureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsEnableRevampRiderReviewRestaurant.Name) {
		variant := r.featureFlagService.GetVariant(featureflag.IsEnableRevampRiderReviewRestaurant.Name, variantOpt)
		var cfg struct {
			RevampVersionEligible bool `json:"revamp_version_eligible"`
		}

		if err := json.Unmarshal([]byte(variant.Payload.Value), &cfg); err != nil {
			logx.Error().Msgf("unmarshal variant for feature `%v` err: %v", featureflag.IsEnableRevampRiderReviewRestaurant.Name, err)
		}

		if !cfg.RevampVersionEligible {
			apiutil.OK(gCtx, NewRatingOptionResponse(nil))
			return
		}
	}

	roc, err := r.ratingOptionRepo.GetAll(ctx, repository.RatingOptionRestaurantActive)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, NewRatingOptionResponse(roc))

}

func NewRatingOptionResponse(roc []model.RatingOption) RatingOptionResponse {
	rating := make([]model.RatingOption, 0)
	radio := make([]model.RatingOption, 0)

	for _, v := range roc {
		if len(v.RadioChoices) > 0 {
			radio = append(radio, v)
		} else {
			rating = append(rating, v)
		}
	}

	ratingItems := make([]RatingOptionItemResponse, len(rating))
	radioItems := make([]RadioOptionItemResponse, len(radio))

	for i, v := range rating {
		ro := NewRatingOptionItemResponse(v)
		ratingItems[i] = ro
	}

	for i, v := range radio {
		ro := NewRadioOptionItemResponse(v)
		radioItems[i] = ro
	}

	return RatingOptionResponse{
		Options:      ratingItems,
		RadioOptions: radioItems,
	}
}

func ProvideRatingOptionAPI(ratingOptionRepo repository.RatingOptionRepository, featureFlagService featureflag.Service) *RatingAPI {
	return &RatingAPI{
		ratingOptionRepo:   ratingOptionRepo,
		featureFlagService: featureFlagService,
	}
}
