package rating

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestRatingAdminAPI_Create(t *testing.T) {
	t.Run("success", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		ctx, recorder := testutil.TestRequestContext("POST", "v1/admin/rating", testutil.JSON(req))

		deps.ratingOptionRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(nil)
		api.Create(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("radio choices success", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		choices := []model.RadioChoice{
			{
				ID:    1,
				Label: "ใช่",
			},
		}
		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, RadioChoices: choices, CreatedBy: "Ruri"}
		ctx, recorder := testutil.TestRequestContext("POST", "v1/admin/rating", testutil.JSON(req))

		deps.ratingOptionRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(nil)
		api.Create(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("fail with radio choices id is zero", func(tt *testing.T) {
		api, _, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		choices := []model.RadioChoice{
			{
				ID:    0,
				Label: "ใช่",
			},
		}
		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, RadioChoices: choices, CreatedBy: "Ruri"}
		ctx, recorder := testutil.TestRequestContext("POST", "v1/admin/rating", testutil.JSON(req))

		api.Create(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("fail with radio choices id empty", func(tt *testing.T) {
		api, _, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		choices := []model.RadioChoice{
			{
				Label: "ใช่",
			},
		}
		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, RadioChoices: choices, CreatedBy: "Ruri"}
		ctx, recorder := testutil.TestRequestContext("POST", "v1/admin/rating", testutil.JSON(req))

		api.Create(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("fail with duplicate radio choices id", func(tt *testing.T) {
		api, _, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		choices := []model.RadioChoice{
			{
				ID:    1,
				Label: "ใช่",
			},
			{
				ID:    1,
				Label: "ใช่",
			},
		}
		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, RadioChoices: choices, CreatedBy: "Ruri"}
		ctx, recorder := testutil.TestRequestContext("POST", "v1/admin/rating", testutil.JSON(req))

		api.Create(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("error", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		ctx, recorder := testutil.TestRequestContext("POST", "v1/admin/rating", testutil.JSON(req))

		deps.ratingOptionRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(errors.New("Error"))
		api.Create(ctx)

		assert.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("error validate field", func(tt *testing.T) {
		api, _, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT"}
		ctx, recorder := testutil.TestRequestContext("POST", "v1/admin/rating", testutil.JSON(req))

		api.Create(ctx)

		assert.Equal(tt, http.StatusBadRequest, recorder.Code)
	})
}

func TestRatingAdminAPI_Get(t *testing.T) {
	t.Run("success", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("v1/admin/rating/%s", id), nil)
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}
		ro := model.RatingOption{Name: "ความสะอาด"}
		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(&ro, nil)

		api.Get(ctx)

		var actual RatingOptionAdminResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Empty(tt, ctx.Errors.Errors())
		require.Equal(tt, "ความสะอาด", actual.Name)
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("error", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("v1/admin/rating/%s", id), nil)
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}
		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(nil, errors.New("error"))

		api.Get(ctx)

		assert.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestRatingAdminAPI_GetAll(t *testing.T) {
	t.Run("success", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		ctx, recorder := testutil.TestRequestContext("GET", "v1/admin/rating", nil)
		ro := model.RatingOption{Name: "ความสะอาด"}
		ro2 := model.RatingOption{Name: "ระบบทำงานร้านรวดเร็ว"}
		deps.ratingOptionRepo.EXPECT().GetAll(gomock.Any(), repository.RatingOptionQueryAll).
			Return([]model.RatingOption{ro, ro2}, nil)

		api.GetAll(ctx)

		var actual []RatingOptionAdminResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Empty(tt, ctx.Errors.Errors())
		require.Equal(tt, "ความสะอาด", actual[0].Name)
		require.Equal(tt, "ระบบทำงานร้านรวดเร็ว", actual[1].Name)
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("error", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		ctx, recorder := testutil.TestRequestContext("GET", "v1/admin/rating", nil)

		deps.ratingOptionRepo.EXPECT().GetAll(gomock.Any(), repository.RatingOptionQueryAll).
			Return(nil, errors.New("error"))

		api.GetAll(ctx)

		assert.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestRatingAdminAPI_Update(t *testing.T) {
	t.Run("success", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}
		ro := model.RatingOption{Name: "ความสะอาด"}
		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(&ro, nil)
		deps.ratingOptionRepo.EXPECT().Update(gomock.Any(), gomock.Any()).
			Return(nil)
		api.Update(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("radio choices success", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()
		choices := []model.RadioChoice{
			{
				ID:    1,
				Label: "ใช่",
			},
		}

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, RadioChoices: choices, CreatedBy: "Ruri"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}
		ro := model.RatingOption{Name: "ความสะอาด"}
		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(&ro, nil)
		deps.ratingOptionRepo.EXPECT().Update(gomock.Any(), gomock.Any()).
			Return(nil)
		api.Update(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("error validate field", func(tt *testing.T) {
		api, _, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}

		api.Update(ctx)

		assert.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("error not found", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}

		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(nil, errors.New("not found"))

		api.Update(ctx)

		assert.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("update error", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}
		ro := model.RatingOption{Name: "ความสะอาด"}
		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(&ro, nil)
		deps.ratingOptionRepo.EXPECT().Update(gomock.Any(), gomock.Any()).
			Return(errors.New("error"))
		api.Update(ctx)

		assert.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestRatingAdminAPI_Delete(t *testing.T) {
	t.Run("success", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("DELETE", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}
		ro := model.RatingOption{Name: "ความสะอาด"}
		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(&ro, nil)
		deps.ratingOptionRepo.EXPECT().Delete(gomock.Any(), &ro).
			Return(nil)
		api.Delete(ctx)

		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("error not found", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("DELETE", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}

		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(nil, errors.New("not found"))

		api.Delete(ctx)

		assert.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("delete error", func(tt *testing.T) {
		api, deps, ctrl := newTestRatingAdminAPI(tt)
		defer ctrl.Finish()

		req := RatingOptionAdminRequest{RatingType: "RESTAURANT", Name: "ความสะอาด", Label: "ความสะอาด", Active: true, CreatedBy: "Ruri"}
		id := "id48"
		ctx, recorder := testutil.TestRequestContext("DELETE", fmt.Sprintf("v1/admin/rating/%s", id), testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: id},
		}
		ro := model.RatingOption{Name: "ความสะอาด"}
		deps.ratingOptionRepo.EXPECT().Get(gomock.Any(), id).
			Return(&ro, nil)
		deps.ratingOptionRepo.EXPECT().Delete(gomock.Any(), &ro).
			Return(errors.New("error"))
		api.Delete(ctx)

		assert.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

type ratingAdminDeps struct {
	ratingOptionRepo *mock_repository.MockRatingOptionRepository
}

func newTestRatingAdminAPI(t *testing.T) (RatingAdminAPI, ratingAdminDeps, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	ratingOptionRepo := mock_repository.NewMockRatingOptionRepository(ctrl)
	return RatingAdminAPI{ratingOptionRepo: ratingOptionRepo}, ratingAdminDeps{ratingOptionRepo: ratingOptionRepo}, ctrl
}
