package rating

import (
	"net/http"
	"testing"

	"github.com/Unleash/unleash-client-go/v3/api"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestRatingAPI_GetAll(t *testing.T) {
	t.Run("success", func(tt *testing.T) {
		ratingAPI, deps, ctrl := newTestRatingAPI(tt)
		defer ctrl.Finish()
		radio := []model.RadioChoice{
			{ID: 1, Label: "answer"},
		}
		expectedData := []model.RatingOption{
			{Name: "n1", Label: "l1", Active: false},
			{Name: "n2", Label: "l2", Active: true},
			{Name: "n3", Label: "l3", Active: true, RadioChoices: radio},
		}

		deps.ratingOptionRepo.EXPECT().GetAll(gomock.Any(), gomock.Any()).
			Return(expectedData, nil)
		deps.featureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), gomock.Any()).
			Return(false)
		ctx, recorder := testutil.TestRequestContext("GET", "/v1/rating/options", nil)

		ratingAPI.GetAll(ctx)
		var actual RatingOptionResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, 2, len(actual.Options))
		require.Equal(tt, 1, len(actual.RadioOptions))
	})

	t.Run("error", func(tt *testing.T) {
		ratingAPI, deps, ctrl := newTestRatingAPI(tt)
		defer ctrl.Finish()

		deps.ratingOptionRepo.EXPECT().GetAll(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("DATA NOT FOUND"))
		deps.featureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), gomock.Any()).
			Return(false)
		ctx, recorder := testutil.TestRequestContext("GET", "v1/rating/options", nil)

		ratingAPI.GetAll(ctx)

		assert.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("options should be empty when revamp_version_eligible is false", func(tt *testing.T) {
		ratingAPI, deps, ctrl := newTestRatingAPI(tt)
		defer ctrl.Finish()

		deps.featureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnableRevampRiderReviewRestaurant.Name).
			Return(true)
		deps.featureFlagService.EXPECT().GetVariant(featureflag.IsEnableRevampRiderReviewRestaurant.Name, gomock.Any()).
			Return(&api.Variant{
				Payload: api.Payload{
					Value: `{"revamp_version_eligible": false}`,
				},
			})
		ctx, recorder := testutil.TestRequestContext("GET", "v1/rating/options", nil)

		ratingAPI.GetAll(ctx)
		var actual RatingOptionResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, 0, len(actual.Options))
		require.Equal(tt, 0, len(actual.RadioOptions))
	})

	t.Run("options should not be empty when revamp_version_eligible is true", func(tt *testing.T) {
		ratingAPI, deps, ctrl := newTestRatingAPI(tt)
		defer ctrl.Finish()
		radio := []model.RadioChoice{
			{ID: 1, Label: "answer"},
		}
		expectedData := []model.RatingOption{
			{Name: "n1", Label: "l1", Active: false},
			{Name: "n2", Label: "l2", Active: true},
			{Name: "n3", Label: "l3", Active: true, RadioChoices: radio},
		}

		deps.ratingOptionRepo.EXPECT().GetAll(gomock.Any(), gomock.Any()).
			Return(expectedData, nil)
		deps.featureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnableRevampRiderReviewRestaurant.Name).
			Return(true)
		deps.featureFlagService.EXPECT().GetVariant(featureflag.IsEnableRevampRiderReviewRestaurant.Name, gomock.Any()).
			Return(&api.Variant{
				Payload: api.Payload{
					Value: `{"revamp_version_eligible": true}`,
				},
			})
		ctx, recorder := testutil.TestRequestContext("GET", "v1/rating/options", nil)

		ratingAPI.GetAll(ctx)
		var actual RatingOptionResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Empty(tt, ctx.Errors.Errors())
		assert.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, 2, len(actual.Options))
		require.Equal(tt, 1, len(actual.RadioOptions))
	})
}

type dependencies struct {
	ratingOptionRepo     *mock_repository.MockRatingOptionRepository
	ratingRestaurantRepo *mock_repository.MockRatingRestaurantRepository
	featureFlagService   *mock_featureflag.MockService
}

func newTestRatingAPI(t *testing.T) (RatingAPI, dependencies, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	ratingOptionRepo := mock_repository.NewMockRatingOptionRepository(ctrl)
	featureFlagService := mock_featureflag.NewMockService(ctrl)
	return RatingAPI{ratingOptionRepo: ratingOptionRepo, featureFlagService: featureFlagService},
		dependencies{ratingOptionRepo: ratingOptionRepo, featureFlagService: featureFlagService}, ctrl
}
