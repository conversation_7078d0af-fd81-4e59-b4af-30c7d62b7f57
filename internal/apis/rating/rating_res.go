package rating

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type RatingOptionResponse struct {
	Options      []RatingOptionItemResponse `json:"options"`
	RadioOptions []RadioOptionItemResponse  `json:"radioOptions"`
}

type RatingOptionItemResponse struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Label string `json:"label"`
}

type RadioOptionItemResponse struct {
	ID      string              `json:"id"`
	Name    string              `json:"name"`
	Label   string              `json:"label"`
	Choices []model.RadioChoice `json:"choices"`
}

type RatingOptionAdminResponse struct {
	ID         string              `json:"id"`
	RatingType model.RatingType    `json:"ratingType"`
	Name       string              `json:"name"`
	Label      string              `json:"label"`
	Active     bool                `json:"active"`
	Choices    []model.RadioChoice `json:"choices"`
	HaveChoice bool                `json:"haveChoice"`
	CreatedAt  time.Time           `json:"createdAt"`
	UpdatedAt  time.Time           `json:"updatedAt"`
	CreatedBy  string              `json:"createdBy"`
}

func NewRatingOptionAdminResponse(ro model.RatingOption) RatingOptionAdminResponse {
	return RatingOptionAdminResponse{
		ID:         ro.ID.Hex(),
		RatingType: ro.RatingType,
		Name:       ro.Name,
		Label:      ro.Label,
		Active:     ro.Active,
		Choices:    ro.RadioChoices,
		HaveChoice: len(ro.RadioChoices) > 0,
		CreatedAt:  ro.CreatedAt,
		UpdatedAt:  ro.UpdatedAt,
		CreatedBy:  ro.CreatedBy,
	}
}

func NewRatingOptionItemResponse(ro model.RatingOption) RatingOptionItemResponse {
	return RatingOptionItemResponse{
		ID:    ro.ID.Hex(),
		Name:  ro.Name,
		Label: ro.Label,
	}
}

func NewRadioOptionItemResponse(ro model.RatingOption) RadioOptionItemResponse {
	return RadioOptionItemResponse{
		ID:      ro.ID.Hex(),
		Name:    ro.Name,
		Label:   ro.Label,
		Choices: ro.RadioChoices,
	}
}
