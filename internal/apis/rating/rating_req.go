package rating

import (
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type RatingOptionAdminRequest struct {
	RatingType   model.RatingType    `json:"ratingType" binding:"required"`
	Name         string              `json:"name" binding:"required"`
	Label        string              `json:"label" binding:"required"`
	Active       bool                `json:"active"`
	RadioChoices []model.RadioChoice `json:"choices"`
	CreatedBy    string              `json:"createdBy" binding:"required"`
}

func NewRatingOptionAdminRequest(gCtx *gin.Context) (*RatingOptionAdminRequest, error) {
	var req RatingOptionAdminRequest
	if err := gCtx.ShouldBindJSON(&req); err != nil {
		return nil, err
	}
	return &req, nil
}

func (req RatingOptionAdminRequest) ToRatingOption() model.RatingOption {
	return *model.NewRatingOption(req.RatingType, req.Name, req.Label, req.Active, req.CreatedBy, req.RadioChoices)
}

func (req RatingOptionAdminRequest) UpdateRatingOption(m *model.RatingOption) {
	m.Label = req.Label
	m.Name = req.Name
	m.Active = req.Active
	m.RatingType = req.RatingType
	m.RadioChoices = req.RadioChoices
	m.CreatedBy = req.CreatedBy
}
