package rating

import (
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

const (
	RatingParamKey = "id"
)

type RatingAdminAPI struct {
	ratingOptionRepo repository.RatingOptionRepository
}

func validateRadio(gCtx *gin.Context, req *RatingOptionAdminRequest) bool {
	if len(req.RadioChoices) > 0 {
		ids := make([]int, 0)
		for _, o := range req.RadioChoices {
			if o.ID == 0 {
				apiutil.ErrBadRequest(gCtx, apiError.ErrChoiceIDNotExist())
				return true
			}

			if contains(ids, o.ID) {
				apiutil.ErrBadRequest(gCtx, apiError.ErrDuplicateChoiceID())
				return true
			}
			ids = append(ids, o.ID)
		}
	}
	return false
}

func (r *RatingAdminAPI) Create(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	req, err := NewRatingOptionAdminRequest(gCtx)
	if err != nil {
		apiutil.InvalidJSONRequest(gCtx)
		return
	}

	ro := req.ToRatingOption()

	hasError := validateRadio(gCtx, req)
	if hasError {
		return
	}

	err = r.ratingOptionRepo.Create(ctx, &ro)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func (r *RatingAdminAPI) GetAll(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	roc, err := r.ratingOptionRepo.GetAll(ctx, repository.RatingOptionQueryAll)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}
	roResponses := make([]RatingOptionAdminResponse, len(roc))
	for i, v := range roc {
		p := NewRatingOptionAdminResponse(v)
		roResponses[i] = p
	}

	apiutil.OK(gCtx, roResponses)
}

func (r *RatingAdminAPI) Get(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	id := gCtx.Param(RatingParamKey)
	ro, err := r.ratingOptionRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, NewRatingOptionAdminResponse(*ro))
}

func contains(nums []int, num int) bool {
	for _, v := range nums {
		if v == num {
			return true
		}
	}
	return false
}

func (r *RatingAdminAPI) Update(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	req, err := NewRatingOptionAdminRequest(gCtx)
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInternal(err))
		return
	}

	id := gCtx.Param(RatingParamKey)
	ro, err := r.ratingOptionRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrNotFound(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	hasError := validateRadio(gCtx, req)
	if hasError {
		return
	}

	req.UpdateRatingOption(ro)
	err = r.ratingOptionRepo.Update(ctx, ro)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func (r *RatingAdminAPI) Delete(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	id := gCtx.Param(RatingParamKey)
	ro, err := r.ratingOptionRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrNotFound(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	err = r.ratingOptionRepo.Delete(ctx, ro)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func ProvideRatingAdminAPI(ratingOptionRepo repository.RatingOptionRepository) *RatingAdminAPI {
	return &RatingAdminAPI{ratingOptionRepo: ratingOptionRepo}
}
