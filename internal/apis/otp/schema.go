package otp

type GenerationRequest struct {
	LineUID string `json:"lineUid" example:"U0123"`
	// Phone number in 0-prefixed closed dialing format
	PhoneNumber string `json:"phoneNumber" binding:"required" example:"0812345678"`
}

type OtpLineUidRequest struct {
	LineUID string `json:"lineUid" binding:"required" example:"U0123"`
}

type GenerationResponse struct {
	// seconds before OTP expires
	LineSpanInSec int `json:"lifeSpanInSec" example:"300"`
	// number of times to try to validate OTP before being temporarily locked out
	ValidationRemainingTries int `json:"validationRemainingTries" example:"3"`
	// seconds until one can generate OTP, again
	IntervalInSec int `json:"intervalInSec" example:"30"`
}

type ValidationRequest struct {
	LineUID string `json:"lineUid" example:"U0123"`
	OTP     string `json:"OTP" binding:"required" example:"12345"`
}

type ValidationResponse struct {
	// Token is used to verify that a lineUID and a phone number have been validated
	Token string `json:"token"`
}
