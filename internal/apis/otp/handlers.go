package otp

import (
	"fmt"
	"math/rand"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/sms"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

const KeyLineAccessToken = "x-line-access-token"

type OTPAPI struct {
	sms           sms.SMS
	repo          repository.OTPSessionRepo
	tokenVerifier auth.TokenVerifier
}

// Generate
// @summary Generates OTP and sends SMS
// @router /v1/otp [POST]
// @tags SMS
// @accept json
// @produce json
// @param req body GenerationRequest true "Generation Request"
// @success 200 {object} GenerationResponse
func (api *OTPAPI) Generate(gctx *gin.Context) {
	var req GenerationRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		_ = gctx.Error(err)
		return
	}

	lineAccessToken := gctx.GetHeader(KeyLineAccessToken)
	if lineAccessToken == "" && req.LineUID == "" {
		var uidReq OtpLineUidRequest
		if err := gctx.ShouldBindJSON(&uidReq); err != nil {
			gctx.JSON(http.StatusBadRequest, utils.ErrorStructResponse(err))
			return
		}
	} else if lineAccessToken != "" {
		profile, err := api.tokenVerifier.GetUserProfile(gctx, lineAccessToken)
		if err != nil {
			logrus.WithContext(gctx).Warnf("get user profile from line access token error: %v", err)
			apiutil.ErrInternalError(gctx, apiutil.NewFromString(absapi.ERRCODE_INTERNAL_ERROR, "get user profile from line access token error"))
			return
		}
		req.LineUID = profile.UserID
	}

	ctx := gctx.Request.Context()

	ses, err := api.repo.LoadOrNew(ctx, req.LineUID)
	if err != nil {
		_ = gctx.Error(err)
		return
	}
	ses.PhoneNumber = req.PhoneNumber
	originalSes := ses

	if err := ses.GenerateOTP(); err != nil {
		switch err {
		case model.OTPValidateBanError:
			_ = gctx.Error(validateBanError(ses.BanExpiration))
		case model.OTPGenBanError:
			_ = gctx.Error(genBanError(ses.BanExpiration))
		case model.OTPGenIntervalError:
			_ = gctx.Error(genIntervalError(ses.WaitIntervalExpiration()))
		default:
			_ = gctx.Error(err)
		}
		return
	}

	if _, err := api.sms.Send(ctx, sms.SendRequest{
		Phone:       toE164(req.PhoneNumber),
		Message:     fmt.Sprintf("%s คือ OTP สำหรับการสมัคร LINE MAN Rider", ses.OTP),
		ServiceKind: "OTP",
		RetryCount:  ses.GenCount - 1,
	}); err != nil {
		originalSes.GenCount += 1
		if err := api.repo.Save(ctx, originalSes); err != nil {
			_ = gctx.Error(err)
		}
		_ = gctx.Error(err)
		return
	}

	if err := api.repo.Save(ctx, ses); err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.OK(gctx, GenerationResponse{
		LineSpanInSec:            durationRemainingInSec(ses.OTPExpiration()),
		ValidationRemainingTries: ses.RemainingValidateCount(),
		IntervalInSec:            durationRemainingInSec(ses.WaitIntervalExpiration()),
	})
}

func toE164(internalPhone string) string {
	if len(internalPhone) < 1 {
		return internalPhone
	}
	return "66" + internalPhone[1:]
}

// @summary Validates OTP
// @router /v1/otp/validate [POST]
// @tags SMS
// @accept json
// @produce json
// @param req body ValidationRequest true "Validation Request"
// @success 204 "No Content"
func (api *OTPAPI) Validate(gctx *gin.Context) {
	var req ValidationRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		_ = gctx.Error(err)
		return
	}

	lineAccessToken := gctx.GetHeader(KeyLineAccessToken)
	if lineAccessToken == "" && req.LineUID == "" {
		var uidReq OtpLineUidRequest
		if err := gctx.ShouldBindJSON(&uidReq); err != nil {
			gctx.JSON(http.StatusBadRequest, utils.ErrorStructResponse(err))
			return
		}
	} else if lineAccessToken != "" {
		profile, err := api.tokenVerifier.GetUserProfile(gctx, lineAccessToken)
		if err != nil {
			logrus.WithContext(gctx).Warnf("get user profile from line access token error: %v", err)
			apiutil.ErrInternalError(gctx, apiutil.NewFromString(absapi.ERRCODE_INTERNAL_ERROR, "get user profile from line access token error"))
			return
		}
		req.LineUID = profile.UserID
	}

	ctx := gctx.Request.Context()

	ses, err := api.repo.Load(ctx, req.LineUID)
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	if err := ses.Validate(req.OTP); err != nil {
		if err == model.OTPValidateBanError {
			_ = gctx.Error(validateBanError(ses.BanExpiration))
			return
		}

		if saveErr := api.repo.Save(ctx, ses); saveErr != nil {
			_ = gctx.Error(saveErr)
			return
		}

		switch {
		case ses.IsValidateBanned():
			_ = gctx.Error(validateBanError(ses.BanExpiration))
		case ses.IsOTPExpired():
			_ = gctx.Error(expiredError(ses.RemainingValidateCount()))
		default:
			_ = gctx.Error(wrongOTPError(ses.RemainingValidateCount()))
		}
		return
	}

	if err := api.repo.Save(ctx, ses); err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.NoContent(gctx)
}

func init() {
	rand.Seed(time.Now().UnixNano())
}

func ProvideOTPAPI(sms sms.SMS, repo repository.OTPSessionRepo, tokenVerifier auth.TokenVerifier) *OTPAPI {
	return &OTPAPI{
		sms:           sms,
		repo:          repo,
		tokenVerifier: tokenVerifier,
	}
}
