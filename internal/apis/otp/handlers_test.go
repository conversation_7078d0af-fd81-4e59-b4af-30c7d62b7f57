package otp_test

import (
	"bytes"
	"errors"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/otp"
	"git.wndv.co/lineman/fleet-distribution/internal/auth/mock_auth"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/sms"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/sms/mock_sms"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

var newOTPSessionfn = model.ProvideNewOTPSession(model.OTPConfig{
	OTPLifespan:         time.Duration(20) * time.Minute,
	MaximumValidate:     5,
	ValidateBanDuration: time.Duration(20) * time.Minute,
	GenBanDuration:      time.Duration(20) * time.Minute,
	GenInterval:         time.Duration(0),
	MaximumGens:         5,
}, time.Now)

func TestOTPAPI_Generate(t *testing.T) {
	lineUID := "U123"
	phoneNumber := "0123"

	t.Run("should behaves as expected, if no errors", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, mcks := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		gctx := createGCTX(genReqBody(lineUID, phoneNumber))

		mcks.repo.EXPECT().
			LoadOrNew(gomock.Any(), lineUID).
			Return(newOTPSessionfn(lineUID), nil)
		mcks.repo.EXPECT().
			Save(gomock.Any(), gomock.Any()).
			Return(nil)
		mcks.sms.EXPECT().
			Send(gomock.Any(), gomock.Any()).
			Return(sms.SendResponse{}, nil)

		api.Generate(gctx)
		assert.Empty(tt, gctx.Errors.Errors())
	})

	t.Run("should generate otp correctly (using line access token)", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, mcks := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		gctx := createGCTX(genReqBody("", phoneNumber))

		mcks.repo.EXPECT().
			LoadOrNew(gomock.Any(), "lineUid").
			Return(newOTPSessionfn("lineUid"), nil)
		mcks.repo.EXPECT().
			Save(gomock.Any(), gomock.Any()).
			Return(nil)
		mcks.sms.EXPECT().
			Send(gomock.Any(), gomock.Any()).
			Return(sms.SendResponse{}, nil)

		mcks.tokenVerifier.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&line.Profile{UserID: "lineUid"}, nil)
		gctx.Request.Header.Add("x-line-access-token", "fakeLineAccessToken")

		api.Generate(gctx)

		assert.Empty(tt, gctx.Errors.Errors())
	})

	t.Run("should return 400 when generate otp without line uid and line access token", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, _ := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		gctx, recorder := testutil.TestRequestContext("POST", "/v1/otp", testutil.JSON(otp.GenerationRequest{PhoneNumber: phoneNumber}))

		api.Generate(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("returns error on cache down", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, mcks := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		mcks.repo.EXPECT().
			LoadOrNew(gomock.Any(), gomock.Any()).
			Return(model.OTPSession{}, errors.New("some error"))

		gctx := createGCTX(genReqBody(lineUID, phoneNumber))

		api.Generate(gctx)
		assert.NotEmpty(tt, gctx.Errors.Errors())
	})

	t.Run("if unable to send, returns error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, mcks := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		genGCTX := createGCTX(genReqBody(lineUID, phoneNumber))
		ses := newOTPSessionfn(lineUID)

		mcks.repo.EXPECT().
			LoadOrNew(gomock.Any(), lineUID).
			Return(ses, nil)
		mcks.sms.EXPECT().
			Send(gomock.Any(), gomock.Any()).
			Return(sms.SendResponse{}, errors.New("some error"))
		mcks.repo.EXPECT().
			Save(gomock.Any(), gomock.Any()).
			Return(nil)

		api.Generate(genGCTX)
		assert.NotEmpty(tt, genGCTX.Errors.Errors())
	})
}

func TestOTPAPI_Validate(t *testing.T) {
	t.Run("validate behaves as expected, if no errors", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, mcks := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		lineUID := "U123"
		code := "1234"

		ses := newOTPSessionfn(lineUID)
		ses.OTP = code
		ses.Expiration = time.Now().Add(time.Duration(5) * time.Hour)

		gctx := createGCTX(valReqBody(lineUID, code))
		mcks.repo.EXPECT().
			Load(gomock.Any(), gomock.Any()).
			Return(ses, nil)

		mcks.repo.EXPECT().
			Save(gomock.Any(), gomock.Any()).
			Return(nil)

		api.Validate(gctx)
		assert.Empty(tt, gctx.Errors.Errors())
	})

	t.Run("should validate otp correctly (using line access token)", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, mcks := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		lineUID := "U123"
		code := "1234"

		ses := newOTPSessionfn("")
		ses.OTP = code
		ses.Expiration = time.Now().Add(time.Duration(5) * time.Hour)

		gctx := createGCTX(valReqBody(lineUID, code))
		mcks.repo.EXPECT().
			Load(gomock.Any(), gomock.Any()).
			Return(ses, nil)

		mcks.repo.EXPECT().
			Save(gomock.Any(), gomock.Any()).
			Return(nil)

		mcks.tokenVerifier.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&line.Profile{UserID: lineUID}, nil)
		gctx.Request.Header.Add("x-line-access-token", "fakeLineAccessToken")

		api.Validate(gctx)
		assert.Empty(tt, gctx.Errors.Errors())
	})

	t.Run("returns error on cache down", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, mcks := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		gctx := createGCTX(valReqBody("U123", "1234"))

		mcks.repo.EXPECT().
			Load(gomock.Any(), gomock.Any()).
			Return(model.OTPSession{}, errors.New("some error"))

		api.Validate(gctx)
		assert.NotEmpty(tt, gctx.Errors.Errors())
	})

	t.Run("should return 400 when validate otp without line uid and line access token", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, _ := newTestOTPAPI(ctrl)
		defer ctrl.Finish()

		gctx, recorder := testutil.TestRequestContext("POST", "/v1/otp/validate", testutil.JSON(otp.ValidationRequest{OTP: "1234"}))

		api.Validate(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})
}

func createGCTX(body string) *gin.Context {
	gctx, _ := testutil.TestRequestContext("", "", bytes.NewBufferString(body))
	return gctx
}

func genReqBody(lineUID string, phoneNumber string) string {
	return fmt.Sprintf(`
		{
			"lineUid":     "%s",
			"phoneNumber": "%s"
		}
	`, lineUID, phoneNumber)
}

func valReqBody(lineUID string, otp string) string {
	return fmt.Sprintf(`
		{
			"lineUid": "%s",
			"otp":     "%s"
		}
	`, lineUID, otp)
}

type mocks struct {
	repo          *mock_repository.MockOTPSessionRepo
	sms           *mock_sms.MockSMS
	tokenVerifier *mock_auth.MockTokenVerifier
}

func newTestOTPAPI(ctrl *gomock.Controller) (*otp.OTPAPI, *mocks) {
	mcks := &mocks{
		repo:          mock_repository.NewMockOTPSessionRepo(ctrl),
		sms:           mock_sms.NewMockSMS(ctrl),
		tokenVerifier: mock_auth.NewMockTokenVerifier(ctrl),
	}

	otpAPI := otp.ProvideOTPAPI(mcks.sms, mcks.repo, mcks.tokenVerifier)

	return otpAPI, mcks
}
