//go:build integration_test
// +build integration_test

package order_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/proto"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"git.wndv.co/lineman/fleet-distribution/toggle"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestFoodProvider_Accept(t *testing.T) {
	t.Run("can accept auto-assigned order without captcha", func(tt *testing.T) {
		ftd := newFoodTestData(tt)

		ftd.autoAssignOrderToDriver(1, ftd.autoAssignedOrderID, ftd.driverID)

		testAcceptOrderSuccessfully(tt, ftd, ftd.autoAssignedOrderID, ftd.driverID, "{}")
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), ftd.autoAssignedOrderID)
		require.NoError(tt, err)
		assert.Nil(tt, o.LeavePrevStopAt)
	})

	t.Run("accept order publish the distribution event correctly", func(tt *testing.T) {
		ftd := newFoodTestData(tt)

		ftd.autoAssignOrderToDriver(1, ftd.autoAssignedOrderID, ftd.driverID)

		testAcceptOrderSuccessfully(tt, ftd, ftd.autoAssignedOrderID, ftd.driverID, "{}")
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), ftd.autoAssignedOrderID)
		require.NoError(tt, err)
		assert.Nil(tt, o.LeavePrevStopAt)
		assert.Len(tt, ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder, 2)
		assert.Equal(tt, ftd.autoAssignedOrderID, ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder[0].Key)
		event := &driverv1.OrderDistributionEvent{}
		require.NoError(tt, proto.Unmarshal(ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder[0].Payload, event))
		assert.Equal(tt, ftd.autoAssignedOrderID, event.OrderId)
		assert.Equal(tt, string(model.AssigningDriverDistributionEventName), event.EventName)
		assert.Nil(tt, event.DriverId)

		assert.Equal(tt, ftd.autoAssignedOrderID, ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder[1].Key)
		event = &driverv1.OrderDistributionEvent{}
		require.NoError(tt, proto.Unmarshal(ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder[1].Payload, event))
		assert.Equal(tt, ftd.autoAssignedOrderID, event.OrderId)
		assert.Equal(tt, string(model.DriverMatchedDistributionEventName), event.EventName)
		assert.Equal(tt, ftd.driverID, *event.DriverId)
	})

	t.Run("accept order publish the distribution event correctly", func(tt *testing.T) {
		ftd := newFoodTestData(tt)

		ftd.autoAssignOrderToDriver(1, ftd.orderWithActualAssigningID, ftd.driverID)

		testAcceptOrderSuccessfully(tt, ftd, ftd.orderWithActualAssigningID, ftd.driverID, "{}")
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), ftd.orderWithActualAssigningID)
		require.NoError(tt, err)
		assert.Nil(tt, o.LeavePrevStopAt)
		assert.Len(tt, ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder, 1)
		assert.Equal(tt, ftd.orderWithActualAssigningID, ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder[0].Key)
		event := &driverv1.OrderDistributionEvent{}
		require.NoError(tt, proto.Unmarshal(ftd.ctn.SecureIMFKafkaProducerClientForTest.Recorder[0].Payload, event))
		assert.Equal(tt, ftd.orderWithActualAssigningID, event.OrderId)
		assert.Equal(tt, string(model.DriverMatchedDistributionEventName), event.EventName)
		assert.Equal(tt, ftd.driverID, *event.DriverId)
	})

	t.Run("accept auto-assigned order after assign to another driver", func(tt *testing.T) {
		ftd := newFoodTestData(tt)

		ftd.autoAssignOrderToDriver(1, ftd.autoAssignedOrderID, ftd.driverID)
		ftd.autoAssignOrderToDriver(2, ftd.autoAssignedOrderID, "anotherdriver")

		gctx := testutil.NewContextWithRecorder()
		ftd.buildAcceptRequest(gctx, ftd.autoAssignedOrderID, ftd.driverID, "{}", true)

		ctx := context.Background()
		ftd.updateDriverLocation(ctx, ftd.driverID)
		ftd.ctn.StubMapService.FastestRoute = &model.MapRoute{Distance: 1, Duration: 1}

		gctx.Send(ftd.ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, 400)

		var errResp errorResp
		gctx.DecodeJSONResponse(&errResp)
		require.True(tt, strings.Contains(errResp.Error, "was assigned to another driver"))
	})

	t.Run("queue order", func(tt *testing.T) {
		ftd := newFoodTestData(tt)

		require.NoError(tt, ftd.ctn.Fixtures.InitFixture(ftd.ctn.DBConnectionForTest, "fixtures_trips"))

		driverID := "ASSIGNED"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID:        driverID,
				AssignedOrders: []prediction.AssignedOrder{{OrderID: ftd.autoAssignedOrderID}},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-10",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-10",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    ftd.autoAssignedOrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    ftd.autoAssignedOrderID,
						ActionType: prediction.DropOffAction,
					},
				},
			},
		}

		_, err := ftd.ctn.MongoAssignmentLogRepository.AssignToDrivers(context.Background(), 1, ftd.autoAssignedOrderID, 0, []repository.DriverDistance{{DriverID: driverID, Distance: 0}}, model.AssignmentLogOpt{AutoAssigned: true, AllowQueueing: true, PlanRoute: planRoute})
		if err != nil {
			panic(err)
		}

		testAcceptOrderSuccessfully(tt, ftd, ftd.autoAssignedOrderID, driverID, "{}")
		driver, err := ftd.ctn.DriverRepository.FindDriverID(context.Background(), driverID)
		require.NoError(tt, err)
		assert.Equal(tt, []string{ftd.autoAssignedOrderID}, driver.QueueingOrders)
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), ftd.autoAssignedOrderID)
		require.NoError(tt, err)
		assert.Nil(tt, o.LeavePrevStopAt)
	})

	t.Run("change status to driver to restaurant when order is switch flow", func(tt *testing.T) {
		ftd := newFoodTestData(tt)
		orderModel := getOrder(tt, ftd.ctn, ftd.orderID)
		orderModel.Options.SwitchFlow = true
		orderModel.Routes[0].Info = model.StopInfoCollector{
			StopInfo: &model.StopInfoFood{
				Service:     model.ServiceFood,
				PriceScheme: model.PriceSchemeRMS,
			},
		}

		err := ftd.ctn.MongoOrderRepository.UpdateOrder(context.Background(), orderModel)
		require.NoError(tt, err)
		testAcceptOrderSuccessfully(tt, ftd, ftd.orderID, ftd.driverID, captchaBody(ftd.correctID))
		orderModel = getOrder(tt, ftd.ctn, ftd.orderID)
		require.Equal(tt, model.StatusDriverToRestaurant, orderModel.Status)
		assert.NotNil(tt, orderModel.LeavePrevStopAt)
	})

	t.Run("change status to driver match when order is not switch flow", func(tt *testing.T) {
		ftd := newFoodTestData(tt)
		orderModel := getOrder(tt, ftd.ctn, ftd.orderID)
		orderModel.Options.SwitchFlow = false
		orderModel.Routes[0].Info = model.StopInfoCollector{
			StopInfo: &model.StopInfoFood{
				Service:     model.ServiceFood,
				PriceScheme: model.PriceSchemeRMS,
			},
		}

		err := ftd.ctn.MongoOrderRepository.UpdateOrder(context.Background(), orderModel)
		require.NoError(tt, err)
		testAcceptOrderSuccessfully(tt, ftd, ftd.orderID, ftd.driverID, captchaBody(ftd.correctID))
		orderModel = getOrder(tt, ftd.ctn, ftd.orderID)
		require.Equal(tt, model.StatusDriverMatched, orderModel.Status)
		assert.Nil(tt, orderModel.LeavePrevStopAt)
	})

	t.Run("Accept auto assign order with force offlined driver should be error as expected", func(tt *testing.T) {
		ftd := newFoodTestData(tt)

		err := ftd.ctn.DriverRepository.SetCurrentStatus(context.Background(), "DRV_PATTAYA_ONLINE", model.StatusOffline)
		require.NoError(tt, err)

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/order/LMF-4/accept")
		ctx.Authorized(ftd.ctn.RedisTokenStore, "DRV_PATTAYA_ONLINE")
		ctx.Send(ftd.ctn.GinEngineRouter)

		type info struct {
			Title  string `json:"title"`
			Detail string `json:"detail"`
		}
		errorRes := struct {
			Code    string `json:"code"`
			Message string `json:"message"`
			Info    info   `json:"info"`
		}{}
		ctx.AssertResponseCode(t, 400)
		ctx.DecodeJSONResponse(&errorRes)
		require.Equal(tt, "DRIVER_STATUS_OFFLINE", errorRes.Code)
		require.Equal(tt, "force driver status to offline", errorRes.Message)
		require.Equal(tt, "เราปิดรับงานให้คุณ", errorRes.Info.Title)
		require.Equal(tt, "คุณจะปิดรับงานโดยอัตโนมัติในกรณีที่คุณกดรับงานไม่ทัน", errorRes.Info.Detail)
	})

	t.Run("banned withdraw driver should not accept order in contingency mode", func(tt *testing.T) {
		ftd := newFoodTestData(tt)
		ftd.ctn.Acceptor.Dep.ContingencyCfg = order.NewAtomicContingencyConfig(order.ContingencyConfig{ContingencyModeEnabled: true, LimitMinusCredit: -1000.0})
		gctx := testutil.NewContextWithRecorder()

		orderModel := getOrder(tt, ftd.ctn, ftd.orderID)
		orderModel.Routes[0].Info = model.StopInfoCollector{
			StopInfo: &model.StopInfoFood{
				Service:     model.ServiceFood,
				PriceScheme: model.PriceSchemeRMS,
			},
		}

		drivTran, err := ftd.ctn.DataStoreDriverTransactionRepository.FindByID(context.Background(), ftd.driverID)
		require.NoError(tt, err)
		drivTran.BanWithdraw("Test", "<test-order-1>", "<EMAIL>")
		err = ftd.ctn.DataStoreDriverTransactionRepository.Update(context.Background(), drivTran)
		require.NoError(tt, err)

		err = ftd.ctn.MongoOrderRepository.UpdateOrder(context.Background(), orderModel)
		require.NoError(tt, err)
		ftd.buildAcceptRequest(gctx, ftd.orderID, ftd.driverID, captchaBody(ftd.correctID), true)

		ctx := context.Background()
		ftd.updateDriverLocation(ctx, ftd.driverID)
		ftd.ctn.StubMapService.FastestRoute = &model.MapRoute{Distance: 1, Duration: 1}

		gctx.Send(ftd.ctn.GinEngineRouter)
		gctx.AssertResponseCode(tt, 400)

		var errResp api.Error
		gctx.DecodeJSONResponse(&errResp)
		require.Equal(t, errResp.Code, "DRIVER_BANNED_WITHDRAW")
	})
}

func TestFoodProvider_CompletePrincipleOrder(t *testing.T) {
	t.Run("If rider completion order step failed should rollback mongo transaction", func(t *testing.T) {
		driverID := "DRV_COMPLETED_ROLLBACK"
		orderID := "LMF-COMPLETED-ROLLBACK"

		t.Run("Legacy status is ARRIVED", func(tt *testing.T) {
			// Given
			ctn := ittest.NewContainer(tt)
			o := getOrder(tt, ctn, orderID)
			o.Status = model.StatusDriverArrived
			err := ctn.MongoOrderRepository.UpdateOrder(context.Background(), o)
			require.NoError(tt, err)

			gctx := createUpdateStatusRequest(ctn, driverID, orderID)

			// When
			gctx.Send(ctn.GinEngineRouter)

			// Then
			gctx.AssertResponseCode(tt, 500)
			order, err := ctn.MongoOrderRepository.Get(context.Background(), orderID)
			require.NoError(tt, err)
			require.Equal(tt, model.StatusDriverArrived, order.Status)
		})

		t.Run("Revamped status is ARRIVED_AT", func(tt *testing.T) {
			// Given
			ctn := ittest.NewContainer(tt)
			o := getOrder(tt, ctn, orderID)
			o.Status = model.StatusArrivedAt
			o.HeadTo = len(o.Routes) - 1
			o.RevampedStatus = true
			err := ctn.MongoOrderRepository.UpdateOrder(context.Background(), o)
			require.NoError(tt, err)

			gctx := createUpdateStatusRequest(ctn, driverID, orderID)

			// When
			gctx.Send(ctn.GinEngineRouter)

			// Then
			gctx.AssertResponseCode(tt, 500)
			order, err := ctn.MongoOrderRepository.Get(context.Background(), orderID)
			require.NoError(tt, err)
			require.Equal(tt, model.StatusArrivedAt, order.Status)
		})
	})

	t.Run("`CASH` payment method", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     73.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
		}
		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)

		require.Equal(t, 3, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   73.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("complete b2b order should dequeue with no error", func(t *testing.T) {
		container := ittest.NewContainer(t)
		if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_dequeue_b2b"); err != nil {
			t.Errorf("Unexpected error initfixture: %v", err)
		}
		pp := prepareTransaction{
			driverID:      "DRV_B2B_DEQUEUE",
			orderID:       "LMF-10",
			paymentMethod: model.PaymentMethodCash,
		}
		_, err := completeOrderTransaction(t, container, pp)
		require.NoError(t, err)
	})

	t.Run("`E-PAYMENT` payment method", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-12"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     40,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     "",
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 2, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`E-PAYMENT` payment method with not enough credit but can transfer from wallet", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-12"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     40,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     "",
			driverBalance: &prepareDriverBalance{
				driverCredit: 0.0,
				driverWallet: 0.0,
			},
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   1.20,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`CASH-ADVANCE` payment method", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-13"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     73.0,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     "",
		}

		trx, err := completeOrderTransaction(t, container, pp)
		require.NoError(t, err)
		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   73.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`CASH-ADVANCE` payment method when enough credit + wallet", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-13"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     120.0,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     "",
			driverBalance: &prepareDriverBalance{
				driverCredit: 70,
				driverWallet: 0,
			},
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   51.20,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   51.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   120.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`CASH-ADVANCE` payment method when enough credit + wallet with credit outstanding", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-13"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     120.0,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     "",
			driverBalance: &prepareDriverBalance{
				driverCredit: 10,
				driverWallet: 0,
			},
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 7, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   90.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   90.00,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   120.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   21.20,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`CASH-COLLECTION` payment method", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-11"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 50,
			userDeliveryFee:     100,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  100.00,
				SubTotal: 100.00,
				Total:    100.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 301.50,
				driverWallet: 0,
			},
			driverMoneyFlow: model.FlowCashCollection,
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   100.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   100.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`CASH-COLLECTION` payment method and deduct wallet when credit not enough and transfer amount equal delivery fee", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-14"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 50,
			userDeliveryFee:     100.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  50.00,
				SubTotal: 50.00,
				Total:    50.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 101.50,
				driverWallet: 0,
			},
			driverMoneyFlow: model.FlowCashCollection,
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.50,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   100.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		dtt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, types.Money(0), dtt.WalletBalance)
		require.Equal(t, types.Money(0), dtt.CreditBalance())
	})

	t.Run("`CASH-COLLECTION` doesn't transfer credit -> wallet if driver is withdraw-banned", func(t *testing.T) {
		driverID := "DRV_BANNED"
		orderID := "LMF-14"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 50,
			userDeliveryFee:     100.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  50.00,
				SubTotal: 50.00,
				Total:    50.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 101.50,
				driverWallet: 0,
			},
			driverMoneyFlow: model.FlowCashCollection,
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 5, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   100.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   50.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		dtt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, types.Money(50), dtt.WalletBalance)
		require.Equal(t, types.Money(-50), dtt.CreditBalance())
	})

	t.Run("`CASH-COLLECTION` payment method deduct wallet when credit not enough and transfer amount less than delivery fee", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-14"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 80.00,
			userDeliveryFee:     100.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashCollection,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  50.00,
				SubTotal: 50.00,
				Total:    50.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 102.40,
				driverWallet: 0,
			},
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   80.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   2.40,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   100.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		dtt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, types.Money(30), dtt.WalletBalance)
		require.Equal(t, types.Money(0), dtt.CreditBalance())
	})

	t.Run("`CASH-COLLECTION` payment method deduct wallet when credit not enough and transfer greater than delivery fee", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-14"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 50.00,
			userDeliveryFee:     100.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashCollection,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  80.00,
				SubTotal: 80.00,
				Total:    80.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 101.50,
				driverWallet: 0,
			},
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)

		require.Equal(t, 7, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.50,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   50.0,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   50.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   80.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   100.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   30.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		dtt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, types.Money(-0), dtt.WalletBalance)
		require.Equal(t, types.Money(-30), dtt.CreditBalance())
	})

	t.Run("`CASH-COLLECTION` payment method should have on top schema", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-14"

		container := ittest.NewContainer(t)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 50.00,
			userDeliveryFee:     100.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashCollection,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  80.00,
				SubTotal: 80.00,
				Total:    80.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 101.50,
				driverWallet: 0,
			},
			ontop: &prepareOntop{
				id:               "fb8ca76b29cb4a61b550c65b4beb637b",
				name:             "AA7 - Basket Size พื้นที่เกาะอยุธยา",
				amount:           10.0,
				incentiveName:    "inc",
				incentiveSources: []string{"inc_s1", "inc_s2"},
			},
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 9, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   50.0,
			},
			{
				Category:         model.WalletTransactionCategory,
				Type:             model.OnTopTransactionType,
				Amount:           10.0,
				IncentiveNames:   []string{"AA7 - Basket Size พื้นที่เกาะอยุธยา"},
				IncentiveSources: []string{"inc_s1", "inc_s2"},
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.50,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   60.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   60.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   80.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   100.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   0.3,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   20.3,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		dtt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, types.Money(0), dtt.WalletBalance)
		require.Equal(t, types.Money(-20.3), dtt.CreditBalance())
	})

	t.Run("`CASH` payment method enough wallet to transfer and not enough credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.5,
			userDeliveryFee:     50.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverBalance: &prepareDriverBalance{
				driverCredit: 5.0,
				driverWallet: 100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         96.23,
			PurchaseCreditBalance: 0.00,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -100,
		})

		require.NoError(t, err)

		require.Equal(t, 5, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   46.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   46.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   50.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH` GP payment method enough wallet to transfer and not enough credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverBalance: &prepareDriverBalance{
				driverCredit: 0.0,
				driverWallet: 100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         141.23,
			PurchaseCreditBalance: 0.00,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -100,
		})

		require.NoError(t, err)

		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   1.27,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH-COLLECTION` payment method enough wallet to transfer and not enough credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashCollection,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  120.00,
				SubTotal: 120.00,
				Total:    120.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 0.0,
				driverWallet: 100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         21.23,
			PurchaseCreditBalance: 0,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 5, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   121.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   121.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   120.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH-ADVANCEMENT` payment method enough wallet to transfer and not enough credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			coupon:              10.00,
			driverMoneyFlow:     model.FlowCashAdvancementCoupon,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  110.00,
				SubTotal: 120.00,
				Total:    110.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 0.0,
				driverWallet: 100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         151.23,
			PurchaseCreditBalance: 0,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 5, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   10.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH-ADVANCEMENT` payment method enough wallet to transfer and not enough credit (minus credit, not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			coupon:              10.00,
			driverMoneyFlow:     model.FlowCashAdvancementCoupon,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  110.00,
				SubTotal: 120.00,
				Total:    110.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: 0.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         0.0,
			PurchaseCreditBalance: -48.77,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   52.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   52.50,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   10.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   48.77,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`E-PAYMENT` payment method enough wallet to transfer and not enough credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  120.00,
				SubTotal: 120.00,
				Total:    120.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: 0.0,
				driverWallet: 100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         141.23,
			PurchaseCreditBalance: 0,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   1.27,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`E-PAYMENT` payment method enough wallet to transfer and not enough credit (minus credit, not break limit) in contingency mode", func(t *testing.T) {
		t.SkipNow()
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  120.00,
				SubTotal: 120.00,
				Total:    120.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: 0.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         0.0,
			PurchaseCreditBalance: -58.77,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 5, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   42.5,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   58.77,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH` GP payment method not enough wallet and credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: 0.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         0.00,
			PurchaseCreditBalance: -58.77,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -100,
		})

		require.NoError(t, err)

		require.Equal(t, 5, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   58.77,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH` GP 10 payment method not enough wallet and credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     10.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: 0.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         0.00,
			PurchaseCreditBalance: -68.77,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -100,
		})

		require.NoError(t, err)

		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   10.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   68.77,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH-COLLECTION` payment method not enough wallet and credit (not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     10.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashCollection,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  120.00,
				SubTotal: 120.00,
				Total:    120.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: 0.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         0.00,
			PurchaseCreditBalance: -188.77,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 7, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.WithdrawTransactionType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.PurchaseTransactionType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   120.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   10.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   188.77,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH-COLLECTION` payment method not enough wallet and credit (minus wallet, not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     10.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashCollection,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  120.00,
				SubTotal: 120.00,
				Total:    120.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: -100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         -57.5,
			PurchaseCreditBalance: -231.27,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -1000,
		})

		require.NoError(t, err)

		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   120.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.UserDeliveryFeeType,
				Amount:   10.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   231.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   57.50,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH-ADVANCEMENT` payment method not enough wallet and credit (minus wallet, not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
			coupon:              10.00,
			driverMoneyFlow:     model.FlowCashAdvancementCoupon,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  110.00,
				SubTotal: 120.00,
				Total:    110.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: -100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         -47.5,
			PurchaseCreditBalance: -101.27,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 5, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   10.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   101.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   47.5,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`E-PAYMENT` payment method not enough wallet and credit (minus wallet, not break limit) in contingency mode", func(t *testing.T) {
		t.SkipNow()
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			userDeliveryFee:     0.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  120.00,
				SubTotal: 120.00,
				Total:    120.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: -100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         -57.5,
			PurchaseCreditBalance: -101.27,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.50,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   101.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   57.5,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH ADVANCEMENT E-PAYMENT` payment method", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-18"

		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_cash_advancement_epayment")
		if err != nil {
			panic(err)
		}

		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashAdvancementEpayment,
		}

		trx, err := completeOrderTransaction(t, container, pp)
		require.NoError(t, err)
		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   18.00,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   50.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`CASH-ADVANCEMENT E-Payment` payment method not enough wallet and credit (minus wallet, not break limit) in contingency mode", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-19"

		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_cash_advancement_epayment")
		if err != nil {
			panic(err)
		}

		pp := prepareTransaction{
			deliveryFeeSubtotal: 42.50,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			coupon:              10.00,
			driverMoneyFlow:     model.FlowCashAdvancementEpayment,
			itemFee: &model.ItemFeeSummary{
				ItemFee:  10.00,
				SubTotal: 20.00,
				Total:    10.00,
			},
			driverBalance: &prepareDriverBalance{
				driverCredit: -100.0,
				driverWallet: -100.0,
			},
		}
		expectedDrivTrans := model.DriverTransaction{
			WalletBalance:         -37.5,
			PurchaseCreditBalance: -101.27,
		}
		trx, err := completeOrderTransactionInContingencyMode(t, container, pp, order.ContingencyConfig{
			ContingencyModeEnabled: true, LimitMinusCredit: -200,
		})

		require.NoError(t, err)

		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   42.5,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   10.00,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   10.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   101.27,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.OutstandingTransactionType,
				Amount:   37.5,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))

		actualDrivTrans, err := container.DriverTransactionService.GetDriverTransaction(context.Background(), driverID, repository.WithReadPrimary)
		require.NoError(t, err)
		require.Equal(t, expectedDrivTrans.WalletBalance, actualDrivTrans.WalletBalance)
		require.Equal(t, expectedDrivTrans.PurchaseCreditBalance, actualDrivTrans.PurchaseCreditBalance)
	})

	t.Run("`CASH ADVANCEMENT E-PAYMENT NON-RMS` payment method", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-30"

		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_cash_advancement_epayment")
		require.NoError(t, err)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashAdvancementEpayment,
		}

		trx, err := completeOrderTransaction(t, container, pp)
		require.NoError(t, err)
		require.Equal(t, 4, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   18.00,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   50.00,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("`CASH ADVANCEMENT E-PAYMENT NON-RMS` payment method with ontop", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-30"

		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_cash_advancement_epayment")
		require.NoError(t, err)

		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashAdvancementEpayment,
			ontop: &prepareOntop{
				id:               "fb8ca76b29cb4a61b550c65b4beb637b",
				name:             "AA7 - Basket Size พื้นที่เกาะอยุธยา",
				amount:           10.0,
				incentiveName:    "inc",
				incentiveSources: []string{"inc_s1", "inc_s2"},
			},
		}

		trx, err := completeOrderTransaction(t, container, pp)
		require.NoError(t, err)
		require.Equal(t, 6, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.ItemFeeTransactionType,
				Amount:   18.00,
			},
			{
				Category: model.WalletTransactionCategory,
				Type:     model.CouponTransactionType,
				Amount:   50.00,
			},
			{
				Category:         model.WalletTransactionCategory,
				Type:             model.OnTopTransactionType,
				Amount:           10.0,
				IncentiveNames:   []string{"AA7 - Basket Size พื้นที่เกาะอยุธยา"},
				IncentiveSources: []string{"inc_s1", "inc_s2"},
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   0.3,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})

	t.Run("complete order should pay coin", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-1132"

		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_ontop_coin")
		require.NoError(t, err)

		ots := []prepareOntop{
			{
				id:     "1",
				name:   "scheme 1",
				amount: 31.0,
			},
			{
				id:     "2",
				name:   "scheme 2",
				amount: 10.0,
				coin:   7,
			},
			{
				id:     "3",
				name:   "scheme 3",
				amount: 1.0,
				coin:   2,
			},
		}

		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCreditCard,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     model.FlowCashAdvancementEpayment,
			ontops:              ots,
		}

		_, err = completeOrderTransaction(t, container, pp)
		require.NoError(t, err)

		var rb model.RewardBalance
		q := bson.M{"driver_id": driverID}
		ctx := context.Background()
		err = container.RewardBalanceDataStore.FindOne(ctx, q, &rb)
		require.NoError(t, err)
		assert.Equal(t, 9, rb.CoinAmount)

		var rt model.RewardTransaction
		q = bson.M{"driver_id": driverID, "type": model.OnTopRewardTransactionType}
		err = container.MongoRewardTransactionDataStore.FindOne(ctx, q, &rt)
		require.NoError(t, err)
		assert.Equal(t, model.ServiceFood, rt.Info.ServiceType)
		assert.Equal(t, "trip-id-test", rt.Info.TripID)
		assert.Equal(t, []string{orderID}, rt.Info.OrderIDs)
		assert.Equal(t, 9, rt.Amount)
		assert.Equal(t, 2, len(rt.Sources))
		assert.NotEmpty(t, rt.TransactionID)

		var txn model.Transaction
		q = bson.M{"info.driver_id": driverID, "info.type": model.OnTopTransactionType}
		err = container.TransactionDataStore.FindOne(ctx, q, &txn)
		require.NoError(t, err)
		assert.Equal(t, 3, len(txn.Sources))
		assert.Equal(t, types.Money(31), txn.Sources[0].Amount)
		assert.Equal(t, types.Money(10), txn.Sources[1].Amount)
		assert.Equal(t, types.Money(1), txn.Sources[2].Amount)
		assert.Equal(t, types.Money(42), txn.Info.Amount)
	})

	t.Run("`E-PAYMENT` payment method - TRUE Money", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-12"

		container := ittest.NewContainer(t)
		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     40,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodTRUEMoney,
			commission:          0,
			withholdingTax:      0.03,
			driverMoneyFlow:     "",
		}

		trx, err := completeOrderTransaction(t, container, pp)

		require.NoError(t, err)
		require.Equal(t, 2, len(trx))

		tcTrxs := []validTransaction{
			{
				Category: model.WalletTransactionCategory,
				Type:     model.DriverWageType,
				Amount:   40.00,
			},
			{
				Category: model.CreditTransactionCategory,
				Type:     model.WithholdingTransactionType,
				Amount:   1.20,
			},
		}

		require.ElementsMatch(t, tcTrxs, mapActualTransaction(trx))
	})
}

func TestFoodProvider_AcceptOrderBackToBack(t *testing.T) {
	t.Parallel()

	t.Run("queue order with compatible status(with after pickup status)", func(t *testing.T) {
		ctx := context.Background()
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_back_to_back")
		if err != nil {
			t.Errorf("[TestFoodProvider_AcceptOrderBackToBack] Unexpected error occurred at InitFixture: %v", err.Error())
		}

		driverID := "B2B-001"
		orderID := "LMF-B2B002"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID:        driverID,
				AssignedOrders: []prediction.AssignedOrder{{OrderID: orderID}},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-B2B001",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.DropOffAction,
					},
				},
			},
		}
		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)

		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")
		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		assert.Equal(t, []string{orderID}, driver.QueueingOrders)
		t.Run("distance to second order is calculated from first order's dropoff", func(t *testing.T) {
			order := getOrder(t, ftd.ctn, orderID)
			assert.InDelta(t, 700.0, float64(order.Routes[0].Distance/types.KM), 100.0) // about 700 km from lat 7 -> lat 14
		})
		o, err := container.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)
	})

	t.Run("queue order as last back to back order (max loads = 3)", func(t *testing.T) {
		ctx := context.Background()
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_back_to_back")
		if err != nil {
			t.Errorf("[TestFoodProvider_AcceptOrderBackToBack] Unexpected error occurred at InitFixture: %v", err.Error())
		}

		driverID := "B2B-001"
		orderID := "LMF-B2B002"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID:        driverID,
				AssignedOrders: []prediction.AssignedOrder{{OrderID: orderID}},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-B2B001",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.DropOffAction,
					},
				},
			},
		}

		driv, err := container.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)

		driv.QueueingOrders = []string{"LMF-B2B003"}
		driv.QueueingTrips = []string{"TRIP-B2B003"}

		err = container.DriverRepository.Update(ctx, driv)
		require.NoError(t, err)

		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)

		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")
		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		assert.Equal(t, []string{"LMF-B2B003", orderID}, driver.QueueingOrders)

		updatedOrder, err := container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)

		assert.Equal(t, []string{"TRIP-B2B003", updatedOrder.TripID}, driver.QueueingTrips)
		o, err := container.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)
	})

	t.Run("bundle back to back order (max loads = 3)", func(t *testing.T) {
		ctx := context.Background()
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_back_to_back")
		if err != nil {
			t.Errorf("[TestFoodProvider_AcceptOrderBackToBack] Unexpected error occurred at InitFixture: %v", err.Error())
		}

		driverID := "B2B-001"
		orderID := "LMF-B2B002"

		order3Before, err := container.MongoOrderRepository.Get(ctx, "LMF-B2B003")
		require.NoError(t, err)
		order2Before, err := container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)
		expectDistance, _, err := container.StubMapService.FindFastestRoute(ctx, mapservice.Location(order2Before.Routes[0].Location), mapservice.Location(order3Before.Routes[0].Location), false)
		require.NoError(t, err)
		require.NotEqual(t, expectDistance.Distance, order3Before.Routes[0].Distance.Float64(), "distance0 of order3 can't be equal to the final expect value, to test if the value has really been updated")

		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID:        driverID,
				AssignedOrders: []prediction.AssignedOrder{{OrderID: orderID}},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-B2B001",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.DropOffAction,
					},
				},
			},
		}

		driv, err := container.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)

		driv.QueueingOrders = []string{"LMF-B2B003"}
		driv.QueueingTrips = []string{"TRIP-B2B003"}

		err = container.DriverRepository.Update(ctx, driv)
		require.NoError(t, err)

		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)

		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")
		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		assert.Equal(t, []string{"LMF-B2B003", orderID}, driver.QueueingOrders)
		assert.Equal(t, []string{"TRIP-B2B003"}, driver.QueueingTrips)

		updatedTrip, err := container.TripRepository.GetTripByTripID(ctx, "TRIP-B2B003")
		require.NoError(t, err)
		require.Len(t, updatedTrip.Routes, 4)
		require.Equal(t, model.TripActionPickUp, updatedTrip.Routes[0].Action)
		require.Equal(t, model.TripActionPickUp, updatedTrip.Routes[1].Action)
		require.Equal(t, model.TripActionDropOff, updatedTrip.Routes[2].Action)
		require.Equal(t, model.TripActionDropOff, updatedTrip.Routes[3].Action)

		order3After, err := container.MongoOrderRepository.Get(ctx, "LMF-B2B003")
		require.NoError(t, err)
		assert.Equal(t, expectDistance.Distance, order3After.Routes[0].Distance.Float64())
		o, err := container.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)
	})

	t.Run("queue order as first back to back order (max loads = 3)", func(t *testing.T) {
		ctx := context.Background()
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_back_to_back")
		if err != nil {
			t.Errorf("[TestFoodProvider_AcceptOrderBackToBack] Unexpected error occurred at InitFixture: %v", err.Error())
		}

		driverID := "B2B-001"
		orderID := "LMF-B2B002"

		order3Before, err := container.MongoOrderRepository.Get(ctx, "LMF-B2B003")
		require.NoError(t, err)
		trip3Before, err := container.TripRepository.GetTripByTripID(ctx, "TRIP-B2B003")
		require.NoError(t, err)
		order2Before, err := container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)
		expectDistance, _, err := container.StubMapService.FindFastestRoute(ctx, mapservice.Location(order2Before.Routes[1].Location), mapservice.Location(trip3Before.Routes[0].Location), false)
		require.NoError(t, err)
		require.NotEqual(t, expectDistance.Distance, order3Before.Routes[0].Distance.Float64(), "distance0 of order3 can't be equal to the final expect value, to test if the value has really been updated")
		require.NotEqual(t, expectDistance.Distance, trip3Before.Routes[0].Distance.Float64(), "distance0 of trip3 can't be equal to the final expect value, to test if the value has really been updated")

		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID:        driverID,
				AssignedOrders: []prediction.AssignedOrder{{OrderID: orderID}},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-B2B001",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B002",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.DropOffAction,
					},
				},
			},
		}

		driv, err := container.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)

		driv.QueueingOrders = []string{"LMF-B2B003"}
		driv.QueueingTrips = []string{"TRIP-B2B003"}

		err = container.DriverRepository.Update(ctx, driv)
		require.NoError(t, err)

		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)

		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")
		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		assert.Equal(t, []string{orderID, "LMF-B2B003"}, driver.QueueingOrders)

		updatedOrder, err := container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)

		assert.Equal(t, []string{updatedOrder.TripID, "TRIP-B2B003"}, driver.QueueingTrips)

		order3After, err := container.MongoOrderRepository.Get(ctx, "LMF-B2B003")
		require.NoError(t, err)
		trip3After, err := container.TripRepository.GetTripByTripID(ctx, "TRIP-B2B003")
		require.NoError(t, err)
		assert.Equal(t, expectDistance.Distance, order3After.Routes[0].Distance.Float64())
		assert.Equal(t, expectDistance.Distance, trip3After.Routes[0].Distance.Float64())
		o, err := container.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)
	})

	t.Run("queue order with compatible status(with config status)", func(t *testing.T) {
		ctx := context.Background()
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_back_to_back")
		if err != nil {
			t.Errorf("[TestFoodProvider_AcceptOrderBackToBack] Unexpected error occurred at InitFixture: %v", err.Error())
		}

		driverID := "B2B-002"
		orderID := "LMF-B2B004"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID:        driverID,
				AssignedOrders: []prediction.AssignedOrder{{OrderID: orderID}},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B003",
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    "LMF-B2B004",
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    "LMF-B2B004",
						ActionType: prediction.DropOffAction,
					},
				},
			},
		}
		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)

		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")
		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		assert.Equal(t, []string{orderID}, driver.QueueingOrders)
		o, err := container.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)
	})
}

func TestFoodProvider_AcceptOrderMO(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)
	err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_back_to_back")
	if err != nil {
		t.Errorf("[TestFoodProvider_AcceptOrderMO] Unexpected error occurred at InitFixture: %v", err.Error())
	}

	t.Run("bundle mo order to current trip", func(t *testing.T) {
		ctx := context.Background()
		driverID := "DRIV-MO-001"
		orderID := "LMF-MO02"
		tripID := "LMRT-01"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID: driverID,
				AssignedOrders: []prediction.AssignedOrder{
					{OrderID: "LMF-MO01"},
					{OrderID: "LMF-MO02"},
				},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-MO01",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO02",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO01",
						ActionType: "dropoff",
					},
					{
						OrderID:    "LMF-MO02",
						ActionType: "dropoff",
					},
				},
			},
		}
		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)
		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")

		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		require.Empty(t, driver.QueueingOrders)
		require.Equal(t, tripID, driver.CurrentTrip)

		trip, err := ftd.ctn.TripRepository.GetTripByTripID(ctx, tripID)
		require.NoError(t, err)
		require.ElementsMatch(t, []string{"LMF-MO01", "LMF-MO02"}, []string{trip.Orders[0].OrderID, trip.Orders[1].OrderID})
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO01", StopID: 0}, {OrderID: "LMF-MO02", StopID: 0}}, trip.Routes[0].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO01", StopID: 1}}, trip.Routes[1].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO02", StopID: 1}}, trip.Routes[2].StopOrders)
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)
	})

	t.Run("bundle mo reassigned order to current trip and auto transition restaurant accept order", func(t *testing.T) {
		ctx := context.Background()
		driverID := "DRIV-MO-010"
		orderID := "LMF-MO-REASSIGNED-01"
		tripID := "LMRT-04"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID: driverID,
				AssignedOrders: []prediction.AssignedOrder{
					{OrderID: "LMF-MO05"},
					{OrderID: "LMF-MO04"},
					{OrderID: "LMF-MO-REASSIGNED-01"},
				},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-MO04",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO-REASSIGNED-01",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO05",
						ActionType: "dropoff",
					},
					{
						OrderID:    "LMF-MO04",
						ActionType: "dropoff",
					},
					{
						OrderID:    "LMF-MO-REASSIGNED-01",
						ActionType: "dropoff",
					},
				},
			},
		}
		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)
		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")

		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		require.Empty(t, driver.QueueingOrders)
		require.Equal(t, tripID, driver.CurrentTrip)

		trip, err := ftd.ctn.TripRepository.GetTripByTripID(ctx, tripID)
		require.NoError(t, err)
		require.Equal(t, model.TripStatusDriveTo, trip.Status)
		require.Equal(t, 1, trip.HeadTo)
		require.Equal(t, model.StatusDriverToDestination, trip.Orders[0].Status)
		require.Equal(t, model.StatusDriverToRestaurant, trip.Orders[1].Status)
		require.Equal(t, model.StatusDriverToRestaurant, trip.Orders[2].Status)
		require.ElementsMatch(t, []string{"LMF-MO05", "LMF-MO04", "LMF-MO-REASSIGNED-01"}, []string{trip.Orders[0].OrderID, trip.Orders[1].OrderID, trip.Orders[2].OrderID})
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO05", StopID: 0, Done: true}}, trip.Routes[0].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO04", StopID: 0}, {OrderID: "LMF-MO-REASSIGNED-01", StopID: 0}}, trip.Routes[1].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO05", StopID: 1}}, trip.Routes[2].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO04", StopID: 1}}, trip.Routes[3].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO-REASSIGNED-01", StopID: 1}}, trip.Routes[4].StopOrders)
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), trip.Orders[1].OrderID)
		require.NoError(t, err)
		assert.NotNil(t, o.LeavePrevStopAt)
		o, err = ftd.ctn.MongoOrderRepository.Get(context.TODO(), trip.Orders[2].OrderID)
		require.NoError(t, err)
		assert.NotNil(t, o.LeavePrevStopAt)
	})

	t.Run("bundle switch flow mo order to current trip and auto transition status", func(t *testing.T) {
		ctx := context.Background()
		driverID := "DRIV-MO-002"
		orderID := "LMF-MO-SWITCH-FLOW-01"
		tripID := "LMRT-02"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID: driverID,
				AssignedOrders: []prediction.AssignedOrder{
					{OrderID: "LMF-MO03"},
					{OrderID: "LMF-MO-SWITCH-FLOW-01"},
				},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-MO03",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO-SWITCH-FLOW-01",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO03",
						ActionType: "dropoff",
					},
					{
						OrderID:    "LMF-MO-SWITCH-FLOW-01",
						ActionType: "dropoff",
					},
				},
			},
		}
		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)
		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")

		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		require.Empty(t, driver.QueueingOrders)
		require.Equal(t, tripID, driver.CurrentTrip)

		trip, err := ftd.ctn.TripRepository.GetTripByTripID(ctx, tripID)
		require.NoError(t, err)
		require.ElementsMatch(t, []string{"LMF-MO03", "LMF-MO-SWITCH-FLOW-01"}, []string{trip.Orders[0].OrderID, trip.Orders[1].OrderID})
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO03", StopID: 0}, {OrderID: "LMF-MO-SWITCH-FLOW-01", StopID: 0}}, trip.Routes[0].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO03", StopID: 1}}, trip.Routes[1].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO-SWITCH-FLOW-01", StopID: 1}}, trip.Routes[2].StopOrders)

		var ord model.Order
		err = container.OrderDataStore.FindOne(context.Background(), bson.M{"order_id": orderID}, &ord)
		require.NoError(t, err)
		require.Equal(t, driverID, ord.Driver)
		require.Equal(t, tripID, ord.TripID)
		require.Equal(t, model.StatusDriverArrivedRestaurant, ord.Status)
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.NotNil(t, o.LeavePrevStopAt)
	})

	t.Run("bundle switch flow mo_s3 order to current trip shouldn't autotransition", func(t *testing.T) {
		ctx := context.Background()
		driverID := "DRIV-MO-003"
		orderID := "LMF-MO-S3-SWITCH-FLOW-01"
		tripID := "LMRT-03"
		planRoute := model.PlanRoute{
			RouteResponse: prediction.RouteResponse{
				RiderID: driverID,
				AssignedOrders: []prediction.AssignedOrder{
					{OrderID: "LMF-MO03"},
					{OrderID: "LMF-MO-S3-SWITCH-FLOW-01"},
				},
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    "LMF-MO03",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO-S3-SWITCH-FLOW-01",
						ActionType: "pickup",
					},
					{
						OrderID:    "LMF-MO03",
						ActionType: "dropoff",
					},
					{
						OrderID:    "LMF-MO-S3-SWITCH-FLOW-01",
						ActionType: "dropoff",
					},
				},
			},
		}
		ftd := newFoodQueueTestData(t, container, driverID, orderID)
		ftd.autoAssignQueueOrderToDriver(1, orderID, driverID, planRoute)
		testAcceptOrderSuccessfully(t, ftd, orderID, driverID, "{}")

		driver, err := ftd.ctn.DriverRepository.FindDriverID(ctx, driverID)
		require.NoError(t, err)
		require.Empty(t, driver.QueueingOrders)
		require.Equal(t, tripID, driver.CurrentTrip)

		trip, err := ftd.ctn.TripRepository.GetTripByTripID(ctx, tripID)
		require.NoError(t, err)
		require.ElementsMatch(t, []string{"LMF-MO03", "LMF-MO-S3-SWITCH-FLOW-01"}, []string{trip.Orders[0].OrderID, trip.Orders[1].OrderID})
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO03", StopID: 0}}, trip.Routes[0].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO-S3-SWITCH-FLOW-01", StopID: 0}}, trip.Routes[1].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO03", StopID: 1}}, trip.Routes[2].StopOrders)
		require.ElementsMatch(t, []model.TripStopOrder{{OrderID: "LMF-MO-S3-SWITCH-FLOW-01", StopID: 1}}, trip.Routes[3].StopOrders)

		var ord model.Order
		err = container.OrderDataStore.FindOne(context.Background(), bson.M{"order_id": orderID}, &ord)
		require.NoError(t, err)
		require.Equal(t, driverID, ord.Driver)
		require.Equal(t, tripID, ord.TripID)
		require.Equal(t, model.StatusDriverToRestaurant, ord.Status)
		o, err := ftd.ctn.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)
	})
}

type prepareTransaction struct {
	deliveryFeeSubtotal float64
	userDeliveryFee     float64
	driverID            string
	orderID             string
	paymentMethod       model.PaymentMethod
	commission          float64
	withholdingTax      float64
	driverMoneyFlow     model.DriverMoneyFlow
	itemFee             *model.ItemFeeSummary
	driverBalance       *prepareDriverBalance
	ontop               *prepareOntop
	ontops              []prepareOntop
	sub                 float64
	coupon              float64
}

type prepareDriverBalance struct {
	driverCredit float64
	driverWallet float64
}

type prepareOntop struct {
	id               string
	name             string
	amount           float64
	coin             int
	incentiveName    string
	incentiveSources []string
}

type validTransaction struct {
	Category         model.TransactionCategory
	Type             model.TransactionType
	Amount           float64
	IncentiveNames   []string
	IncentiveSources []string
}

func mapActualTransaction(trx []model.Transaction) []validTransaction {
	actualTrxs := make([]validTransaction, 0)
	for _, trx := range trx {
		actualTrxs = append(actualTrxs, validTransaction{
			Category:         trx.Info.Category,
			Type:             trx.Info.Type,
			Amount:           trx.Info.Amount.Float64(),
			IncentiveNames:   trx.Info.IncentiveNames,
			IncentiveSources: trx.Info.IncentiveSources,
		})
	}
	return actualTrxs
}

func mockSingleTrip(tripID string, order model.Order) (model.Trip, error) {
	if len(order.Routes) < 2 {
		return model.Trip{}, fmt.Errorf("order should have at least 2 routes")
	}

	now := timeutil.BangkokNow()
	routes := make([]model.TripRoute, len(order.Routes))
	action := model.TripActionPickUp
	for i, s := range order.Routes {
		routes[i] = model.TripRoute{
			ID:                    s.ID,
			Action:                action,
			Location:              s.Location,
			Distance:              s.Distance,
			EstimatedDeliveryTime: s.EstimatedDeliveryTime,
			StopOrders: []model.TripStopOrder{
				{
					OrderID: order.OrderID,
					StopID:  i,
					Done:    false,
				},
			},
			CreatedAt: now,
			UpdatedAt: now,
		}
		action = model.TripActionDropOff
	}

	t := model.Trip{
		TripID:   tripID,
		DriverID: order.Driver,
		Routes:   routes,
		Orders: []model.TripOrder{
			{
				OrderID:         order.OrderID,
				ServiceType:     order.ServiceType,
				DeliveringRound: order.DeliveringRound,
				Status:          order.Status,
			},
		},
		HeadTo:                0,
		SchemaVersion:         model.CurrentSchemaVersion,
		RevenuePrincipalModel: order.RevenuePrincipalModel,
		CreatedFromB2BOrder:   order.IsB2B,
		CreatedAt:             now,
		UpdatedAt:             now,
	}

	deliveryFee := order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee
	baseFee := types.Money(deliveryFee.SubTotal)
	t.DriverWageSummary.BaseWage = baseFee
	t.DriverWageSummary.DistanceWage = types.NewMoney(deliveryFee.RawBaseFee).Add(types.NewMoney(deliveryFee.RoadFee)).Sub(baseFee)
	t.DriverWageSummary.ShiftDeduction = types.NewMoney(deliveryFee.ShiftPriceValue)
	// This field support only messenger round trip order only
	t.DriverWageSummary.ExtraCharge = types.NewMoney(deliveryFee.ExtraCharges.Total())
	t.DriverWageSummary.Commission = types.NewMoney(deliveryFee.Commission)
	t.DriverWageSummary.WithHoldingTax = types.NewMoney(deliveryFee.WithholdingTax)
	t.DriverWageSummary.TotalDriverWage = t.DriverWageSummary.BaseWage.Add(t.DriverWageSummary.DistanceWage).Add(t.DriverWageSummary.ExtraCharge).Sub(t.DriverWageSummary.ShiftDeduction)

	t.UpdateHistory(order.OrderID, order.Status, t.Status)
	return t, nil
}

func completeOrderTransaction(t *testing.T, container *ittest.IntegrationTestContainer, pp prepareTransaction) ([]model.Transaction, error) {
	ord, err := container.MongoOrderRepository.Get(context.Background(), pp.orderID)
	require.NoError(t, err)
	ord.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = pp.paymentMethod
	ord.Routes[1].PriceSummary.DeliveryFee.SubTotal = pp.deliveryFeeSubtotal
	ord.Routes[1].PriceSummary.DeliveryFee.RawBaseFee = pp.deliveryFeeSubtotal
	ord.Routes[1].PriceSummary.DeliveryFee.UserDeliveryFee = pp.userDeliveryFee

	ord.Driver = pp.driverID
	ord.Options.DriverMoneyFlow = pp.driverMoneyFlow
	ord.RevenuePrincipalModel = true

	if pp.itemFee != nil {
		ord.Routes[1].PriceSummary.ItemFee = model.ItemFeeSummary{
			ItemFee:  pp.itemFee.ItemFee,
			SubTotal: pp.itemFee.SubTotal,
			Total:    pp.itemFee.Total,
		}
	}

	if pp.coupon > 0 {
		ord.Routes[1].PriceSummary.ItemFee.Discounts = model.DiscountList{model.Discount{Type: model.DiscountTypeCouponAdvance, Category: "", Code: "", Discount: pp.coupon}}
	}

	if pp.ontop != nil {
		ord.Routes[1].PriceSummary.DeliveryFee.OnTopScheme = []model.OnTopScheme{
			{
				ID:               pp.ontop.id,
				Name:             pp.ontop.name,
				Amount:           pp.ontop.amount,
				Coin:             pp.ontop.coin,
				IncentiveSources: pp.ontop.incentiveSources,
				IncentiveName:    pp.ontop.incentiveName,
			},
		}
	} else if pp.ontops != nil {
		schemes := []model.OnTopScheme{}
		for _, ot := range pp.ontops {
			schemes = append(schemes, model.OnTopScheme{
				ID:               ot.id,
				Name:             ot.name,
				Amount:           ot.amount,
				Coin:             ot.coin,
				IncentiveSources: ot.incentiveSources,
				IncentiveName:    ot.incentiveName,
			})
		}
		ord.Routes[1].PriceSummary.DeliveryFee.OnTopScheme = schemes
	}

	ord.SetOnTopFare()
	ord.SetCommission(pp.commission, 0.03)
	ord.SetWithholdingTax(pp.withholdingTax)

	tripID := "trip-id-test"
	ord.TripID = tripID
	trip, err := mockSingleTrip(tripID, *ord)
	require.NoError(t, err)
	trip.Status = model.TripStatusArrivedAt
	trip.HeadTo = 1

	err = container.TripRepository.Create(context.Background(), trip)
	require.NoError(t, err)

	if pp.driverBalance != nil {
		dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), pp.driverID)
		require.NoError(t, err)
		dt.PurchaseCreditBalance = types.Money(pp.driverBalance.driverCredit)
		dt.WalletBalance = types.Money(pp.driverBalance.driverWallet)
		err = container.DataStoreDriverTransactionRepository.Update(context.Background(), dt)
		require.NoError(t, err)
	}

	err = container.MongoOrderRepository.UpdateOrder(context.Background(), ord)
	require.NoError(t, err)

	callNextStatus(container, t, pp.driverID, pp.orderID, http.StatusOK, model.StatusCompleted)

	trx := make([]model.Transaction, 0, 100)
	err = container.TransactionDataStore.Find(context.Background(), bson.M{
		"$or": []bson.M{
			{"info.trip_id": tripID},
			{"info.order_id": pp.orderID},
		},
	}, 0, 0, &trx)
	return trx, err
}

func completeOrderTransactionInContingencyMode(t *testing.T, container *ittest.IntegrationTestContainer, pp prepareTransaction, cfg order.ContingencyConfig) ([]model.Transaction, error) {
	container.OrderAPI.ContingencyCfg = order.NewAtomicContingencyConfig(cfg)
	return completeOrderTransaction(t, container, pp)
}

func TestFoodProvider_UpdateOrderStatus(t *testing.T) {
	t.Run("can update status to complete with switch flow", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-22"

		container := ittest.NewContainer(t)
		prepareSwitchFlowOrder(container, t, driverID, orderID)

		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverArrivedRestaurant)
		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverToDestination)
		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverArrived)
		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusCompleted)
		callNextStatus(container, t, driverID, orderID, http.StatusBadRequest, model.StatusCompleted)
		t.Run("complete order also insert revision", func(t *testing.T) {
			ctx := context.Background()
			order, err := container.MongoOrderRepository.Get(ctx, orderID)
			require.NoError(t, err)
			revision, err := container.MongoOrderRepository.FindRevision(ctx, repository.NewOrderRevisionQuery().WithOrderIDs([]string{orderID}), 0, 0)
			require.NoError(t, err)
			require.Equal(t, *order, revision[0])
		})
	})

	t.Run("update leave_last_stop_at when update to DRIVER_TO_RESTAURANT", func(t *testing.T) {
		// Given

		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-22"
		tripID := "TRIP-1"

		container := ittest.NewContainer(t)
		o := prepareNonSwitchFlowOrder(container, t, driverID, orderID)
		createTripFromOrder(container, t, o, tripID)
		o, err := container.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.Nil(t, o.LeavePrevStopAt)

		enableTrip := toggle.EnableTripOnGinContext
		// When
		gctx := callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverToRestaurant, enableTrip)

		// Then
		require.JSONEq(t, `{}`, gctx.ResponseRecorder.Body.String())
		var trip model.Trip
		err = container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
		require.NoError(t, err)
		require.Equal(t, model.TripStatusDriveTo, trip.Status)
		require.Equal(t, model.StatusDriverToRestaurant, trip.Orders[0].Status)
		o, err = container.MongoOrderRepository.Get(context.TODO(), orderID)
		require.NoError(t, err)
		assert.NotNil(t, o.LeavePrevStopAt)
	})
	t.Run("can update status to complete with switch flow (ENABLED TRIP)", func(t *testing.T) {
		// Given

		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-22"
		tripID := "TRIP-1"

		container := ittest.NewContainer(t)
		o := prepareSwitchFlowOrder(container, t, driverID, orderID)
		createTripFromOrder(container, t, o, tripID)

		enableTrip := toggle.EnableTripOnGinContext
		t.Run("when order status from DRIVER_TO_RESTAURANT -> DRIVER_ARRIVED_RESTAURANT", func(t *testing.T) {
			// When
			gctx := callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverArrivedRestaurant, enableTrip)

			// Then
			require.JSONEq(t, `{}`, gctx.ResponseRecorder.Body.String())
			var trip model.Trip
			err := container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
			require.NoError(t, err)
			require.Equal(t, model.TripStatusArrivedAt, trip.Status)
			require.Equal(t, 0, trip.HeadTo)
			require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[0].Status)
			require.Equal(t, false, trip.Routes[0].StopOrders[0].Done)
			require.Equal(t, false, trip.Routes[1].StopOrders[0].Done)
		})

		t.Run("when order status from DRIVER_ARRIVED_RESTAURANT -> DRIVER_TO_DESTINATION", func(t *testing.T) {
			// When
			gctx := callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverToDestination, enableTrip)

			// Then
			require.JSONEq(t, `{}`, gctx.ResponseRecorder.Body.String())
			var trip model.Trip
			err := container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
			require.NoError(t, err)
			require.Equal(t, model.TripStatusDriveTo, trip.Status)
			require.Equal(t, 1, trip.HeadTo)
			require.Equal(t, model.StatusDriverToDestination, trip.Orders[0].Status)
			require.Equal(t, true, trip.Routes[0].StopOrders[0].Done)
			require.Equal(t, false, trip.Routes[1].StopOrders[0].Done)
		})

		t.Run("when order status from DRIVER_TO_DESTINATION -> ARRIVED", func(t *testing.T) {
			// When
			gctx := callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverArrived, enableTrip)

			// Then
			require.JSONEq(t, `{}`, gctx.ResponseRecorder.Body.String())
			var trip model.Trip
			err := container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
			require.NoError(t, err)
			require.Equal(t, model.TripStatusArrivedAt, trip.Status)
			require.Equal(t, 1, trip.HeadTo)
			require.Equal(t, model.StatusDriverArrived, trip.Orders[0].Status)
			require.Equal(t, true, trip.Routes[0].StopOrders[0].Done)
			require.Equal(t, false, trip.Routes[1].StopOrders[0].Done)
		})

		t.Run("when order status from ARRIVED -> DROP_OFF_DONE", func(t *testing.T) {
			// When
			_ = callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusCompleted, enableTrip)

			// Then
			var trip model.Trip
			err := container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
			require.NoError(t, err)
			require.Equal(t, model.TripStatusCompleted, trip.Status)
			require.Equal(t, 1, trip.HeadTo)
			require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
			require.Equal(t, true, trip.Routes[0].StopOrders[0].Done)
			require.Equal(t, true, trip.Routes[1].StopOrders[0].Done)
		})

		t.Run("when order status from COMPLETED -> COMPLETED (Duplicate)", func(t *testing.T) {
			timeutils.Now = func() time.Time {
				return time.Date(2022, 0o6, 21, 21, 0, 0, 0, timeutils.BangkokLocation())
			}
			defer timeutils.Unfreeze()

			// When
			gctx := callNextStatus(container, t, driverID, orderID, http.StatusBadRequest, model.StatusCompleted, enableTrip)

			// Then
			require.JSONEq(t, `{"code":"ORDER_COMPLETED","message":"order got completed","timestamp":1655820000000000000}`, gctx.ResponseRecorder.Body.String())
			var trip model.Trip
			err := container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
			require.NoError(t, err)
			require.Equal(t, model.TripStatusCompleted, trip.Status)
			require.Equal(t, 1, trip.HeadTo)
			require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
			require.Equal(t, true, trip.Routes[0].StopOrders[0].Done)
			require.Equal(t, true, trip.Routes[1].StopOrders[0].Done)
		})

		t.Run("complete order also insert revision", func(t *testing.T) {
			ctx := context.Background()
			order, err := container.MongoOrderRepository.Get(ctx, orderID)
			require.NoError(t, err)
			revision, err := container.MongoOrderRepository.FindRevision(ctx, repository.NewOrderRevisionQuery().WithOrderIDs([]string{orderID}), 0, 0)
			require.NoError(t, err)
			require.Equal(t, *order, revision[0])
		})
	})

	t.Run("can update status to complete with reassign", func(t *testing.T) {
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-REASSIGNED"

		container := ittest.NewContainer(t)

		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverArrivedRestaurant)
		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverToDestination)
		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusDriverArrived)
		callNextStatus(container, t, driverID, orderID, http.StatusOK, model.StatusCompleted)
	})

	t.Run("can leave stop even delivering photo is required but VOS config was disabled", func(tt *testing.T) {
		tt.Parallel()
		driverID := "ASSIGNED"
		orderID := "LMF-5"

		container := ittest.NewContainer(tt)

		// make sure without VOS disabled config, the order cannot exit the stop
		gctx := callNextStatus(container, tt, driverID, orderID, http.StatusBadRequest, model.StatusDriverArrived)
		var res struct {
			Code string `json:"code"`
		}
		gctx.DecodeJSONResponse(&res)
		assert.Equal(tt, "DELIVERING_PHOTO_IS_REQUIRED", res.Code)

		// delivery photo was ignored when disable VOS config
		container.Locator.ProviderDeps.Cfg.AtomicOrderDBConfig.Config.DisableVOSPhoto = true
		callNextStatus(container, tt, driverID, orderID, http.StatusOK, model.StatusCompleted)

		order, err := container.MongoOrderRepository.Get(context.Background(), orderID)
		assert.NoError(t, err)
		assert.NotContains(tt, model.PauseDeliveringPhoto, order.Routes[1].Pauses)
	})
}

func TestFoodProvider_AdminCompleteOrder(t *testing.T) {
	checkOrderZeroFieldForFullTimeDriver := func(t *testing.T, order model.Order) {
		deliveryFee := order.Routes[order.PayAtStop].PriceSummary.DeliveryFee
		require.Empty(t, deliveryFee.CustomOnTops)
		require.Empty(t, deliveryFee.OnTopScheme)
		require.Equal(t, 0.0, deliveryFee.RawBaseFee)
		require.Equal(t, 0.0, deliveryFee.BaseFee)
		require.Equal(t, 0.0, deliveryFee.RoadFee)
		require.Equal(t, 0.0, deliveryFee.CustomOnTop)
		require.Equal(t, 0.0, deliveryFee.CommissionRate)
		require.Equal(t, 0.0, deliveryFee.StartingFee)
		require.Equal(t, 0.0, deliveryFee.Commission)
		require.Equal(t, 0.0, deliveryFee.WithholdingTax)
		require.Equal(t, 0.0, deliveryFee.OnTopFare)
		require.Equal(t, 0.0, deliveryFee.RawOnTopFare)
		require.Equal(t, 0.0, deliveryFee.RawBundleOnTopFare)
		require.Equal(t, 0, deliveryFee.Coin)
		require.Equal(t, 0, deliveryFee.RawCoin)
		require.Equal(t, 0, deliveryFee.RawBundleCoin)
		require.Equal(t, 0.0, deliveryFee.OnTopCommissionFare)
		require.Equal(t, 0.0, deliveryFee.OnTopWithholdingTax)
		require.Equal(t, 0.0, deliveryFee.DistanceUnitFee)
	}

	checkTripZeroFieldForFullTimeDriver := func(t *testing.T, trip model.Trip) {
		driverWageSummary := trip.DriverWageSummary
		require.Equal(t, types.Money(0), driverWageSummary.BaseWage)
		require.Equal(t, types.Money(0), driverWageSummary.DistanceWage)
		require.Equal(t, types.Money(0), driverWageSummary.ExtraCharge)
		require.Equal(t, types.Money(0), driverWageSummary.ShiftDeduction)
		require.Equal(t, types.Money(0), driverWageSummary.TotalDriverWage)
		require.Equal(t, types.Money(0), driverWageSummary.Commission)
		require.Equal(t, types.Money(0), driverWageSummary.AdditionServiceCommission)
		require.Equal(t, types.Money(0), driverWageSummary.WithHoldingTax)
		require.Equal(t, types.Money(0), driverWageSummary.TransferAmount)
		require.Equal(t, types.Money(0), driverWageSummary.Outstanding)
	}

	t.Run("admin can force complete order", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)

		orderId := "LMF-9"

		gctx := createAdminCompleteOrderRequest(orderId, false, "test admin complete order")
		gctx.Send(container.GinEngineRouter)
		gctx.AssertResponseCode(tt, http.StatusOK)

		order, err := container.MongoOrderRepository.Get(gctx.GinCtx().Request.Context(), orderId)
		require.NoError(tt, err)
		require.Equal(tt, model.StatusCompleted, order.Status)
		require.Len(tt, order.Remarks, 2)
		require.Equal(tt, "จบ Order นี้ จากระบบ Rider Admin โดย <EMAIL>", order.Remarks[0])
		require.Equal(tt, "[จบงานโดย Rider Admin] test admin complete order", order.Remarks[1])
		tt.Run("also create revision", func(tt *testing.T) {
			revision, err := container.MongoOrderRepository.FindRevision(gctx.GinCtx().Request.Context(), repository.NewOrderRevisionQuery().WithOrderIDs([]string{orderId}), 0, 0)
			require.NoError(tt, err)
			require.Equal(tt, *order, revision[0])
		})
	})

	t.Run("admin can force complete order - ENABLED TRIP", func(tt *testing.T) {
		// Given
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-6"
		tripID := "TRIP-FORCE-COMPLETE-SINGLE-ORDER"
		tt.Parallel()
		container := ittest.NewContainer(tt)
		if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_trips"); err != nil {
			panic(fmt.Sprintf("unexpected error initfixture: %v", err))
		}

		var order model.Order
		err := container.OrderDataStore.FindOne(context.Background(), bson.M{"order_id": orderID}, &order)
		require.NoError(t, err)
		order.TripID = tripID
		order.Status = model.StatusDriverToDestination
		order.Driver = driverID

		err = container.OrderDataStore.Replace(context.Background(), bson.M{"order_id": orderID}, order)
		require.NoError(t, err)

		var driver model.Driver
		err = container.DriversDataStore.FindOne(context.Background(), bson.M{"driver_id": driverID}, &driver)
		require.NoError(t, err)
		driver.CurrentTrip = tripID

		err = container.DriversDataStore.Replace(context.Background(), bson.M{"driver_id": driverID}, driver)
		require.NoError(t, err)

		gctx := createAdminCompleteOrderRequest(orderID, false, "test admin complete order")
		gctx.GinCtx().Request = gctx.GinCtx().Request.WithContext(toggle.EnableCtx(gctx.GinCtx().Request.Context(), toggle.TripEnabled))

		// When
		gctx.Send(container.GinEngineRouter)

		// Then
		gctx.AssertResponseCode(tt, http.StatusOK)
		expectOrder, err := container.MongoOrderRepository.Get(gctx.GinCtx().Request.Context(), orderID)
		require.NoError(tt, err)
		require.Equal(tt, model.StatusCompleted, expectOrder.Status)
		require.Len(tt, expectOrder.Remarks, 2)
		require.Equal(tt, "จบ Order นี้ จากระบบ Rider Admin โดย <EMAIL>", expectOrder.Remarks[0])
		require.Equal(tt, "[จบงานโดย Rider Admin] test admin complete order", expectOrder.Remarks[1])

		var trip model.Trip
		err = container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
		require.NoError(t, err)
		require.Equal(t, model.TripStatusCompleted, trip.Status)
		require.Equal(t, 1, trip.HeadTo)
		require.Equal(t, true, trip.Routes[1].StopOrders[0].Done)
		require.WithinDuration(t, timeutils.Now(), trip.Routes[1].CompletedAt, 5*time.Second)
		require.WithinDuration(t, timeutils.Now(), trip.Routes[1].UpdatedAt, 5*time.Second)
		require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
	})

	t.Run("admin can force complete full time order - ENABLED TRIP", func(tt *testing.T) {
		// Given
		driverID := "DRV_PATTAYA_ONLINE"
		orderID := "LMF-10001"
		tripID := "TRIP-FORCE-COMPLETE-SINGLE-FULL-TIME-ORDER"
		tt.Parallel()
		container := ittest.NewContainer(tt)
		if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_trips"); err != nil {
			panic(fmt.Sprintf("unexpected error initfixture: %v", err))
		}

		var order model.Order
		err := container.OrderDataStore.FindOne(context.Background(), bson.M{"order_id": orderID}, &order)
		require.NoError(t, err)
		order.TripID = tripID
		order.Status = model.StatusDriverToDestination
		order.Driver = driverID

		err = container.OrderDataStore.Replace(context.Background(), bson.M{"order_id": orderID}, order)
		require.NoError(t, err)

		var driver model.Driver
		err = container.DriversDataStore.FindOne(context.Background(), bson.M{"driver_id": driverID}, &driver)
		require.NoError(t, err)
		driver.CurrentTrip = tripID

		err = container.DriversDataStore.Replace(context.Background(), bson.M{"driver_id": driverID}, driver)
		require.NoError(t, err)

		gctx := createAdminCompleteOrderRequest(orderID, false, "test admin complete order")
		gctx.GinCtx().Request = gctx.GinCtx().Request.WithContext(toggle.EnableCtx(gctx.GinCtx().Request.Context(), toggle.TripEnabled))

		// When
		gctx.Send(container.GinEngineRouter)

		// Then
		gctx.AssertResponseCode(tt, http.StatusOK)
		expectOrder, err := container.MongoOrderRepository.Get(gctx.GinCtx().Request.Context(), orderID)
		require.NoError(tt, err)
		require.Equal(tt, model.StatusCompleted, expectOrder.Status)
		require.Len(tt, expectOrder.Remarks, 2)
		require.Equal(tt, "จบ Order นี้ จากระบบ Rider Admin โดย <EMAIL>", expectOrder.Remarks[0])
		require.Equal(tt, "[จบงานโดย Rider Admin] test admin complete order", expectOrder.Remarks[1])

		var trip model.Trip
		err = container.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": tripID}, &trip)
		require.NoError(t, err)
		require.Equal(t, model.TripStatusCompleted, trip.Status)
		require.Equal(t, 1, trip.HeadTo)
		require.Equal(t, true, trip.Routes[1].StopOrders[0].Done)
		require.WithinDuration(t, timeutils.Now(), trip.Routes[1].CompletedAt, 5*time.Second)
		require.WithinDuration(t, timeutils.Now(), trip.Routes[1].UpdatedAt, 5*time.Second)
		require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)

		var ord model.Order
		err = container.OrderDataStore.FindOne(context.Background(), bson.M{"order_id": orderID}, &ord)
		checkOrderZeroFieldForFullTimeDriver(t, ord)
		checkTripZeroFieldForFullTimeDriver(t, trip)
	})
}

func callNextStatus(container *ittest.IntegrationTestContainer, t *testing.T, driverID, orderID string, statusCode int, driverStatus model.Status, gctxOptions ...func(*gin.Context)) *testutil.GinContextWithRecorder {
	gctx := createUpdateStatusRequest(container, driverID, orderID)

	ctx := gctx.GinCtx()
	for _, opt := range gctxOptions {
		opt(ctx)
	}

	gctx.Send(container.GinEngineRouter)
	gctx.AssertResponseCode(t, statusCode)
	orderModel, err := container.MongoOrderRepository.Get(context.Background(), orderID)
	require.NoError(t, err)
	require.Equal(t, driverStatus, orderModel.Status)
	return gctx
}

func prepareNonSwitchFlowOrder(container *ittest.IntegrationTestContainer, t *testing.T, driverID, orderID string) *model.Order {
	q := createSwitchFlowOrderQuote()
	o := createSwitchFlowOrder(orderID, driverID, q)
	o.Options.SwitchFlow = false
	err := container.MongoOrderRepository.CreateOrder(context.Background(), o)
	require.NoError(t, err)

	orderModel, err := container.MongoOrderRepository.Get(context.Background(), orderID)
	require.NoError(t, err)
	orderModel.Status = model.StatusRestaurantAccepted

	err = container.MongoOrderRepository.UpdateOrder(context.Background(), orderModel)
	require.NoError(t, err)

	return orderModel
}

func prepareSwitchFlowOrder(container *ittest.IntegrationTestContainer, t *testing.T, driverID, orderID string) *model.Order {
	q := createSwitchFlowOrderQuote()
	o := createSwitchFlowOrder(orderID, driverID, q)
	err := container.MongoOrderRepository.CreateOrder(context.Background(), o)
	require.NoError(t, err)

	orderModel, err := container.MongoOrderRepository.Get(context.Background(), orderID)
	require.NoError(t, err)
	orderModel.Status = model.StatusDriverToRestaurant

	err = container.MongoOrderRepository.UpdateOrder(context.Background(), orderModel)
	require.NoError(t, err)

	return orderModel
}

func createTripFromOrder(container *ittest.IntegrationTestContainer, t *testing.T, order *model.Order, tripID string) {
	trip, err := model.NewTrip(tripID, *order)
	require.NoError(t, err)

	order.TripID = tripID

	err = container.MongoOrderRepository.UpdateOrder(context.Background(), order)
	require.NoError(t, err)

	trip.DriverWageSummary = model.TripDriverWageSummary{
		BaseWage:        10,
		DistanceWage:    10,
		ShiftDeduction:  10,
		TotalDriverWage: 10,
		Commission:      10,
		WithHoldingTax:  10,
		TransferAmount:  10,
		Outstanding:     10,
	}

	err = container.TripDataStore.Insert(context.Background(), trip)
	require.NoError(t, err)
}

func createSwitchFlowOrderQuote() model.Quote {
	return model.Quote{
		QuoteID:               "LMFQ-22",
		ServiceType:           model.ServiceFood,
		RevenuePrincipalModel: true,
		Routes: []model.Stop{
			{
				ID:      "22214aZ",
				Name:    "โรตีไม่สาย",
				Address: "Ratchada Road.",
				Phones:  []string{"0890059948"},
				Location: model.Location{
					Lat: 14.343028,
					Lng: 100.560946,
				},
				PickingItems: []model.Item{
					{
						Name:     "โรตีไม่สาย",
						Price:    10,
						Quantity: 1,
					},
				},
				CollectPayment: false,
				ItemsPrice:     20,
			},
			{
				ID:      "ChIJlX",
				Name:    "ลุงทอม",
				Address: "Sukumvit Road.",
				Phones:  []string{"0895959595"},
				Location: model.Location{
					Lat: 14.343028,
					Lng: 100.560946,
				},
				DeliveryItems: []model.Item{
					{
						Name:     "โรตีไม่สาย",
						Price:    10,
						Quantity: 1,
					},
				},
				CollectPayment: false,
				ItemsPrice:     0,
				PriceSummary:   model.PriceSummary{},
			},
		},
		Options: model.OrderOptions{
			SwitchFlow: true,
		},
		DistributeRegions: []model.RegionCode{"BKK"},
	}
}

func createSwitchFlowOrder(orderID, driverID string, quote model.Quote) *model.Order {
	return &model.Order{
		Quote:    quote,
		OrderID:  orderID,
		Status:   model.StatusDriverToRestaurant,
		Driver:   driverID,
		Region:   "BKK",
		ExpireAt: time.Now().Add(5 * time.Minute).UTC(),
	}
}

func createUpdateStatusRequest(container *ittest.IntegrationTestContainer, driverID, orderID string) *testutil.GinContextWithRecorder {
	gctx := testutil.NewContextWithRecorder()
	gctx.SetPUT("%s", fmt.Sprintf("/v1/order/%s/next-status", orderID))
	gctx.Authorized(container.RedisTokenStore, driverID)
	gctx.Body().JSON(struct{}{}).Build()
	return gctx
}

func createAdminCompleteOrderRequest(orderId string, userProcessPayment bool, comment string) *testutil.GinContextWithRecorder {
	gctx := testutil.NewContextWithRecorder()
	gctx.SetPOST("%s", fmt.Sprintf("/v1/admin/order/%s/complete", orderId))
	gctx.AdminAuthorized("<EMAIL>")
	gctx.Body().JSON(struct {
		UserProcessPayment bool   `json:"userProcessPayment"`
		Comment            string `json:"comment"`
	}{
		UserProcessPayment: userProcessPayment,
		Comment:            comment,
	}).Build()
	return gctx
}

func testAcceptOrderSuccessfully(t *testing.T, ftd *foodtestdata, orderID string, driverID string, body string) {
	gctx := testutil.NewContextWithRecorder()
	ftd.buildAcceptRequest(gctx, orderID, driverID, body, true)

	ctx := context.Background()
	ftd.updateDriverLocation(ctx, driverID)
	ftd.ctn.StubMapService.FastestRoute = &model.MapRoute{Distance: 1, Duration: 1}

	gctx.GinCtx().Request = gctx.GinCtx().Request.WithContext(toggle.EnableCtx(gctx.GinCtx().Request.Context(), toggle.TripEnabled))
	gctx.Send(ftd.ctn.GinEngineRouter)

	gctx.AssertResponseCode(t, 200)
}

func testAcceptUncompatibleOrder(t *testing.T, ftd *foodtestdata, orderID string, driverID string, body string) {
	gctx := testutil.NewContextWithRecorder()
	ftd.buildAcceptRequest(gctx, orderID, driverID, body, true)

	ctx := context.Background()
	ftd.updateDriverLocation(ctx, driverID)
	ftd.ctn.StubMapService.FastestRoute = &model.MapRoute{Distance: 1, Duration: 1}

	ftd.ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

	gctx.AssertResponseCode(t, 400)
}

func acceptOrderRequest(container *ittest.IntegrationTestContainer, orderID, driverID, body string) *testutil.GinContextWithRecorder {
	gctx := testutil.NewContextWithRecorder()
	gctx.SetPOST("%s", "/v1/order/"+orderID+"/accept")
	gctx.Authorized(container.RedisTokenStore, driverID)
	gctx.Body().RawJSON(body).Build()
	return gctx
}

func getOrder(t testing.TB, ctn *ittest.IntegrationTestContainer, orderID string) *model.Order {
	order, err := ctn.MongoOrderRepository.Get(context.Background(), orderID)
	require.NoError(t, err)
	return order
}

func captchaBody(choiceID int) string {
	type inner struct {
		ChoiceID int `json:"choiceId"`
	}
	type captcha struct {
		Inner inner `json:"captcha"`
	}

	c := captcha{
		Inner: inner{ChoiceID: choiceID},
	}

	b, err := json.Marshal(c)
	if err != nil {
		panic(err)
	}

	return string(b)
}

type foodtestdata struct {
	ctn                        *ittest.IntegrationTestContainer
	driverID                   string
	orderID                    string
	autoAssignedOrderID        string
	hybridOrderID              string
	orderWithActualAssigningID string
	order                      *model.Order
	correctID                  int
	wrongID                    int
}

func newFoodTestData(t *testing.T) *foodtestdata {
	data := foodtestdata{
		ctn:                        ittest.NewContainer(t),
		driverID:                   "DRV_PATTAYA_ONLINE",
		orderID:                    "LMF-3",
		autoAssignedOrderID:        "LMF-4",
		hybridOrderID:              "LMF-HYBRID",
		orderWithActualAssigningID: "LMF-withactualassigning",
	}

	data.order = getOrder(t, data.ctn, data.orderID)
	data.wrongID = data.correctID + 1

	return &data
}

func newFoodQueueTestData(t *testing.T, container *ittest.IntegrationTestContainer, driverID string, orderID string) *foodtestdata {
	data := foodtestdata{
		ctn:                 container,
		driverID:            driverID,
		autoAssignedOrderID: orderID,
	}

	order := getOrder(t, container, orderID)

	data.order = order
	data.wrongID = data.correctID + 1

	return &data
}

func (ftd *foodtestdata) buildAcceptRequest(gctx *testutil.GinContextWithRecorder, orderID string, driverID string, body string, withBody bool) {
	gctx.SetPOST("%s", "/v1/order/"+orderID+"/accept")
	gctx.Authorized(ftd.ctn.RedisTokenStore, driverID)

	if withBody {
		gctx.Body().RawJSON(body).Build()
	}
}

func (ftd *foodtestdata) updateDriverLocation(ctx context.Context, driverID string) {
	err := ftd.ctn.DriverLocationRepositoryForTest.UpdateDriverLocation(ctx, repository.UpdateDriverLocationRequest{DriverID: driverID, Location: model.LocationWithUpdatedAt{
		Lat: 13,
		Lng: 100,
	}})
	if err != nil {
		panic(err)
	}
	// wait for batch update...
	time.Sleep(time.Millisecond)
}

func (ftd *foodtestdata) autoAssignOrderToDriver(round int, orderID string, driverID string) {
	_, err := ftd.ctn.MongoAssignmentLogRepository.AssignToDrivers(context.Background(), round, orderID, 0, []repository.DriverDistance{{DriverID: driverID, Distance: 0}}, model.AssignmentLogOpt{AutoAssigned: true})
	if err != nil {
		panic(err)
	}
}

func (ftd *foodtestdata) autoAssignQueueOrderToDriver(round int, orderID string, driverID string, planRoute model.PlanRoute) {
	_, err := ftd.ctn.MongoAssignmentLogRepository.AssignToDrivers(context.Background(), round, orderID, 0, []repository.DriverDistance{{DriverID: driverID, Distance: 0}},
		model.AssignmentLogOpt{AutoAssigned: true, AllowQueueing: true, PlanRoute: planRoute})
	if err != nil {
		panic(err)
	}
}

type errorResp struct {
	Error string `json:"error"`
}

func prepareVerifyPhotoOrder(container *ittest.IntegrationTestContainer, t *testing.T, driverID, orderID string) {
	q := createSwitchFlowOrderQuote()
	o := createSwitchFlowOrder(orderID, driverID, q)

	err := container.MongoOrderRepository.CreateOrder(context.Background(), o)
	require.NoError(t, err)

	orderModel, err := container.MongoOrderRepository.Get(context.Background(), orderID)
	require.NoError(t, err)
	orderModel.Status = model.StatusDriverArrivedRestaurant

	err = container.MongoOrderRepository.UpdateOrder(context.Background(), orderModel)
	require.NoError(t, err)
}

func TestCalculateShouldVerify(t *testing.T) {
	t.Run("should apply flag correctly", func(t *testing.T) {
		driverID := "DRV_SHOULD_VERIFY"
		orderID := "LMF-VER-1"

		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_verify_complete_order")
		if err != nil {
			panic(err)
		}
		prepareVerifyPhotoOrder(container, t, driverID, orderID)

		gctx := createUpdateStatusRequest(container, driverID, orderID)
		gctx.Send(container.GinEngineRouter)

		od, err := container.MongoOrderRepository.Get(context.Background(), orderID)
		require.NoError(t, err)
		require.Equal(t, true, od.ShouldVerify)
	})

	t.Run("should not apply flag on same driver in one day", func(t *testing.T) {
		driverID := "DRV_SHOULD_NOT_VERIFY"
		orderID := "LMF-VER-1"

		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_verify_complete_order")
		if err != nil {
			panic(err)
		}

		prepareVerifyPhotoOrder(container, t, driverID, orderID)

		gctx := createUpdateStatusRequest(container, driverID, orderID)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		od, err := container.MongoOrderRepository.Get(context.Background(), orderID)
		require.NoError(t, err)
		require.Equal(t, false, od.ShouldVerify)
	})
}

func Test_ChangePaymentMethod(t *testing.T) {
	t.Run("should return 423 when order is locked", func(t *testing.T) {
		ctx := context.Background()
		container := ittest.NewContainer(t)
		orderID := "LMF-1"
		o, err := container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)
		o.Routes[0].Pauses = model.PauseSet{model.PauseConfirmPrice: true}
		o.Routes[1].Pauses = model.PauseSet{model.PauseCheckout: true}
		require.NoError(t, container.MongoOrderRepository.UpdateOrder(ctx, o))
		ok, err := container.RedisLocker.Lock(ctx, fmt.Sprintf("lock_order:%s", orderID))
		require.NoError(t, err)
		require.True(t, ok)

		gctx := changePaymentMethodRequest(container, orderID)
		gctx.AssertResponseCode(t, 423)

		err = container.RedisLocker.Unlock(ctx, fmt.Sprintf("lock_order:%s", orderID))
		require.NoError(t, err)
	})
}

func changePaymentMethodRequest(container *ittest.IntegrationTestContainer, orderID string) *testutil.GinContextWithRecorder {
	gctx := testutil.NewContextWithRecorder()
	gctx.SetPUT("/v1/delivery/food/order/%s/payment-method", orderID)
	gctx.Body().JSON(order.PaymentMethodRequest{}).Build()
	gctx.Send(container.GinEngineRouter)
	return gctx
}

func Test_Continue(t *testing.T) {
	ctx := context.Background()
	container := ittest.NewContainer(t)
	orderID := "LMF-1"
	o, err := container.MongoOrderRepository.Get(ctx, orderID)
	require.NoError(t, err)
	o.Routes[0].Pauses = model.PauseSet{model.PauseConfirmPrice: true}
	o.Routes[1].Pauses = model.PauseSet{model.PauseCheckout: true}
	require.NoError(t, container.MongoOrderRepository.UpdateOrder(ctx, o))
	t.Run("continue CONFIRM_PRICE fails if status is other than WAITING_USER_CONFIRM_PRICE", func(t *testing.T) {
		gctx := continueRequest(container, orderID, 0, string(model.PauseConfirmPrice))
		gctx.AssertResponseCode(t, 400)
	})
	t.Run("continue CONFIRM_PRICE also moves order status", func(t *testing.T) {
		o, err := container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)
		o.Status = model.StatusWaitingUserConfirmPrice
		require.NoError(t, container.MongoOrderRepository.UpdateOrder(ctx, o))

		gctx := continueRequest(container, orderID, 0, string(model.PauseConfirmPrice))
		gctx.AssertResponseCode(t, 200)

		o, err = container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)
		assert.False(t, o.Routes[0].Pauses[model.PauseConfirmPrice])
		assert.Equal(t, model.StatusUserConfirmedPrice, o.Status)
	})
	t.Run("continue CHECKOUT remove the pause", func(t *testing.T) {
		gctx := continueRequest(container, orderID, 1, string(model.PauseCheckout))
		gctx.AssertResponseCode(t, 200)
		o, err := container.MongoOrderRepository.Get(ctx, orderID)
		require.NoError(t, err)
		assert.False(t, o.Routes[1].Pauses[model.PauseCheckout])
	})
	t.Run("continue fails if the stop does not has the particular pause", func(t *testing.T) {
		gctx := continueRequest(container, orderID, 1, string(model.PauseCheckout))
		gctx.AssertResponseCode(t, 400)
	})
}

func continueRequest(container *ittest.IntegrationTestContainer, orderID string, pos int, pause string) *testutil.GinContextWithRecorder {
	type pauseReq struct {
		Pause string `json:"pause"`
	}
	gctx := testutil.NewContextWithRecorder()
	gctx.SetPOST("/v2/delivery/orders/%s/routes/%v/continue", orderID, pos)
	gctx.Body().JSON(pauseReq{Pause: pause}).Build()
	gctx.Send(container.GinEngineRouter)
	return gctx
}

func TestCalculateCancellationQuotaOnCompletingOrder(t *testing.T) {
	driverID := "DRV_PATTAYA_ONLINE"
	orderID := "LMF-10"

	prepareDriverWithQuota := func(testCtn *ittest.IntegrationTestContainer, cancellationQuota int, completedOrdersTillNextQuota int) {
		driver, err := testCtn.DriverRepository.FindDriverID(context.Background(), driverID)
		require.NoError(t, err)

		driver.CancellationQuota = &model.CancellationQuota{
			CancellationRateFreeQuota:     cancellationQuota,
			CompletedOrdersSinceLastQuota: completedOrdersTillNextQuota,
		}
		err = testCtn.DriverRepository.Update(context.Background(), driver)
		require.NoError(t, err)
	}

	expectDriverWithQuota := func(testCtn *ittest.IntegrationTestContainer, cancellationQuota int, completedOrdersTillNextQuota int) {
		driver, err := testCtn.DriverRepository.FindDriverID(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, cancellationQuota, driver.CancellationQuota.CancellationRateFreeQuota)
		require.Equal(t, completedOrdersTillNextQuota, driver.CancellationQuota.CompletedOrdersSinceLastQuota)
	}

	completeOrder := func(testCtn *ittest.IntegrationTestContainer) {
		pp := prepareTransaction{
			deliveryFeeSubtotal: 40.00,
			userDeliveryFee:     73.00,
			driverID:            driverID,
			orderID:             orderID,
			paymentMethod:       model.PaymentMethodCash,
			commission:          0,
			withholdingTax:      0.03,
		}
		_, err := completeOrderTransaction(t, testCtn, pp)
		require.NoError(t, err)
	}

	t.Run("add quota on driver without cancellation quota", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		completeOrder(container)
		expectDriverWithQuota(container, 2, 1)
	})

	t.Run("add quota on driver with 0 quota", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		prepareDriverWithQuota(container, 0, 0)
		completeOrder(container)
		expectDriverWithQuota(container, 0, 1)
	})

	t.Run("add quota on driver with 17 completed orders till next quota", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		prepareDriverWithQuota(container, 0, 17)
		completeOrder(container)
		expectDriverWithQuota(container, 1, 0)
	})

	t.Run("add quota on driver with max quota", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		prepareDriverWithQuota(container, 4, 0)
		completeOrder(container)
		expectDriverWithQuota(container, 4, 0)
	})
}
