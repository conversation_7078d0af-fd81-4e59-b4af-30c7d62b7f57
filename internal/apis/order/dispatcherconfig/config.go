package dispatcherconfig

import (
	"sync"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-shared/dispatcherconfig"
)

type AutoAssignDbConfig = dispatcherconfig.AutoAssignDbConfig
type AtomicAutoAssignDbConfig = dispatcherconfig.AtomicAutoAssignDbConfig

func ProvideAutoAssignDbConfig(configUpdater *config.DBConfigUpdater) *AtomicAutoAssignDbConfig {
	var cfg AtomicAutoAssignDbConfig
	configUpdater.Register(&cfg)
	return &cfg
}

var NewAtomicAutoAssignDbConfig = dispatcherconfig.NewAtomicAutoAssignDbConfig

// AutoAcceptConfig and AtomicAutoAcceptConfig mainly use by assigner on lm-driver
// it doesn't move to shared module as no need anymore when enable assign API and clean up code - lmf-16135
type AutoAcceptConfig struct {
	AutoAcceptEnabled      bool `envconfig:"AUTO_ACCEPT_ENABLED" default:"true"`
	FullyAutoAcceptEnabled bool `envconfig:"FULLY_AUTO_ACCEPT_ENABLED" default:"true"`
}

type AtomicAutoAcceptConfig struct {
	lock   sync.RWMutex
	Config AutoAcceptConfig
	config.Validatable[AutoAcceptConfig]
}

func (cfg *AtomicAutoAcceptConfig) Parse() {
	cfg.lock.Lock()
	defer cfg.lock.Unlock()
	envconfig.MustProcess("", &cfg.Config)
}

func (cfg *AtomicAutoAcceptConfig) Get() AutoAcceptConfig {
	cfg.lock.RLock()
	defer cfg.lock.RUnlock()
	return cfg.Config
}

func ProvideAutoAcceptConfig(configUpdater *config.DBConfigUpdater) *AtomicAutoAcceptConfig {
	var cfg AtomicAutoAcceptConfig
	configUpdater.Register(&cfg)
	return &cfg
}

// DistributionConfig is configuration for order distribution feature.
type DistributionConfig = dispatcherconfig.DistributionConfig

func ProvideDistributionConfig() (cfg DistributionConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func ProvideAtomicDistributionConfig(configUpdater *config.DBConfigUpdater) *AtomicDistributionConfig {
	var cfg AtomicDistributionConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type AtomicDistributionConfig = config.AtomicWrapper[DistributionConfig]

func NewAtomicDistributionConfig(cfg DistributionConfig) *AtomicDistributionConfig {
	return config.NewAtomicWrapper(cfg)
}
