package internalapi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	driverProvisionPb "git.wndv.co/go/proto/lineman/driver_provision/v1"
	interventionV1 "git.wndv.co/go/proto/lineman/price_intervention/v1"
	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/delivery-service/pkg/client"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	apiErr "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	repConverter "git.wndv.co/lineman/fleet-distribution/internal/apis/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/trip"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongotxn"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/driverprovision"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/priceintervention"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	cacheClient "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const (
	KeyJobID = "jobId"
	WHT      = 0.03
)

type InternalAPI struct {
	AssignmentLog              repository.AssignmentLogRepository
	OrderService               repository.OrderRepository
	DriverRepository           repository.DriverRepository
	DriverService              service.DriverServiceInterface
	DriverTransactionRepo      repository.DriverTransactionRepository
	DriverTransactionService   payment.DriverTransactionService
	DriverTransactionServiceV2 service.DriverTransactionServiceV2
	DriverRegistrationRepo     repository.DriverRegistrationRepository
	DriversDataStore           persistence.DriversDataStore
	IncentiveRepository        incentive.IncentiveRepository
	TransactionRepository      repository.TransactionRepository
	BanHistoryService          repository.BanHistoryRepository
	ServiceAreaRepo            repository.ServiceAreaRepository
	CliAreaRepo                repository.ClientAreaRepository
	RegionRepo                 repository.RegionRepository
	DriverLocationRepo         repository.DriverLocationRepository
	Cache                      cache.Cache
	UniversalClient            datastore.RedisClient
	otpSessionRepo             repository.OTPSessionRepo
	transService               payment.TransactionService
	LocationManager            service.LocationManager
	Rep                        rep.REPService
	Bus                        domain.EventBus
	StatisticRepo              repository.DriverStatisticRepository
	TransactionSchemeRepo      repository.TransactionSchemeRepository
	TripServices               service.TripServices
	TripRepository             repository.TripRepository
	Assigner                   service.Assigner
	VosService                 service.VOSService
	DriverActiveTimeRepo       repository.DriverActiveTimeRepository
	IncomeDailySummaryRepo     repository.IncomeDailySummaryRepository
	IncomeSummaryService       income.IncomeSummaryService
	Locker                     locker.Locker
	IMFKafkaProducer           kafcclient.IMFKafkaProducer
	GroupTransactionRepo       repository.GroupTransactionRepository
	Distributor                order.OrderDistributor
	Dispatcher                 dispatcher.Dispatcher
	TxnHelper                  transaction.TxnHelper
	ThrottledOrderRepo         repository.ThrottledOrderRepository
	DeferredOrderRepository    repository.DeferredOrderRepository
	DBConfigUpdater            config.ConfigUpdater
	PriceInterventionClient    priceintervention.PriceInterventionClient
	DriverProvisionClient      driverprovision.DriverProvisionClient
	FeatureFlagService         featureflag.Service
	PendingTransactionRepo     repository.PendingTransactionRepository
	delivery                   delivery.Delivery
	DistributionService        service.DistributionService
	ZoneRepo                   repository.ZoneRepository
	ThrottledOrderDBConfig     config.ThrottledOrderDBConfig
}

func (api *InternalAPI) GetDriverAssignmented(gctx *gin.Context) {
	orderID := gctx.Param("orderID")
	ctx := gctx.Request.Context()
	records, err := api.AssignmentLog.AssignedDrivers(ctx, orderID)
	if err != nil {
		logrus.Errorf("cannot get assigned drivers: %v", err)
	}
	apiutil.OK(gctx, struct {
		Drivers []model.Record `json:"drivers"`
	}{
		Drivers: records,
	})
}

func (api *InternalAPI) GetDriverRating(gctx *gin.Context) {
	orderID := gctx.Param("orderID")
	order, err := api.OrderService.Get(gctx.Request.Context(), orderID, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, struct {
		Comment string `json:"comment"`
		Score   uint32 `json:"score"`
	}{
		Comment: order.Comment,
		Score:   order.RatingScore,
	})
}

func (api *InternalAPI) SetExpireAt(gctx *gin.Context) {
	orderID := gctx.Param("orderID")
	var req SetExpireAtReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		_ = gctx.Error(err)
		return
	}
	order, err := api.OrderService.Get(gctx.Request.Context(), orderID)
	if err != nil {
		_ = gctx.Error(err)
		return
	}
	order.ExpireAt = req.ExpireAt

	if err := api.OrderService.UpdateOrder(gctx.Request.Context(), order); err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.OK(gctx, struct {
		OrderID  string    `json:"orderId"`
		ExpireAt time.Time `json:"expireAt"`
	}{
		OrderID:  order.OrderID,
		ExpireAt: order.ExpireAt,
	})
}

// SetDriverFreeCredit add free credit to driver if credit is less than the amount requested. Otherwise, deduct from
// non-free credit
func (api *InternalAPI) SetDriverFreeCredit(gctx *gin.Context) {
	req := struct {
		Amount types.Money `json:"amount"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
	}
	ctx := gctx.Request.Context()
	driverID := gctx.Param("driver_id")
	driverTxn, err := api.DriverTransactionRepo.FindByID(ctx, driverID)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiErr.ErrDriverNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
		return
	}
	adjustAmount := req.Amount - driverTxn.CreditBalance()

	if adjustAmount > 0 {
		freeCreditTransaction := model.NewFreeCreditTransactionInfo(driverID, adjustAmount)
		driverTxn.FreeCreditTransactions.Add(*freeCreditTransaction)
	} else if adjustAmount < 0 {
		freeCreditDeductAmount := adjustAmount.Abs()
		if adjustAmount.Abs() >= driverTxn.FreeCreditBalance() {
			freeCreditDeductAmount = driverTxn.FreeCreditBalance()
		}
		driverTxn.FreeCreditTransactions.Deduct(freeCreditDeductAmount)
		purchaseCreditDeductAmount := adjustAmount.Abs() - freeCreditDeductAmount
		driverTxn.PurchaseCreditBalance -= purchaseCreditDeductAmount
	}

	if adjustAmount != 0 {
		if err := api.DriverTransactionRepo.Update(ctx, driverTxn); err != nil {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
	}
	apiutil.OK(gctx, payment.NewDriverTransactionRes(*driverTxn))
}

func (api *InternalAPI) SetDriverCredit(gctx *gin.Context) {
	req := struct {
		Amount types.Money `json:"amount"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
	}
	ctx := gctx.Request.Context()
	driverID := gctx.Param("driver_id")
	driverTxn, err := api.DriverTransactionRepo.FindByID(ctx, driverID)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiErr.ErrDriverNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
	}
	driverTxn.PurchaseCreditBalance = req.Amount
	if err := api.DriverTransactionRepo.Update(ctx, driverTxn); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	apiutil.OK(gctx, payment.NewDriverTransactionRes(*driverTxn))
}

func (api *InternalAPI) SetDriverPositiveCredit(gctx *gin.Context) {
	req := struct {
		Amount types.Money `json:"amount"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
	}
	ctx := gctx.Request.Context()
	driverID := gctx.Param("driver_id")
	driverTxn, err := api.DriverTransactionRepo.FindByID(ctx, driverID)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiErr.ErrDriverNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
	}
	driverTxn.PositiveCreditBalance = req.Amount
	if err := api.DriverTransactionRepo.Update(ctx, driverTxn); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	apiutil.OK(gctx, payment.NewDriverTransactionRes(*driverTxn))
}

func (api *InternalAPI) SetCash(gctx *gin.Context) {
	req := struct {
		Amount types.Money `json:"amount"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
	}
	driverID := gctx.Param("driver_id")
	if err := api.DriverTransactionService.UpdateCash(gctx, driverID, req.Amount); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SetDriverOnline(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	ctx := gctx.Request.Context()

	if err := api.DriverRepository.SetCurrentStatus(ctx, driverID, model.StatusOnline); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	if _, err := api.Assigner.UnAssignTrip(ctx, driverID); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	now := time.Now()
	payloadDriver := repConverter.ToRepPayload(driverID, string(model.StatusOnline), "", &now)
	if err := api.Rep.Publish(rep.EventDriverUpdateStatus, &payloadDriver); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SetDriverOffline(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	driv, err := api.DriverRepository.GetProfile(gctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	if driv.Status != model.StatusOnline {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(errors.New("driver's status must be ONLINE")))
		return
	}

	driv.Offline()
	if err := api.DriverRepository.SetProfile(gctx, driv, nil); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	now := time.Now()
	payloadDriver := repConverter.ToRepPayload(driv.DriverID, string(driv.Status), string(driv.Region), &now)
	err = api.Rep.Publish(rep.EventDriverUpdateStatus, &payloadDriver)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) ResetLatestRandomAt(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	driv, err := api.DriverRepository.GetProfile(gctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	driv.LatestRandomAt = nil

	err = api.DriverRepository.Update(gctx, driv)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

// RemoveDriver delete driver and driver registration out of this service. Intention of this
// api is uses for testing register and approve driver feature. See LMFS-892 for more details.
func (api *InternalAPI) RemoveDriver(gctx *gin.Context) {
	logger := logrus.WithField("method", "RemoveDriver")
	ctx := gctx.Request.Context()
	lineUserID := crypt.NewLazyEncryptedString(gctx.Param("driver_id"))
	if err := api.DriversDataStore.Remove(ctx, bson.M{"line_uid": lineUserID}); err != nil {
		logger.Errorf("cannot remove driver registration: %v", err)
	}
	if err := api.DriverRegistrationRepo.RemoveByLineUID(ctx, lineUserID); err != nil {
		logger.Errorf("cannot remove driver registration: %v", err)
	}
	apiutil.OK(gctx, struct{}{})
}

func (api *InternalAPI) UnlockAutoAssignState(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	ctx := gctx.Request.Context()
	api.Locker.RemoveState(ctx, locker.DriverAutoAssignState(driverID))
	apiutil.NoContent(gctx)
}

func (api *InternalAPI) GetDriverLastAttempt(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	driverKeySlot := api.DriverActiveTimeRepo.GetDriverKeySlot(driverID)
	latestActiveTime, err := api.DriverActiveTimeRepo.GetDriverLatestActiveTime(gctx, driverID)
	if err != nil {
		if err == redis.Nil {
			apiutil.ErrNotFound(gctx, apiutil.NewFromString("DRIVER_LAST_ATTEMPT_NOT_FOUND", "driver last attempt not found"))
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
		return
	}
	apiutil.OK(gctx, struct {
		RedisKey      string `json:"redis_key"`
		LatestAttempt string `json:"latest_attempt"`
	}{
		RedisKey:      driverKeySlot,
		LatestAttempt: latestActiveTime.Format(time.DateTime),
	})
}

func (api *InternalAPI) SetDriverLastAttempt(gctx *gin.Context) {
	req := struct {
		Timestamp int64 `json:"timestamp"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
	}
	driverID := gctx.Param("driver_id")
	err := api.DriverService.UpdateDriverLastAttempt(gctx, driverID, service.WithOverrideDriverLatestAttempt(time.Unix(req.Timestamp, 0)))
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SetSMARatingScore(gctx *gin.Context) {
	req := struct {
		SMARatingScore float64 `json:"SMARatingScore"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
	}
	driverID := gctx.Param("driver_id")

	driv, err := api.DriverRepository.FindDriverID(gctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	driv.SMARatingScore = req.SMARatingScore

	err = api.DriverRepository.Update(gctx, driv)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) GetProfileHistories(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	driv, err := api.DriverRepository.FindDriverID(gctx, driverID, repository.WithReadSecondaryPreferred)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiutil.ErrNotFound(gctx, apiErr.ErrDriverNotExists())
			return
		}
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	res := struct {
		ProfileHistories []model.ProfileHistory `json:"profileHistories"`
	}{}
	res.ProfileHistories = driv.ProfileHistories

	apiutil.OK(gctx, res)
}

func (api *InternalAPI) SetProfileHistories(gctx *gin.Context) {
	req := struct {
		ProfileHistories []model.ProfileHistory `json:"ProfileHistories"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
	}
	driverID := gctx.Param("driver_id")

	driv, err := api.DriverRepository.FindDriverID(gctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	driv.ProfileHistories = req.ProfileHistories

	err = api.DriverRepository.Update(gctx, driv)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	apiutil.NoContent(gctx)
}

func transactionTypeMapper(req CreateTransactionReq) (model.TransactionAction, *model.TransactionInfo) {
	var action model.TransactionAction
	var txn *model.TransactionInfo

	switch req.Type {
	case model.IncentiveTransactionType:
		action = model.IncentiveTransactionAction
		txn = model.NewIncentiveTransactionInfo(req.DriverID, req.Amount, []string{})
		if req.SubType != "" {
			txn.SubType = req.SubType
		}
		txn.MissionID = req.MissionId
		txn.RefIDs = req.InfoRefIds
		if req.IncentiveSource != "" {
			txn.IncentiveSources = []string{req.IncentiveSource}
		}
	}

	return action, txn
}

func (api *InternalAPI) CreateTransaction(gctx *gin.Context) {
	var req CreateTransactionReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}

	if req.Category != model.CreditTransactionCategory && req.Category != model.WalletTransactionCategory {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(errors.New("invalid category")))
		return
	}

	if req.Amount <= 0 {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(errors.New("payload amount must be positive number")))
		return
	}

	var transactionDate *time.Time
	if req.TransactionDate != "" {
		t, err := time.ParseInLocation(time.DateOnly, req.TransactionDate, timeutil.BangkokLocation())
		if err != nil {
			apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(errors.New("invalid transaction date")))
			return
		}
		transactionDate = types.Ptr(timeutil.DateTruncateTZ(t, timeutil.BangkokLocation()))
	}

	ctx := gctx.Request.Context()
	driv, err := api.DriverRepository.GetProfile(ctx, req.DriverID)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			apiutil.ErrBadRequest(gctx, apiErr.ErrDriverNotExists())
			return
		}
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	action, txn := transactionTypeMapper(req)
	if txn == nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(fmt.Errorf("%s type not supported", req.Type)))
		return
	}

	bank, err := driv.GetDriverBankInfo()
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(errors.New("can't get driver bank info")))
		return
	}
	txn.WithdrawInfo.SetInfo(bank)

	infos := make([]model.TransactionInfo, 0)

	if req.IsSupportWht {
		whtTxn := model.NewWithholdingCreditTransactionInfo(req.DriverID, "", "", req.Amount.Mul(WHT))
		taxRefID := utils.GenerateUUID()
		whtTxn.WithdrawInfo.SetInfo(bank)
		whtTxn.TaxRefID = taxRefID
		txn.TaxRefID = taxRefID
		infos = append(infos, *whtTxn)
	}

	infos = append(infos, *txn)

	_, txns, err := api.DriverTransactionServiceV2.ProcessDriverTransaction(ctx,
		req.DriverID,
		model.SystemTransactionChannel,
		action,
		model.SuccessTransactionStatus,
		service.TransactionInfos(infos...),
		service.WithTransactionOptions(
			model.WithTransactionRefID(req.RefID),
			model.WithSourcesTransaction(toTransactionSources(req.Sources)),
			model.WithTransactionDate(transactionDate),
		),
	)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	var createdTxn *model.Transaction
	for idx := range txns {
		copy := txns[idx]
		if copy.Info.Type == model.WithholdingTransactionType {
			continue
		}
		createdTxn = &copy
	}

	if createdTxn == nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(errors.New("transaction not found")))
		return
	}

	apiutil.OK(gctx, CreateTransactionResponse{
		ID:        createdTxn.TransactionID,
		CreatedAt: createdTxn.CreatedAt,
	})
}

func (api *InternalAPI) UpdateDriverRole(gctx *gin.Context) {
	type request struct {
		Role string `json:"role"`
	}
	driverID := gctx.Param("driver_id")
	var req request
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}
	ctx := gctx.Request.Context()
	if err := api.DriversDataStore.Update(ctx, bson.M{"driver_id": driverID}, bson.M{"$set": bson.M{"driver_role": req.Role}}); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}
	apiutil.NoContent(gctx)
}

// RemoveCompletedOrders remove all completed orders and reset rating score from driver.
func (api *InternalAPI) RemoveCompletedOrders(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	driverID := gctx.Param("driver_id")
	if err := api.OrderService.RemoveCompletedOrderByDriverID(ctx, driverID); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	// reset driver rating score.
	driv, err := api.DriverRepository.GetProfile(ctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	driv.ResetRatingScore()
	err = api.DriverRepository.SetProfile(ctx, driv, nil)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	apiutil.OK(gctx, struct{}{})
}

func (api *InternalAPI) RemoveCancellationQuota(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	driverID := gctx.Param("driver_id")

	driv, err := api.DriverRepository.GetProfile(ctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	driv.CancellationQuota = nil
	err = api.DriverRepository.SetProfile(ctx, driv, nil)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, struct{}{})
}

// ResetSMARatingScore reset sma rating score and latest ratings
func (api *InternalAPI) ResetSMARatingScore(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	driverID := gctx.Param("driver_id")

	driv, err := api.DriverRepository.GetProfile(ctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	driv.ResetSMARatingScore()
	err = api.DriverRepository.SetProfile(ctx, driv, nil)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	apiutil.OK(gctx, struct{}{})
}

// RemoveBanHistories remove all ban-unban from driver.
func (api *InternalAPI) RemoveBanHistories(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	result, err := api.BanHistoryService.RemoveAllHistory(gctx.Request.Context(), driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, result)
}

// GetDriverLocation get location of specified driver
func (api *InternalAPI) GetDriverLocation(ctx *gin.Context) {
	driverID := ctx.Param("driver_id")

	reqCtx := ctx.Request.Context()
	driverLocation, err := api.DriverLocationRepo.GetDriverLocation(reqCtx, driverID)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			apiutil.ErrNotFound(ctx, apiutil.NewFromString(absapi.ERRCODE_NOT_FOUND, "location not found"))
		} else {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		}

		return
	}

	apiutil.OK(ctx, model.Location{Lat: driverLocation.Location.Lat, Lng: driverLocation.Location.Lng})
}

// UpdateDriverLocation updates driver's location.
func (api *InternalAPI) UpdateDriverLocation(gctx *gin.Context) {
	req := struct {
		Location model.Location `json:"location" binding:"required"`
	}{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		_ = gctx.Error(apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	ctx := gctx.Request.Context()
	driverID := gctx.Param("driver_id")
	err := api.DriverLocationRepo.UpdateDriverLocation(ctx, repository.UpdateDriverLocationRequest{
		DriverID: driverID,
		Location: model.LocationWithUpdatedAt{
			Lat:       req.Location.Lat,
			Lng:       req.Location.Lng,
			UpdatedAt: time.Now(),
		},
	})
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SetDriverWallet(ctx *gin.Context) {
	req := struct {
		Amount types.Money `json:"amount"`
	}{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	reqCtx := ctx.Request.Context()
	driverID := ctx.Param("driver_id")

	dt, err := api.DriverTransactionRepo.FindByID(reqCtx, driverID)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	dt.WalletBalance = req.Amount

	if err := api.DriverTransactionRepo.Update(reqCtx, dt); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(ctx, gin.H{})
}

func (api *InternalAPI) changeTripByOrder(ctx context.Context, ord *model.Order) error {
	input := service.TripOrderEvent{
		TripID:              ord.TripID,
		OrderID:             ord.OrderID,
		OrderStatus:         ord.Status,
		OrderLocation:       ord.Routes[ord.GetPayAtStop()].Location,
		OrderHeadTo:         ord.HeadTo,
		OrderRevampedStatus: ord.RevampedStatus,
	}

	_, err := api.TripServices.OnOrderChanged(ctx, input)
	return err
}

// MakeCompletedOrder make a completed order with adding driver id for testing.
func (api *InternalAPI) MakeCompletedOrder(gctx *gin.Context) {
	req := struct {
		// driverID that want to make.
		DriverID string `json:"driverId"`
		// ServiceType food or messenger
		ServiceType model.Service `json:"serviceType"`
	}{
		ServiceType: model.ServiceFood,
	}

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	orderID := fmt.Sprintf("LMAUTO-%d", time.Now().UnixNano())

	ord := &model.Order{
		Quote: model.Quote{
			QuoteID:     orderID,
			ServiceType: req.ServiceType,
		},
		OrderID: orderID,
		Driver:  req.DriverID,
	}
	if err := api.OrderService.CreateOrder(gctx.Request.Context(), ord); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	ord.SetStatus(model.StatusCompleted)
	if err := api.OrderService.UpdateAndUpsertRevision(gctx.Request.Context(), ord); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if err := api.changeTripByOrder(gctx.Request.Context(), ord); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, struct {
		OrderID string `json:"orderId"`
	}{
		OrderID: orderID,
	})
}

// MakeCompletedIncentiveOrder make a completed order with adding driver id for testing.
func (api *InternalAPI) MakeCompletedIncentiveOrder(gctx *gin.Context) {
	type reqComplete struct {
		// driverID that want to make.
		DriverID string `json:"driverId"`
		// ServiceType food or messenger
		ServiceType model.Service `json:"serviceType"`

		Lat0        float64 `json:"lat0"`
		Lng0        float64 `json:"lng0"`
		Lat1        float64 `json:"lat1"`
		Lng1        float64 `json:"lng1"`
		CreatedAt   string  `json:"createdAt" binding:"required"`
		CompletedAt string  `json:"completedAt"`
		Region      string  `json:"region"`
	}

	var req reqComplete

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	// bkk 11:30
	orderID := fmt.Sprintf("LMAUTO-%d", time.Now().UnixNano())

	ord := &model.Order{
		FraudStatus: "",
		Region:      model.RegionCode(req.Region),
		Quote: model.Quote{
			QuoteID:     orderID,
			ServiceType: req.ServiceType,
			Routes: []model.Stop{
				{
					Location: model.Location{Lat: req.Lat0, Lng: req.Lng0},
				},
				{
					Location: model.Location{Lat: req.Lat1, Lng: req.Lng1},
				},
			},
		},
		OrderID: orderID,
		Driver:  req.DriverID,
	}
	if err := api.OrderService.CreateOrder(gctx.Request.Context(), ord); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	ord.SetStatus(model.StatusCompleted)
	date, err := timeutil.ParseStringToDate(req.CreatedAt, timeutil.BangkokNow())
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	// bkk time is 11:30
	createdTime := time.Date(date.Year(), date.Month(), date.Day(), 4, 30, 0, 0, time.UTC)
	ord.History[string(model.StatusCompleted)] = createdTime
	ord.CreatedAt = createdTime

	if req.CompletedAt != "" {
		cpDate, err := timeutil.ParseStringToDate(req.CompletedAt, timeutil.BangkokNow())
		if err != nil {
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		cp := time.Date(cpDate.Year(), cpDate.Month(), cpDate.Day(), cpDate.Hour(), cpDate.Minute(), cpDate.Second(), cpDate.Nanosecond(), time.UTC)
		ord.History[string(model.StatusCompleted)] = cp
	}

	tid := fmt.Sprintf("TRIPAUTO-%d", time.Now().UnixNano())

	trip, err := model.NewEndedTrip(tid, *ord)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if err := trip.ForceMarkCompletedOrder(ord.OrderID, ord.Status); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if err := api.TripRepository.Create(gctx, trip); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	ord.TripID = trip.TripID

	if err := api.OrderService.UpdateAndUpsertRevision(gctx.Request.Context(), ord); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, struct {
		OrderID string `json:"orderId"`
	}{
		OrderID: orderID,
	})
}

func (api *InternalAPI) SetDeferOrderDistributionTime(gctx *gin.Context) {
	var req struct {
		OrderIDs         []string  `json:"order_ids"`
		DistributionTime time.Time `json:"distribution_time"`
	}

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	err := api.DeferredOrderRepository.UpdateDistributionTime(gctx, req.OrderIDs, req.DistributionTime)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	return
}

func (api *InternalAPI) SetFreeCreditExpiration(gctx *gin.Context) {
	var req struct {
		// driverID that want to make.
		ExpiredDate time.Time `json:"expiredDate"`
	}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	reqCtx := gctx.Request.Context()

	driverID, indexStr := gctx.Param("driver_id"), gctx.Param("index")
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	driver, err := api.DriverTransactionRepo.FindByID(reqCtx, driverID)
	if err != nil {
		apiutil.ErrNotFound(gctx, apiutil.NewFromError(absapi.ERRCODE_NOT_FOUND, err))
		return
	}

	result := driver.SetFreeCreditExpiredDated(index, req.ExpiredDate)
	if !result {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err := api.DriverTransactionRepo.Update(reqCtx, driver); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, struct {
		Size int `json:"size"`
	}{
		Size: len(driver.FreeCreditTransactions),
	})
}

func (api *InternalAPI) SetTodayEarning(gctx *gin.Context) {
	var req struct {
		// driverID that want to make.
		Fee        types.Money `json:"fee"`
		Commission types.Money `json:"commission"`
		Tax        types.Money `json:"tax"`
	}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	driverID := gctx.Param("driver_id")
	ctx := gctx.Request.Context()

	_, err := api.DriverRepository.GetProfile(ctx, driverID)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			apiutil.ErrNotFound(gctx, apiErr.ErrDriverNotExists())
			return
		}

		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	earning := model.NewDriverEarning(driverID, req.Fee, req.Commission, req.Tax, 0)
	if err := api.DriverRepository.SetTodayEarning(ctx, *earning); err != nil {
		logrus.Errorf("Failed to set driver today earning. earning=%v err=%s", earning, err)
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) AddTransaction(ctx *gin.Context) {
	var addTransactionReq payment.AddTransactionReq
	if err := ctx.ShouldBindJSON(&addTransactionReq); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	newTransaction := addTransactionReq.ToTransaction()

	reqCtx := ctx.Request.Context()
	err := api.TransactionRepository.Create(reqCtx, newTransaction)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiErr.ErrInternal(err))
		return
	}
	apiutil.OK(ctx, newTransaction)
}

func (api *InternalAPI) DeleteTransaction(ctx *gin.Context) {
	transactionID := ctx.Param("transaction_id")
	err := api.TransactionRepository.DeleteByID(ctx, transactionID)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiErr.ErrInternal(err))
		return
	}
	apiutil.NoContent(ctx)
}

func (api *InternalAPI) UpdateGroupTransactions(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	requestedBy := gctx.Query("requestedBy")

	var req struct {
		EffectiveTime time.Time `json:"effectiveTime"`
	}

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	if !req.EffectiveTime.IsZero() {
		query := persistence.BuildGroupTransactionQuery().WithRequestedBy(requestedBy)
		groupTxns, err := api.GroupTransactionRepo.Find(ctx, query, 0, 0)
		if err != nil {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}

		for i := range groupTxns {
			effectiveTime := req.EffectiveTime
			groupTxns[i].SetEffectiveTime(&effectiveTime)
		}

		for i := range groupTxns {
			if err := api.GroupTransactionRepo.Update(ctx, &groupTxns[i]); err != nil {
				apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
				return
			}
		}
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) DeleteGroupTransactions(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	requestedBy := gctx.Query("requestedBy")

	query := persistence.BuildGroupTransactionQuery().WithRequestedBy(requestedBy)
	groupTxns, err := api.GroupTransactionRepo.Find(ctx, query, 0, 0)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	for _, groupTxn := range groupTxns {
		if err := api.GroupTransactionRepo.Delete(ctx, groupTxn.ID()); err != nil {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) RemoveServiceAreaByRegion(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	areaID := gctx.Param("region")

	area, err := api.ServiceAreaRepo.GetByRegion(ctx, areaID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	if err := api.ServiceAreaRepo.Delete(ctx, area); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) RemoveClientArea(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	areaReq := gctx.Param(srvarea.KeyClientAreaArea)
	serviceType := gctx.Param(srvarea.KeyClientServiceType)

	area, err := api.CliAreaRepo.GetByRegionAndService(ctx, model.Service(serviceType), areaReq)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	if err := api.CliAreaRepo.Delete(ctx, area); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

// ResetDriverBalance reset driver balance
func (api *InternalAPI) ResetDriverBalance(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	newRecord := model.NewDriverTransaction(driverID)
	ctx := gctx.Request.Context()
	err := api.DriverTransactionRepo.Update(ctx, newRecord)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) UpdateCache(gctx *gin.Context) {
	ctx := gctx.Request.Context()

	err := api.RegionRepo.UpdateCache(ctx)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) UpdateDriverStatusCache(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	var req struct {
		Status       string `json:"status"`
		CurrentOrder string `json:"currentOrder"`
	}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	ctx := gctx.Request.Context()
	if err := cacheClient.SetDriverStatus(ctx, api.UniversalClient, driverID, model.DriverStatus(req.Status)); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	if err := cacheClient.SetCurrentOrder(ctx, api.UniversalClient, driverID, req.CurrentOrder); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
	}

	apiutil.NoContent(gctx)
}

type getDriverReq struct {
	Lat  float64 `form:"lat"`
	Lng  float64 `form:"lng"`
	From float64 `form:"from"`
	To   float64 `form:"to"`
}

func (api *InternalAPI) GetDriversByLocation(gctx *gin.Context) {
	var req getDriverReq
	err := gctx.BindQuery(&req)
	if err != nil {
		gctx.JSON(400, err)
		return
	}

	ctx := gctx.Request.Context()
	result, err := api.DriverLocationRepo.GetDrivers(ctx, repository.DriverLocationQuery{
		Location: model.Location{
			Lat: req.Lat,
			Lng: req.Lng,
		},
		From: req.From,
		To:   req.To,
	})
	if err != nil {
		gctx.Error(err)
		return
	}

	gctx.JSON(200, result)
}

func (api *InternalAPI) GetOTPSession(gctx *gin.Context) {
	lineUID := gctx.Param("lineUID")

	ses, err := api.otpSessionRepo.Load(gctx.Request.Context(), lineUID)
	if err != nil {
		gctx.JSON(500, err)
		return
	}
	gctx.JSON(200, ses)
}

func (api *InternalAPI) GetExportDriverTransaction(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	var request payment.ListTransactionReq
	if err := gctx.ShouldBindQuery(&request); err != nil {
		gctx.JSON(500, err)
		return
	}

	withdrawTxns, err := api.TransactionRepository.FindWalletWithdraw(ctx,
		repository.WithMongoOption(repository.WithReadSecondaryPreferred))
	if err != nil {
		gctx.JSON(500, err)
		return
	}

	exportData, err := api.transService.GenerateRiderWithdrawDataByTransaction(ctx, withdrawTxns)
	if err != nil {
		gctx.JSON(500, err)
		return
	}

	gctx.JSON(200, exportData)
}

func (api *InternalAPI) SetLogLevel(gctx *gin.Context) {
	var req struct {
		Level string `json:"level" binding:"required"`
	}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	level, err := logrus.ParseLevel(req.Level)
	if err != nil {
		gctx.JSON(500, err)
		return
	}

	logrus.Infof("Setting log level to %s.", level)
	logrus.SetLevel(level)

	apiutil.NoContent(gctx)
}

// RemoveDriverStatistic reset auto assign scoring from driver.
func (api *InternalAPI) RemoveDriverStatistic(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	err := api.StatisticRepo.Delete(gctx.Request.Context(), driverID)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(absapi.ERRCODE_NOT_FOUND, err))
		} else {
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, err))
		}
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) DeleteTransactionScheme(gctx *gin.Context) {
	id := gctx.Param("transaction_scheme_id")
	ctx := gctx.Request.Context()
	txnScheme, err := api.TransactionSchemeRepo.FindOneByID(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(absapi.ERRCODE_NOT_FOUND, err))
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
		return
	}

	if err := api.TransactionSchemeRepo.Delete(ctx, txnScheme); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SetUOBRefID(gctx *gin.Context) {
	var req struct {
		DriverID string `json:"driverId" binding:"required"`
		RefID    string `json:"refId" binding:"required"`
	}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}
	if err := api.DriverRepository.UpdateUobRefID(gctx, &model.Driver{DriverID: req.DriverID}, req.RefID); err != nil {
		logrus.Warn("update uobRef error: ", err.Error())
		apiutil.ErrInternalError(gctx, apiutil.NewFromError("500", err))
		return
	}
	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SetDriverCounts(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	var req struct {
		DailyCounts   []model.DailyCount   `json:"dailyCounts"`
		MonthlyCounts []model.MonthlyCount `json:"monthlyCounts"`
	}

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	orderPayload := order.CreateCountsSetOrderPayload(driverID, req.DailyCounts, req.MonthlyCounts)
	payload, err := json.Marshal(orderPayload)
	if err != nil {
		logrus.Warn("an error occurred when marshalling OrderAcceptPayload payload:", err)
	}

	orderModel := event.DriverEventOrderModel{
		Event:     string(event.EventCountsSet),
		Payload:   payload,
		EventTime: timeutil.BangkokNow(),
	}

	message, err := json.Marshal(orderModel)
	if err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	err = api.Bus.Publish(gctx, order.BusTopicDriverEventOrder, message)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) RemoveDriverCounts(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	var req struct {
		DeleteDailyCounts   bool `form:"deleteDailyCounts" binding:"required"`
		DeleteMonthlyCounts bool `form:"deleteMonthlyCounts" binding:"required"`
	}

	if err := gctx.ShouldBindQuery(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	orderPayload := order.CreateCountsDeleteOrderPayload(driverID, req.DeleteDailyCounts, req.DeleteMonthlyCounts)
	payload, err := json.Marshal(orderPayload)
	if err != nil {
		logrus.Warn("an error occurred when marshalling OrderAcceptPayload payload:", err)
	}

	orderModel := event.DriverEventOrderModel{
		Event:     string(event.EventCountsDelete),
		Payload:   payload,
		EventTime: timeutil.BangkokNow(),
	}

	message, err := json.Marshal(orderModel)
	if err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	err = api.Bus.Publish(gctx, order.BusTopicDriverEventOrder, message)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) RemoveIncentive(gctx *gin.Context) {
	id := gctx.Param("incentive_id")
	err := api.IncentiveRepository.Delete(gctx, id)
	if err != nil {
		_ = gctx.Error(err)
		return
	}
	apiutil.NoContent(gctx)
}

func (api *InternalAPI) RemoveAttendanceLogs(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	driver, err := api.DriverRepository.FindDriverID(context.Background(), driverID, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiErr.ErrDriverNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
		return
	}
	driver.AttendanceLogs = []model.AttendanceLog{}
	if err := api.DriverRepository.Update(context.Background(), driver); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) AssignToDriverRequest(gctx *gin.Context) {
	var req AssignToDriverRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}
	ctx := gctx.Request.Context()
	opt := req.Opt
	if _, err := api.AssignmentLog.AssignToDrivers(
		ctx, req.Round, req.OrderID, req.DeliveringRound, []repository.DriverDistance{{DriverID: req.DriverID, Distance: req.Distance}},
		opt,
	); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SyncTripState(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	tripID := gctx.Param("trip_id")

	if err := api.TripServices.SyncTripState(ctx, tripID); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) GetUnsyncTripIDs(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	start := gctx.Query("start")
	end := gctx.Query("end")
	startTime, _ := time.Parse(time.RFC3339, start)
	endTime, _ := time.Parse(time.RFC3339, end)

	tripIDs, err := api.TripRepository.GetUnsyncTripIDs(ctx, startTime, endTime, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiErr.ErrTripNotFound())
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		}
		return
	}

	apiutil.OK(gctx, struct {
		TripIDs []string `json:"tripIDs"`
	}{
		TripIDs: tripIDs,
	})
}

func (api *InternalAPI) BulkCompleteTrip(gctx *gin.Context) {
	var req BulkCompleteTripRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}
	tripIDs := req.TripIDs

	ctx := gctx.Request.Context()
	var successes []string
	var failures []string

	if len(tripIDs) == 0 {
		apiutil.ErrNotFound(gctx, apiErr.ErrTripNotFound())
		return
	}

	for _, tripID := range tripIDs {
		trip, err := api.TripServices.GetTripByID(ctx, tripID)
		if err != nil {
			logrus.Warnf("InternalCompleteTrip: GetTripByID tripID: %v ,err: %v", tripID, err)
			failures = append(failures, tripID)
			continue
		}

		if trip.IsFinished() {
			failures = append(failures, tripID)
			continue
		}

		err = api.TripServices.ForceSyncTripWithFinishedOrders(ctx, trip.TripID)
		if err != nil {
			logrus.Warnf("InternalCompleteTrip: tripSvc ForceSyncTripWithFinishedOrders tripID: %v ,err: %v", tripID, err)
			failures = append(failures, tripID)
			continue
		}

		err = api.TripServices.AdminCompleteTrip(ctx, trip.TripID)
		if err != nil {
			logrus.Warnf("InternalCompleteTrip: tripSvc AdminCompleteTrip tripID: %v ,err: %v", tripID, err)
			failures = append(failures, tripID)
			continue

		}

		successes = append(successes, tripID)
	}

	apiutil.OK(gctx, trip.NewBulkCompleteTripRes(successes, failures))
}

func (api *InternalAPI) GetIncomeSummary(fn func(*gin.Context)) func(*gin.Context) {
	return func(gctx *gin.Context) {
		if _, exists := gctx.GetQuery("realtime"); exists {
			gctx.Set("realtime", true)
			ctx := context.WithValue(gctx.Request.Context(), "realtime", true)
			gctx.Request = gctx.Request.WithContext(ctx)
		}

		driverId := gctx.Param("driver_id")
		driver.SetDriverIDToContext(gctx, driverId)
		fn(gctx)
	}
}

func (api *InternalAPI) SetDriverIncomeDailySummary(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	var req []struct {
		Date         time.Time   `json:"date" binding:"required"`
		TotalIncome  types.Money `json:"total_income"`
		TotalTrip    int         `json:"total_trip"`
		TotalOrder   int         `json:"total_order"`
		IncomeDetail struct {
			TotalWage               types.Money `json:"total_wage"`
			TotalIncentive          types.Money `json:"total_incentive"`
			TotalWithholdingTax     types.Money `json:"total_withholding_tax"`
			TotalTip                types.Money `json:"total_tip"`
			TotalEGSOnTopBonus      types.Money `json:"total_egs_on_top_bonus"`
			TotalGuaranteeIncentive types.Money `json:"total_guarantee_incentive"`
		} `json:"income_detail"`
	}

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	for _, v := range req {
		incomeDailySummary := model.NewIncomeDailySummary(
			v.Date,
			driverID,
			v.TotalIncome,
			v.TotalTrip,
			v.TotalOrder,
			model.NewIncomeDailyDetail(
				v.IncomeDetail.TotalWage,
				v.IncomeDetail.TotalIncentive,
				v.IncomeDetail.TotalWithholdingTax,
				v.IncomeDetail.TotalTip,
				v.IncomeDetail.TotalEGSOnTopBonus,
				v.IncomeDetail.TotalGuaranteeIncentive,
			),
			"add from internal api",
		)

		_, err := api.IncomeDailySummaryRepo.Upsert(gctx.Request.Context(), incomeDailySummary)
		if err != nil {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) RemoveDriverIncomeDailySummary(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	err := api.IncomeDailySummaryRepo.DeleteByDriverID(context.Background(), driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) GetTotalIncome(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	var req TotalIncomeReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	if driverID == "" {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "empty driver id"))
		return
	}

	if req.Start.IsZero() || req.End.IsZero() {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "start and end can't be empty"))
		return
	}

	if !timeutil.IsSameDate(req.Start, req.End, timeutil.BangkokLocation()) {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "start and end should be the same date"))
		return
	}

	if req.Start.After(req.End) {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "start can't after end"))
		return
	}

	ctx := gctx.Request.Context()
	periodSummary, err := api.IncomeSummaryService.Query(ctx, income.IncomeSummaryRequest{
		DriverId:    driverID,
		StartDate:   time.Time{}, // ignore start date, use custom start-end instead
		Granularity: income.CUSTOM,
	}, income.WithRealtimeQuery(),
		income.WithCustomGranularity(req.Start, req.End),
		income.WithBlacklistTransactionType([]model.TransactionType{model.GoodwillTransactionType}),
		income.WithExcludePendingTransactionId(),
	)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	proRateCoin, err := api.IncomeSummaryService.CalculateProrateCoin(ctx, req.Start, req.End, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	ordersLen, err := api.IncomeSummaryService.CountCompletedOrdersInDay(ctx, req.Start, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	proRateIncentive, err := api.IncomeSummaryService.CalculateProrateIncentive(ctx, req.Start.AddDate(0, 0, 1), driverID,
		periodSummary.Summary.TotalOrders(), ordersLen)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	pendingTransactionList, err := api.PendingTransactionRepo.FindAllByDriverIDAndStatusesAndTransactionTypesAndPeriodTransactionDate(
		ctx,
		driverID,
		[]model.PendingTransactionCollectionStatus{
			model.PendingPendingTransactionCollectionStatus,
			model.TransactionCreatedPendingTransactionCollectionStatus,
			// When ops team found order fault, they always will count income to rider
			model.RejectedPendingTransactionCollectionStatus,
		},
		[]model.TransactionType{model.DeliveryFeeTransactionType, model.CommissionTransactionType, model.CouponTransactionType},
		req.Start,
		req.End)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, TotalIncomeResponse{
		OrderIDs:                     periodSummary.OrderIDs,
		TotalWage:                    periodSummary.Summary.TotalDriverWage(),
		TotalOntop:                   periodSummary.Summary.TotalOntop(),
		TotalCoin:                    proRateCoin,
		TotalProductivityIncentive:   proRateIncentive,
		TotalOrdersInGuaranteePeriod: periodSummary.Summary.TotalOrders(),
		TotalOrdersInDay:             ordersLen,
		TotalPendingWage:             getTotalPendingWageOfGetTotalIncome(pendingTransactionList),
	})
}

func getTotalPendingWageOfGetTotalIncome(pendingList []model.PendingTransaction) types.Money {
	totalPendingWage := types.NewMoney(0)
	for _, pendingTnx := range pendingList {
		switch pendingTnx.TransactionInfo.Type {
		case model.CommissionTransactionType:
			totalPendingWage = totalPendingWage.Sub(pendingTnx.TransactionInfo.Amount)
		case model.DeliveryFeeTransactionType, model.CouponTransactionType:
			totalPendingWage = totalPendingWage.Add(pendingTnx.TransactionInfo.Amount)
		}
	}
	return totalPendingWage
}

func (api *InternalAPI) IMFKafkaConnectionCheck(gctx *gin.Context) {
	err := api.IMFKafkaProducer.CheckConnection(gctx.Request.Context())
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}
	apiutil.OK(gctx, "OK")
}

// DistributeOrder
// response with error code = apiutil.ErrValidateDistributeOrderFailed if the order can't be distributed anymore
func (api *InternalAPI) DistributeOrder(gctx *gin.Context) {
	var req DistributeOrderRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}
	if err := req.ValidateDistributeOrderRequest(); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(apiutil.ErrValidateDistributeOrderFailed, err))
		return
	}

	ctx := gctx.Request.Context()
	order, err := api.OrderService.Get(ctx, req.OrderID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiutil.ErrNotFound(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "data not found"))
			return
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
	}

	if order.Status != model.StatusAssigningDriver {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "order status isn't assigning"))
		return
	}

	if timeutil.BangkokNow().After(order.ExpireAt) {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "order was expired"))
		return
	}

	_, err = api.Distributor.Distribute(ctx, order, order.ExpireAt, order.Routes[0].Location)
	if err != nil {
		var validateErr *distribution.ErrValidationDistribution
		if errors.As(err, &validateErr) {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(apiutil.ErrValidateDistributeOrderFailed, validateErr))
			return
		}
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, gin.H{})
}

// DistributeOrderV2
// response with error code = apiutil.ErrValidateDistributeOrderFailed if the order can't be distributed anymore
func (api *InternalAPI) DistributeOrderV2(gctx *gin.Context) {
	var req DistributeOrderV2Request
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}
	if err := req.ValidateDistributeOrderRequest(); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(apiutil.ErrValidateDistributeOrderFailed, err))
		return
	}

	ctx := gctx.Request.Context()
	order, err := api.OrderService.Get(ctx, req.OrderID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiutil.ErrNotFound(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "data not found"))
			return
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
	}

	if order.Status != model.StatusAssigningDriver {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "order status isn't assigning"))
		return
	}

	if timeutil.BangkokNow().After(order.ExpireAt) {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "order expired"))
		return
	}

	if order.RedistributionState.IsBatchDistribution() {
		_, err = api.Distributor.Distribute(ctx, order, order.ExpireAt, order.Routes[0].Location)
		if err != nil {
			var validateErr *distribution.ErrValidationDistribution
			if errors.As(err, &validateErr) {
				apiutil.ErrBadRequest(gctx, apiutil.NewFromError(apiutil.ErrValidateDistributeOrderFailed, validateErr))
				return
			}
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
	} else {
		_, err = api.Distributor.RedistributeV2(ctx, order, order.Routes[0].Location, req.RidersTriedAssigning, req.IsRedistributionRequired)
		if err != nil {
			var validateErr *distribution.ErrValidationDistribution
			if errors.As(err, &validateErr) {
				apiutil.ErrBadRequest(gctx, apiutil.NewFromError(apiutil.ErrValidateDistributeOrderFailed, validateErr))
				return
			}
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
	}
	apiutil.OK(gctx, gin.H{})
}

func (api *InternalAPI) RedistributeOrder(gctx *gin.Context) {
	var req RedistributeOrderRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}
	ctx := gctx.Request.Context()
	order, err := api.OrderService.Get(ctx, req.OrderID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiutil.ErrNotFound(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "data not found"))
			return
		} else {
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
	}

	if order.Status != model.StatusAssigningDriver {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "order status isn't assigning"))
		return
	}

	if timeutil.BangkokNow().After(order.ExpireAt) {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(apiutil.ErrValidateDistributeOrderFailed, "order expired"))
		return
	}

	_, err = api.Distributor.Redistribute(ctx, order, order.Routes[0].Location, req.RedistributionState, req.RidersTriedAssigning)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, gin.H{})
}

func (api *InternalAPI) DistributeOrdersInZone(gctx *gin.Context) {
	var req DistributeOrdersInZoneRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}
	zoneID, err := primitive.ObjectIDFromHex(req.ZoneID)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("invalid zone id")))
		return
	}
	ctx := gctx.Request.Context()

	start := timeutil.BangkokNow()
	defer func() {
		diff := timeutil.BangkokNow().Sub(start)
		logrus.Infof("distribute-order-in-zone: the execution of doing batch distribute for zoneId: %v is %v seconds", zoneID.Hex(), diff.Seconds())
	}()

	query := model.ThrottledOrderQuery{ZoneID: &zoneID, IsProcessed: types.NewBool(false)}
	if req.UsingFetchedThrottledOrders {
		objectIDs := make([]primitive.ObjectID, 0, len(req.ThrottledOrderObjectIDs))
		for _, id := range req.ThrottledOrderObjectIDs {
			oid, err := primitive.ObjectIDFromHex(id)
			if err != nil {
				logrus.Errorf("can't decode objectID from hex: %v zoneID: %v", id, zoneID)
				continue
			}
			objectIDs = append(objectIDs, oid)
		}
		query.ObjectIDs = objectIDs
	}
	if api.ThrottledOrderDBConfig.IsUseSecondaryDB {
		ctx = mongotxn.WithSecondaryDB(ctx)
	}

	res, err := api.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		now := timeutil.BangkokNow()
		throttledOrders, err := api.ThrottledOrderRepo.Find(sessCtx, query, 0, 0)
		if err != nil {
			return nil, errors.New("can't find unprocessed throttled orders")
		}
		if err := api.ThrottledOrderRepo.SetProcessedAt(sessCtx, throttledOrders, now); err != nil {
			return nil, errors.New("can't set processed_at")
		}
		return throttledOrders, nil
	}, transaction.WithLabel("InternalAPI.DistributeOrdersInZone"))
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	orders, ok := res.([]model.ThrottledOrder)
	if !ok {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	orders, err = api.redistributeInvalidThrottledOrders(ctx, orders)
	if err != nil {
		err = fmt.Errorf("cannot redistribute invalid throttled orders: %w", err)
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	if len(orders) > 0 {
		wait, err := api.Distributor.BatchDistribute(ctx, orders, zoneID)
		if err != nil {
			logrus.Errorf("distribute-order-in-zone: can't distribute orders for zoneId: %v, err: %v", zoneID.Hex(), err)
			apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
			return
		}
		wait()
	}

	orderIDs := make([]string, 0, len(orders))
	for _, o := range orders {
		orderIDs = append(orderIDs, o.OrderID)
	}
	apiutil.OK(gctx, NewDistributeOrdersInZoneResponse(orderIDs))
}

func (api *InternalAPI) redistributeInvalidThrottledOrders(ctx context.Context, throttledOrders []model.ThrottledOrder) ([]model.ThrottledOrder, error) {
	invalidOrders, notRedistributeOrders := fp.PartitionSlice(model.IsThrottledOrderInvalidated, throttledOrders)
	if len(invalidOrders) == 0 {
		return notRedistributeOrders, nil
	}

	for _, x := range invalidOrders {
		orderID := x.OrderID
		bgCtx := safe.NewContextWithSameWaitGroup(ctx)
		safe.GoFuncWithCtx(bgCtx, func() {
			if err := api.DistributionService.PublishRedistributeOrderEvent(bgCtx, orderID, nil); err != nil {
				logrus.Errorf("cannot publish redistribute event order id %s: %v", orderID, err)
			}
		})
	}
	return notRedistributeOrders, nil
}

func (api *InternalAPI) PublishDistributeOrderEvent(gctx *gin.Context) {
	var req struct {
		OrderID string `json:"orderId"`
	}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}

	ctx := gctx.Request.Context()
	if err := api.DistributionService.PublishDistributeOrderEvent(ctx, req.OrderID); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) RefreshDBConfigs(gctx *gin.Context) {
	api.DBConfigUpdater.Fetch()
	apiutil.NoContent(gctx)
}

// TODO remove this function after finished testing
func (api *InternalAPI) CheckPIPAPIAcccessibility(gctx *gin.Context) {
	type RequestQuery struct {
		ModelTitle string   `json:"modelTitle" form:"modelTitle"`
		IsFuzzy    bool     `json:"isFuzzy" form:"isFuzzy"`
		Regions    []string `json:"regions" form:"regions"`
	}

	var req RequestQuery
	if err := gctx.ShouldBindQuery(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError("400", err))
		return
	}

	regionReqs := make([]*interventionV1.RegionRequest, 0)
	for _, region := range req.Regions {
		regionReqs = append(regionReqs, &interventionV1.RegionRequest{
			Region: region,
		})
	}

	protoReq := &interventionV1.GetDeliveryFeeOntopByRegionRequest{
		ModelTitle: req.ModelTitle,
		Fuzzy:      req.IsFuzzy,
		Regions:    regionReqs,
	}
	res, err := api.PriceInterventionClient.GetDeliveryFeeOntopByRegion(gctx, protoReq)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, res)
}

func (api *InternalAPI) SetDriverRecommendation(gctx *gin.Context) {
	var req SetDriverRecommendationReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		gctx.Error(err)
		return
	}
	recommendation, err := api.DriverProvisionClient.CreateRecommendation(gctx, &driverProvisionPb.CreateRecommendationRequest{DriverId: req.DriverID, Lat: req.Lat, Lng: req.Lng})
	if err != nil {
		gctx.Error(err)
		return
	}
	expiredAt, err := time.Parse(time.RFC3339, recommendation.ExpiredAt)
	if err != nil {
		gctx.Error(err)
		return
	}
	if len(recommendation.Areas) <= 0 {
		apiutil.OK(gctx, gin.H{})
		return
	}
	areas := make([]model.RecommendedH3, len(recommendation.Areas))
	h3AreasIndex := make(map[string]model.RecommendedH3)
	for i, area := range recommendation.Areas {
		recommendedH3 := model.RecommendedH3{
			H3ID:        area.H3Id,
			Name:        area.Name,
			FullAddress: area.FullAddress,
			WaitingTime: area.WaitingTime,
			Lat:         area.Lat,
			Lng:         area.Lng,
		}
		areas[i] = recommendedH3
		h3AreasIndex[recommendedH3.H3ID] = recommendedH3
	}
	rec := &model.H3Recommendation{
		RecommendationID: recommendation.Id,
		Areas:            areas,
		H3AreasIndex:     h3AreasIndex,
		ExpiredAt:        expiredAt,
	}
	if err := api.DriverRepository.SetDriverH3Recommendation(gctx, req.DriverID, rec); err != nil {
		gctx.Error(err)
		return
	}
	apiutil.OK(gctx, gin.H{})
}

func (api *InternalAPI) UnsetDriverRecommendation(gctx *gin.Context) {
	var req SetDriverRecommendationReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		gctx.Error(err)
		return
	}
	if err := api.DriverRepository.SetDriverH3Recommendation(gctx, req.DriverID, nil); err != nil {
		gctx.Error(err)
		return
	}
	apiutil.OK(gctx, gin.H{})
}

func (api *InternalAPI) GetSalesForcePrechatInfos(gctx *gin.Context) {
	trip, err := api.TripServices.GetTripFromOrderID(gctx, gctx.Param("orderID"))
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiutil.ErrNotFound(gctx, apiutil.NewFromString(absapi.ERRCODE_NOT_FOUND, "data not found"))
			return
		}
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}
	apiutil.OK(gctx, NewPrechatInfosResponse(trip))
}

func (api *InternalAPI) CheckUnleashFlag(gctx *gin.Context) {
	var req CheckFeatureFlagReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		gctx.Error(err)
		return
	}

	if req.IsBoolean {
		val := api.FeatureFlagService.IsEnabled(gctx, req.Flag)
		apiutil.OK(gctx, map[string]any{
			req.Flag: val,
		})
		return
	}

	variant := api.FeatureFlagService.GetVariant(req.Flag)
	apiutil.OK(gctx, map[string]any{
		req.Flag:                               variant.Payload.Value,
		fmt.Sprintf("%s_type", req.Flag):       variant.Payload.Type,
		fmt.Sprintf("is_%s_enabled", req.Flag): variant.Enabled,
	})
}

func (api *InternalAPI) ListDriverTransactionsByTransRefID(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	if driverID == "" {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "empty driver id"))
		return
	}

	transRefID := gctx.Param("trans_ref_id")
	if transRefID == "" {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "empty trans ref id"))
		return
	}

	var req ListDriverTransactionsByTransRefIDReq
	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}

	query := persistence.TransactionQuery{
		DriverID:   driverID,
		TransRefId: transRefID,
	}

	if req.Type != "" {
		query.Types = []model.TransactionType{model.TransactionType(req.Type)}
	}

	if req.SubType != "" {
		query.SubTypes = []model.TransactionSubType{model.TransactionSubType(req.SubType)}
	}

	trans, count, err := api.transService.ListTransaction(gctx.Request.Context(), query, 0, 0)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	resp := NewListDriverTransactionByTransRefIDResponse(trans, count)

	apiutil.OK(gctx, resp)
}

func (api *InternalAPI) PostDeliveryCircuitBreaker(gctx *gin.Context) {
	var req struct {
		OrderID string `json:"orderId"`
	}

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}

	err := api.delivery.DriverAccepted(context.Background(), &client.DriverAcceptedRequest{
		OrderID: req.OrderID,
	})
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *InternalAPI) SearchDriverInMultipolygon(gctx *gin.Context) {
	var req SearchDriverInMultipolygonReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}

	driversWithLocation, err := api.LocationManager.DirectSearchDriversInMultiPolygon(gctx.Request.Context(), repository.DriverLocationInMultiPolygonQuery{
		Coordinates: req.Coordinates,
	})
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	response := NewListDriversWithLocationResponse(driversWithLocation)
	apiutil.OK(gctx, response)
}

func (api *InternalAPI) SearchDriverInRadius(gctx *gin.Context) {
	var req SearchDriverInRadiusReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}

	driversWithLocation, err := api.LocationManager.DirectSearchDrivers(gctx.Request.Context(), repository.DriverLocationQuery{
		IsBikeService: req.Service == model.ServiceBike,
		Location:      req.Location,
		To:            req.To,
		Limit:         req.Limit,
		Service:       req.Service,
	})
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	response := NewListDriversWithLocationResponse(driversWithLocation)
	apiutil.OK(gctx, response)
}

func (api *InternalAPI) CheckIfCandidatesEnough(gctx *gin.Context) {
	var req CheckIfCandidatesEnoughReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErr.ErrInvalidRequest(err))
		return
	}

	ctx := gctx.Request.Context()
	enough, err := api.Distributor.CheckIfCandidatesEnough(ctx, req.OrderID, req.CandidatesRequired)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, CheckIfCandidatesEnoughRes{
		Enough: enough,
	})
}

func ProvideInternalAPI(
	ordsvc repository.OrderRepository,
	aslog repository.AssignmentLogRepository,
	driverRepository repository.DriverRepository,
	driversvc service.DriverServiceInterface,
	regisRepo repository.DriverRegistrationRepository,
	drivds persistence.DriversDataStore,
	driverTransRepo repository.DriverTransactionRepository,
	driverTransService payment.DriverTransactionService,
	driverTransServiceV2 service.DriverTransactionServiceV2,
	incentiveRepo incentive.IncentiveRepository,
	transactionRepo repository.TransactionRepository,
	banHistorySvc repository.BanHistoryRepository,
	areaRepo repository.ServiceAreaRepository,
	cliAreaRepo repository.ClientAreaRepository,
	regionRepo repository.RegionRepository,
	driverLocationRepo repository.DriverLocationRepository,
	cache cache.Cache,
	otpSessionRepo repository.OTPSessionRepo,
	transService payment.TransactionService,
	locationManager service.LocationManager,
	rep rep.REPService,
	bus domain.EventBus,
	universalClient datastore.RedisClient,
	statisticRepo repository.DriverStatisticRepository,
	transactionSchemeRepo repository.TransactionSchemeRepository,
	tripSvcs service.TripServices,
	tripRepository repository.TripRepository,
	assigner service.Assigner,
	vosService service.VOSService,
	driverActiveTimeRepo repository.DriverActiveTimeRepository,
	incomeDailySummaryRepo repository.IncomeDailySummaryRepository,
	incomeSummarySvc income.IncomeSummaryService,
	redisLocker locker.Locker,
	imfKafkaProducer kafcclient.IMFKafkaProducer,
	groupTransactionRepo repository.GroupTransactionRepository,
	txnHelper transaction.TxnHelper,
	throttledOrderRepo repository.ThrottledOrderRepository,
	deferredOrderRepository repository.DeferredOrderRepository,
	distributor order.OrderDistributor,
	dispatcher dispatcher.Dispatcher,
	dbConfigUpdater config.ConfigUpdater,
	priceInterventionClient priceintervention.PriceInterventionClient,
	driverProvisionClient driverprovision.DriverProvisionClient,
	featureFlagSvc featureflag.Service,
	pendingTransactionRepo repository.PendingTransactionRepository,
	delivery delivery.Delivery,
	distributionService service.DistributionService,
	zoneRepo repository.ZoneRepository,
	throttledOrderDBConfig config.ThrottledOrderDBConfig,
) *InternalAPI {
	return &InternalAPI{
		AssignmentLog:              aslog,
		OrderService:               ordsvc,
		DriverRepository:           driverRepository,
		DriverService:              driversvc,
		DriverTransactionRepo:      driverTransRepo,
		DriverTransactionService:   driverTransService,
		DriverTransactionServiceV2: driverTransServiceV2,
		DriverRegistrationRepo:     regisRepo,
		DriversDataStore:           drivds,
		IncentiveRepository:        incentiveRepo,
		TransactionRepository:      transactionRepo,
		BanHistoryService:          banHistorySvc,
		ServiceAreaRepo:            areaRepo,
		CliAreaRepo:                cliAreaRepo,
		RegionRepo:                 regionRepo,
		DriverLocationRepo:         driverLocationRepo,
		Cache:                      cache,
		UniversalClient:            universalClient,
		otpSessionRepo:             otpSessionRepo,
		transService:               transService,
		LocationManager:            locationManager,
		Rep:                        rep,
		Bus:                        bus,
		StatisticRepo:              statisticRepo,
		TransactionSchemeRepo:      transactionSchemeRepo,
		TripServices:               tripSvcs,
		TripRepository:             tripRepository,
		Assigner:                   assigner,
		VosService:                 vosService,
		DriverActiveTimeRepo:       driverActiveTimeRepo,
		IncomeDailySummaryRepo:     incomeDailySummaryRepo,
		IncomeSummaryService:       incomeSummarySvc,
		Locker:                     redisLocker,
		IMFKafkaProducer:           imfKafkaProducer,
		GroupTransactionRepo:       groupTransactionRepo,
		TxnHelper:                  txnHelper,
		ThrottledOrderRepo:         throttledOrderRepo,
		DeferredOrderRepository:    deferredOrderRepository,
		Distributor:                distributor,
		Dispatcher:                 dispatcher,
		DBConfigUpdater:            dbConfigUpdater,
		PriceInterventionClient:    priceInterventionClient,
		DriverProvisionClient:      driverProvisionClient,
		FeatureFlagService:         featureFlagSvc,
		PendingTransactionRepo:     pendingTransactionRepo,
		delivery:                   delivery,
		DistributionService:        distributionService,
		ZoneRepo:                   zoneRepo,
		ThrottledOrderDBConfig:     throttledOrderDBConfig,
	}
}

func parseDate(date string) time.Time {
	if date == "yesterday" {
		return timeutil.BangkokNow().Add(-1 * 24 * time.Hour)
	}

	if date == "today" {
		return timeutil.BangkokNow()
	}

	parse, err := time.Parse("2/1/2006", date)
	if err != nil {
		return timeutil.BangkokNow()
	}

	return parse
}
