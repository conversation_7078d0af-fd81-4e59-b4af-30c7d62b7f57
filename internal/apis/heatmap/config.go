package heatmap

import (
	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	FirstTierColor                   string `envconfig:"HEATMAP_FIRST_TIER_COLOR"  default:"#BFEA3C24"`
	SecondTierColor                  string `envconfig:"HEATMAP_SECOND_TIER_COLOR" default:"#BFF17051"`
	ThirdTierColor                   string `envconfig:"HEATMAP_THIRD_TIER_COLOR" default:"#99F38A68"`
	FourthTierColor                  string `envconfig:"HEATMAP_FOURTH_TIER_COLOR" default:"#80F8AD87"`
	ZeroTierColor                    string `envconfig:"HEATMAP_FOURTH_TIER_COLOR" default:"#00000000"`
	MaxDistance                      int    `envconfig:"HEATMAP_MAX_DISTANCE" default:"10000"`
	Resolution                       int    `envconfig:"HEATMAP_RESOLUTION" default:"8"`
	HeatMapTimeSlotInMinute          int    `envconfig:"HEATMAP_TIMES_LOT_IN_MINUTE" default:"-10"`
	OnTopFareMaxDistance             int    `envconfig:"HEATMAP_ON_TOP_FARE_MAX_DISTANCE" default:"30000"`
	OnTopFareNextHours               int    `envconfig:"HEATMAP_ON_TOP_FARE_FOR_NEXT_HOURS"  default:"12"`
	OnTopFareActiveStatusLabel       string `envconfig:"HEATMAP_ON_TOP_FARE_ACTIVE_STATUS_LABEL"  default:"กำลังมีโบนัส"`
	OnTopFareInActiveStatusLabel     string `envconfig:"HEATMAP_ON_TOP_FARE_INACTIVE_STATUS_LABEL"  default:"ยังไม่ถึงช่วงเวลา"`
	OnTopFareActiveColor             string `envconfig:"HEATMAP_ON_TOP_FARE_ACTIVE_COLOR"  default:"#0EC963"`
	OnTopFareInActiveColor           string `envconfig:"HEATMAP_ON_TOP_FARE_INACTIVE_COLOR"  default:"#777777"`
	OnTopFareActiveBackgroundColor   string `envconfig:"HEATMAP_ON_TOP_FARE_ACTIVE_BACKGROUND_COLOR"  default:"#BF9FE9C1"`
	OnTopFareInActiveBackgroundColor string `envconfig:"HEATMAP_ON_TOP_FARE_INACTIVE_BACKGROUND_COLOR"  default:"#BFB6B6B6"`
	HeatmapOnTopEnabled              bool   `envconfig:"HEATMAP_ON_TOP_ENABLED" default:"true"`
	HeatmapEnabled                   bool   `envconfig:"HEATMAP_ENABLED" default:"true"`
	MatchingRate                     MatchingRateConfig
	OnTopFareLabelFormat             string `envconfig:"HEATMAP_ON_TOP_LABEL_FORMAT" default:"เพิ่ม %s บาทจากค่าส่ง"`
}
type MatchingRateConfig struct {
	FirstTierColor         string  `envconfig:"HEATMAP_MATCHING_RATE_FIRST_TIER_COLOR"  default:"#BFEA3C24"`
	SecondTierColor        string  `envconfig:"HEATMAP_MATCHING_RATE_SECOND_TIER_COLOR" default:"#BFF17051"`
	ThirdTierColor         string  `envconfig:"HEATMAP_MATCHING_RATE_THIRD_TIER_COLOR" default:"#99F38A68"`
	FourthTierColor        string  `envconfig:"HEATMAP_MATCHING_RATE_FOURTH_TIER_COLOR" default:"#80F8AD87"`
	FirstTierScorePercent  float64 `envconfig:"HEATMAP_MATCHING_RATE_FIRST_SCORE_PERCENT"  default:"80"`
	SecondTierScorePercent float64 `envconfig:"HEATMAP_MATCHING_RATE_SECOND_SCORE_PERCENT"  default:"60"`
	ThirdTierScorePercent  float64 `envconfig:"HEATMAP_MATCHING_RATE_THIRD_SCORE_PERCENT"  default:"40"`
	FourthTierScorePercent float64 `envconfig:"HEATMAP_MATCHING_RATE_FOURTH_SCORE_PERCENT"  default:"20"`
	Cron                   string  `envconfig:"HEATMAP_MATCHING_RATE_CRON" default:"@every 5m"`
	Resolution             int     `envconfig:"HEATMAP_MATCHING_RATE_RESOLUTION" default:"7"`
	UnmatchedMinOrder      int     `envconfig:"HEATMAP_MATCHING_RATE_UNMATCHED_MIN_ORDER" default:"0"`
	ParentResolution       int     `envconfig:"HEATMAP_MATCHING_RATE_PARENT_RESOLUTION" default:"6"`
	TimeSlotInMinute       int     `envconfig:"HEAT_MAP_MATCHING_RATE_TIME_SLOT_IN_MINUTE" default:"-10"`
	MaxDistance            int     `envconfig:"HEAT_MAP_MATCHING_MAX_DISTANCE" default:"30000"`
}

func ProvideHeatMapConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
