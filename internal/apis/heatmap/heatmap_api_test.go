package heatmap_test

import (
	"errors"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"testing"
	"time"

	//"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/heatmapdemand/mock_heatmapdemand"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon/mock_polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func assertUsingDemandHeatmap(tt *testing.T, actual heatmap.Res) {
	require.Equal(tt, "h3id1", actual.Data[0].H3Id)
	require.Equal(tt, "h3id5", actual.Data[4].H3Id)
	require.Equal(tt, "FirstTierColor", actual.Data[0].Color)
	require.Equal(tt, "SecondTierColor", actual.Data[1].Color)
	require.Equal(tt, "ThirdTierColor", actual.Data[2].Color)
	require.Equal(tt, "FourthTierColor", actual.Data[3].Color)
	require.Equal(tt, "", actual.Data[4].Color)
}

func TestHeatMapApi_Get(t *testing.T) {

	t.Run("Should return status code 200 and heatmap data", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.HeatMap{
			{H3ID: "h3id1", Tier: 1, Region: "AYUTTHAYA"},
			{H3ID: "h3id2", Tier: 2, Region: "AYUTTHAYA"},
			{H3ID: "h3id3", Tier: 3, Region: "AYUTTHAYA"},
			{H3ID: "h3id4", Tier: 4, Region: "AYUTTHAYA"},
			{H3ID: "h3id5", Tier: 5, Region: "AYUTTHAYA"},
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		area := model.ServiceArea{}
		area.SetHeatMapType(model.DemandHm)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&area, nil)

		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)

		onTopFare := []model.OnTopFare{{
			ID:     "id",
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: "00:00",
							End:   "23:59",
						},
					},
					FlatRateAmount: 10,
				},
			},
			Region: "AYUTTHAYA",
		},
			{
				ID:     "id2",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Time: []model.StartEndTime{
							{
								Begin: "00:00",
								End:   "23:59",
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region: "AYUTTHAYA",
			}}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 5, len(actual.Data))
		require.Equal(tt, 2, len(actual.OnTopFare))
		assertUsingDemandHeatmap(tt, actual)
	})

	t.Run("Should return status code 200 and heatmap data empty when heat map disabled", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)

		api.Cfg.HeatmapOnTopEnabled = false
		api.Cfg.HeatmapEnabled = false
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, 0, len(actual.Data))
		require.Equal(tt, 0, len(actual.OnTopFare))
	})

	t.Run("Should return status code 200 and heatmap data on top in actived", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.HeatMap{
			{H3ID: "h3id1", Tier: 1, Region: "AYUTTHAYA"},
			{H3ID: "h3id2", Tier: 2, Region: "AYUTTHAYA"},
			{H3ID: "h3id3", Tier: 3, Region: "AYUTTHAYA"},
			{H3ID: "h3id4", Tier: 4, Region: "AYUTTHAYA"},
			{H3ID: "h3id5", Tier: 5, Region: "AYUTTHAYA"},
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&model.ServiceArea{}, nil)
		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		start, end := timeutil.BangkokNow().Add(-40).Format("15:04:05"), timeutil.BangkokNow().Add(-20).Format("15:04:05")
		onTopFare := []model.OnTopFare{{
			ID:     "id",
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: start,
							End:   end,
						},
					},
					FlatRateAmount: 10,
				},
			},
			Region: "AYUTTHAYA",
		}}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 5, len(actual.Data))
		require.Equal(tt, 1, len(actual.OnTopFare))
		assertUsingDemandHeatmap(tt, actual)
	})

	t.Run("Should return status code 200 and heatmap data on top in actived now", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.HeatMap{
			{H3ID: "h3id1", Tier: 1, Region: "AYUTTHAYA"},
			{H3ID: "h3id2", Tier: 2, Region: "AYUTTHAYA"},
			{H3ID: "h3id3", Tier: 3, Region: "AYUTTHAYA"},
			{H3ID: "h3id4", Tier: 4, Region: "AYUTTHAYA"},
			{H3ID: "h3id5", Tier: 5, Region: "AYUTTHAYA"},
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&model.ServiceArea{}, nil)
		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		start, end := timeutil.BangkokNow().Add(-40).Format("15:04:05"), timeutil.BangkokNow().Add(20).Format("15:04:05")
		dayUpper := strings.ToUpper(timeutil.BangkokNow().Add(24 * time.Hour).Format("Mon"))
		otDay := model.Days(dayUpper)
		onTopFare := []model.OnTopFare{
			{
				ID:     "id-1",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{otDay},
						Time: []model.StartEndTime{
							{
								Begin: start,
								End:   end,
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region: "AYUTTHAYA",
			},
			{
				ID:     "id-2",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{otDay},
						Time: []model.StartEndTime{
							{
								Begin: start,
								End:   end,
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region:           "AYUTTHAYA",
				EnableActiveTime: true,
				StartTime:        timeutil.BangkokNow().Add(-1 * time.Hour),
				EndTime:          timeutil.BangkokNow().Add(1 * time.Hour),
			},
			{
				ID:     "id-2",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{otDay},
						Time: []model.StartEndTime{
							{
								Begin: start,
								End:   end,
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region:           "AYUTTHAYA",
				EnableActiveTime: true,
				StartTime:        timeutil.BangkokNow().Add(-2 * time.Hour),
				EndTime:          timeutil.BangkokNow().Add(-1 * time.Hour),
			},
		}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 5, len(actual.Data))
		require.Equal(tt, 2, len(actual.OnTopFare))
		assertUsingDemandHeatmap(tt, actual)
		require.Equal(tt, false, actual.OnTopFare[0].IsActived)
	})

	t.Run("Should return status code 200 and on top empty when get on top error ", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.HeatMap{
			{H3ID: "h3id1", Tier: 1, Region: "AYUTTHAYA"},
			{H3ID: "h3id2", Tier: 2, Region: "AYUTTHAYA"},
			{H3ID: "h3id3", Tier: 3, Region: "AYUTTHAYA"},
			{H3ID: "h3id4", Tier: 4, Region: "AYUTTHAYA"},
			{H3ID: "h3id5", Tier: 5, Region: "AYUTTHAYA"},
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&model.ServiceArea{}, nil)
		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 5, len(actual.Data))
		require.Equal(tt, 0, len(actual.OnTopFare))
		assertUsingDemandHeatmap(tt, actual)
	})

	t.Run("Should return status code 200 with matched heatmap by times", func(tt *testing.T) {
		dateTime := time.Date(2023, 4, 26, 14, 0, 0, 0, timeutil.BangkokLocation())
		timeutils.FreezeWithTime(dateTime.Unix() * 1000)
		defer timeutils.Unfreeze()

		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.HeatMap{
			{H3ID: "h3id1", Tier: 1, Region: "AYUTTHAYA"},
			{H3ID: "h3id2", Tier: 2, Region: "AYUTTHAYA"},
			{H3ID: "h3id3", Tier: 3, Region: "AYUTTHAYA"},
			{H3ID: "h3id4", Tier: 4, Region: "AYUTTHAYA"},
			{H3ID: "h3id5", Tier: 5, Region: "AYUTTHAYA"},
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		area := model.ServiceArea{}
		area.SetHeatMapType(model.MatchingRateHm)
		area.SetHeatMapByTimes([]model.HeatMapByTime{
			{
				StartTime:   "12:00",
				EndTime:     "16:00",
				HeatmapType: model.DemandHm,
			},
		})
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&area, nil)

		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		onTopFare := []model.OnTopFare{{
			ID:     "id",
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: "00:00",
							End:   "23:59",
						},
					},
					FlatRateAmount: 10,
				},
			},
			Region: "AYUTTHAYA",
		},
			{
				ID:     "id2",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Time: []model.StartEndTime{
							{
								Begin: "00:00",
								End:   "23:59",
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region: "AYUTTHAYA",
			}}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 5, len(actual.Data))
		require.Equal(tt, 2, len(actual.OnTopFare))
		assertUsingDemandHeatmap(tt, actual)
	})

	t.Run("Should return status code 200 with heatmap on svc area when not matched by times", func(tt *testing.T) {
		dateTime := time.Date(2023, 4, 26, 14, 0, 0, 0, timeutil.BangkokLocation())
		timeutils.FreezeWithTime(dateTime.Unix() * 1000)
		defer timeutils.Unfreeze()

		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.HeatMap{
			{H3ID: "h3id1", Tier: 1, Region: "AYUTTHAYA"},
			{H3ID: "h3id2", Tier: 2, Region: "AYUTTHAYA"},
			{H3ID: "h3id3", Tier: 3, Region: "AYUTTHAYA"},
			{H3ID: "h3id4", Tier: 4, Region: "AYUTTHAYA"},
			{H3ID: "h3id5", Tier: 5, Region: "AYUTTHAYA"},
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		area := model.ServiceArea{}
		area.SetHeatMapType(model.DemandHm)
		area.SetHeatMapByTimes([]model.HeatMapByTime{
			{
				StartTime:   "10:00",
				EndTime:     "12:00",
				HeatmapType: model.MatchingRateHm,
			},
		})
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&area, nil)

		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		onTopFare := []model.OnTopFare{{
			ID:     "id",
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: "00:00",
							End:   "23:59",
						},
					},
					FlatRateAmount: 10,
				},
			},
			Region: "AYUTTHAYA",
		},
			{
				ID:     "id2",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Time: []model.StartEndTime{
							{
								Begin: "00:00",
								End:   "23:59",
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region: "AYUTTHAYA",
			}}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 5, len(actual.Data))
		require.Equal(tt, 2, len(actual.OnTopFare))
		assertUsingDemandHeatmap(tt, actual)
	})

	t.Run("Should not return tier 0", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.HeatMap{
			{H3ID: "h3id0", Tier: 0, Region: "AYUTTHAYA"},
			{H3ID: "h3id1", Tier: 1, Region: "AYUTTHAYA"},
			{H3ID: "h3id2", Tier: 2, Region: "AYUTTHAYA"},
			{H3ID: "h3id3", Tier: 3, Region: "AYUTTHAYA"},
			{H3ID: "h3id4", Tier: 4, Region: "AYUTTHAYA"},
			{H3ID: "h3id5", Tier: 5, Region: "AYUTTHAYA"},
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&model.ServiceArea{}, nil)

		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		driver.SetDriverIDToContext(ctx, "driverId")
		tmr := timeutil.BangkokNow().Add(24 * time.Hour)
		nowMinus1 := timeutil.BangkokNow().Add(-1 * time.Hour)
		nowMinus2 := timeutil.BangkokNow().Add(-2 * time.Hour)
		onTopFare := []model.OnTopFare{
			{
				ID:     "id",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Days(strings.ToUpper(tmr.Format("Mon"))),
						},
						Time: []model.StartEndTime{
							{
								Begin: nowMinus1.Format("15:04"),
								End:   nowMinus2.Format("15:04"),
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region: "AYUTTHAYA",
			},
		}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 5

		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 0, len(actual.OnTopFare))
		require.Equal(tt, 5, len(actual.Data))
		assertUsingDemandHeatmap(tt, actual)
	})

	t.Run("Should return empty list when driver not found", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(nil, errors.New("DRIVER NOT FOUND"))

		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 0, len(actual.Data))
	})

	t.Run("Should return status database error when error occurred during query database", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, _ := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&weekday=1&lat=13.756331&lng=100.501762",
		}

		deps.repo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(nil, errors.New("database error"))
		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}
		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		driver.SetDriverIDToContext(ctx, "driverId")

		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{}, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return([]model.OnTopFare{}, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
		api.Get(ctx)

		require.Equal(tt, "database error", ctx.Errors[0].Error())
	})

	t.Run("Should return status code 400 when lat query param is missing", func(tt *testing.T) {
		api, _, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lng=100.501762",
		}

		api.Get(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return status code 400 when lng query param is missing", func(tt *testing.T) {
		api, _, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331",
		}

		api.Get(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return status code 200 and matching rate heatmap data", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		expectedData := []model.MatchingRateHeatMap{
			{H3Index: "h3id1", UnmatchedScorePercent: 90, Region: "AYUTTHAYA", UnmatchedOrder: 1000},
			{H3Index: "h3id2", UnmatchedScorePercent: 65, Region: "AYUTTHAYA", UnmatchedOrder: 800},
			{H3Index: "h3id3", UnmatchedScorePercent: 55, Region: "AYUTTHAYA", UnmatchedOrder: 499},
			{H3Index: "h3id4", UnmatchedScorePercent: 30, Region: "AYUTTHAYA", UnmatchedOrder: 377},
		}

		deps.matchingRateHeatmapRepo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(expectedData, nil)
		area := model.ServiceArea{}
		area.SetHeatMapType(model.MatchingRateHm)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&area, nil)

		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		onTopFare := []model.OnTopFare{
			{
				ID:     "id",
				Scheme: model.FlatRateScheme,
				Name:   "เพิ่ม 10 บาทจากค่าส่ง",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Time: []model.StartEndTime{
							{
								Begin: "00:00",
								End:   "23:59",
							},
						},
						FlatRateAmount: 10,
						ServiceTypes:   []model.Service{model.ServiceFood, model.ServiceMart},
					},
				},
				Region: "AYUTTHAYA",
			},
			{
				ID:     "id2",
				Scheme: model.FlatRateScheme,
				Name:   "เพิ่มค่าส่งให้อีก 10 บาท",
				Label:  "เพิ่มค่าส่งให้อีก 10 บาท",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Time: []model.StartEndTime{
							{
								Begin: "00:00",
								End:   "23:59",
							},
						},
						FlatRateAmount: 10,
						ServiceTypes:   []model.Service{model.ServiceFood},
					},
				},
				Region: "AYUTTHAYA",
			}}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)

		t.Logf("%+v", actual)

		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		sort.SliceStable(actual.OnTopFare, func(i, j int) bool {
			return actual.OnTopFare[i].Name < actual.OnTopFare[j].Name
		})

		require.Equal(tt, 4, len(actual.Data))
		require.Equal(tt, 2, len(actual.OnTopFare))
		require.Equal(tt, "เพิ่ม 10 บาทจากค่าส่ง", actual.OnTopFare[0].Name)
		require.Equal(tt, model.ServiceFood, actual.OnTopFare[0].ServiceTypes[0])
		require.Equal(tt, model.ServiceMart, actual.OnTopFare[0].ServiceTypes[1])
		require.Equal(tt, "เพิ่มค่าส่งให้อีก 10 บาท", actual.OnTopFare[1].Name)
		require.Equal(tt, model.ServiceFood, actual.OnTopFare[1].ServiceTypes[0])
		require.Equal(tt, "h3id1", actual.Data[0].H3Id)
		require.Equal(tt, "h3id4", actual.Data[3].H3Id)
		require.Equal(tt, "FirstTierColor", actual.Data[0].Color)
		require.Equal(tt, "SecondTierColor", actual.Data[1].Color)
		require.Equal(tt, "ThirdTierColor", actual.Data[2].Color)
		require.Equal(tt, "FourthTierColor", actual.Data[3].Color)
	})

	t.Run("Should return status code 200 and matching rate heatmap empty when get heatmap from db error", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		ctx, recorder := testutil.TestRequestContext("GET", "v1/driver/heatmap", nil)
		ctx.Request.URL = &url.URL{
			RawQuery: "hour=1&distanceInMeters=1000&weekday=1&lat=13.756331&lng=100.501762",
		}

		deps.matchingRateHeatmapRepo.EXPECT().
			Find(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(nil, errors.New("error"))
		area := model.ServiceArea{}
		area.SetHeatMapType(model.MatchingRateHm)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).
			Return(&area, nil)

		d := model.Driver{
			DriverID: "driverId",
			Region:   "AYUTTHAYA",
		}

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), "driverId", repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&d, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), string(d.Region)).Return(polygon.GetRawRegionRes{
			Region: "AYUTTHAYA",
			Geometry: polygon.Geometry{
				Type: "Polygon",
				Coordinates: [][]geom.Coord{
					{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
					{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
				},
			},
		}, nil)
		onTopFare := []model.OnTopFare{
			{
				ID:     "id",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Time: []model.StartEndTime{
							{
								Begin: "00:00",
								End:   "23:59",
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region: "AYUTTHAYA",
			},
			{
				ID:     "id2",
				Scheme: model.FlatRateScheme,
				Name:   "fakeOnTop",
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Time: []model.StartEndTime{
							{
								Begin: "00:00",
								End:   "23:59",
							},
						},
						FlatRateAmount: 10,
					},
				},
				Region: "AYUTTHAYA",
			},
		}
		deps.onTopFareRepo.EXPECT().GetOnTopFareHeatMap(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onTopFare, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFarePinHeatMap(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return([]model.OnTopFare{}, nil)
		api.Cfg.OnTopFareNextHours = 24
		driver.SetDriverIDToContext(ctx, "driverId")
		api.Get(ctx)

		var actual heatmap.Res

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		sort.SliceStable(actual.Data, func(i, j int) bool {
			return actual.Data[i].H3Id < actual.Data[j].H3Id
		})
		require.Equal(tt, 0, len(actual.Data))
		require.Equal(tt, 2, len(actual.OnTopFare))
	})

}

func TestHeatMapApi_Calculation(t *testing.T) {
	t.Run("Should calculate heatmap correctly", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		c := model.Condition{
			Conditions: []model.ConditionItem{
				{
					Region: "AYUTTHAYA",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 5}, {Threshold: 4}, {Threshold: 3}, {Threshold: 2}},
				}, {
					Region: "KHONKAEN",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 10}, {Threshold: 8}},
				},
			},
		}

		deps.heatMapDemand.EXPECT().GetHeatmapCondition(gomock.Any(), gomock.Any()).
			Return(c, nil)

		mockHm := []model.HeatMap{
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "10:00", End: "10:10"},
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "09:00", End: "09:10"},
			{H3ID: "8864a408bbfffff", Region: "KHONKAEN", Tier: 1, Total: 50, Start: "09:00", End: "09:10"},
			{H3ID: "8864a4803bffff2", Region: "PATTAYA", Tier: 1, Total: 30, Start: "10:00", End: "10:10"},
		}

		deps.repo.EXPECT().
			FindByH3IDIn(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(mockHm, nil)

		deps.repo.EXPECT().
			UpsertAll(gomock.Any()).
			Return(nil)

		deps.repo.EXPECT().
			DeleteByCreatedAtLte(gomock.Any()).
			Return(nil)

		var mockOrders []model.OrderEventPayload
		oc1 := createCreatedAt(03, 05)
		oc2 := createCreatedAt(03, 11)
		for i := 1; i < 10; i++ {
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.794800,
					Lat: 13.781986,
				},
				CreatedAt: oc1,
				Region:    "AYUTTHAYA",
			})

			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.58211814639135,
					Lat: 14.352162661270864,
				},
				CreatedAt: oc1,
				Region:    "KHONKAEN",
			})
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.58211814639135,
					Lat: 14.352162661270864,
				},
				CreatedAt: oc2,
				Region:    "KHONKAEN",
			})
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.5823705,
					Lat: 13.7228842,
				},
				CreatedAt: oc1,
				Region:    "BKK",
			})
		}

		heatmaps, err := api.CalculateHeatMap(mockOrders)
		require.Len(tt, heatmaps, 2)
		require.NoError(tt, err)
		for _, hm := range heatmaps {
			require.Equal(t, "10:00", hm.Start)
			require.Equal(t, "10:10", hm.End)
			if hm.Region == "AYUTTHAYA" {
				require.Equal(t, "8864a4803bfffff", hm.H3ID)
				require.Equal(t, 18, hm.Total)
				require.Equal(t, 1, hm.Tier)
			} else if hm.Region == "KHONKAEN" {
				require.Equal(t, "8864a408bbfffff", hm.H3ID)
				require.Equal(t, 9, hm.Total)
				require.Equal(t, 2, hm.Tier)
			}
		}
	})

	t.Run("Should return err when GetHeatmapCondition error", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		deps.heatMapDemand.EXPECT().GetHeatmapCondition(gomock.Any(), gomock.Any()).
			Return(model.Condition{}, errors.New("error"))

		_, err := api.CalculateHeatMap([]model.OrderEventPayload{})

		require.Error(tt, err)
	})

	t.Run("Should return err when FindByH3IDIn error", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		deps.heatMapDemand.EXPECT().GetHeatmapCondition(gomock.Any(), gomock.Any()).
			Return(model.Condition{}, nil)

		deps.repo.EXPECT().
			FindByH3IDIn(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(nil, errors.New("error"))

		_, err := api.CalculateHeatMap([]model.OrderEventPayload{})

		require.Error(tt, err)
	})

	t.Run("Should return err when UpsertAll error", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		c := model.Condition{
			Conditions: []model.ConditionItem{
				{
					Region: "AYUTTHAYA",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 5}, {Threshold: 4}, {Threshold: 3}, {Threshold: 2}},
				},
			},
		}

		deps.heatMapDemand.EXPECT().GetHeatmapCondition(gomock.Any(), gomock.Any()).
			Return(c, nil)

		mockHm := []model.HeatMap{
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "10:00", End: "10:10"},
		}

		deps.repo.EXPECT().
			FindByH3IDIn(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(mockHm, nil)

		deps.repo.EXPECT().
			UpsertAll(gomock.Any()).
			Return(errors.New("error"))

		_, err := api.CalculateHeatMap([]model.OrderEventPayload{})

		require.Error(tt, err)
	})

	t.Run("Should return err when DeleteByCreatedAtLte error", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		c := model.Condition{
			Conditions: []model.ConditionItem{
				{
					Region: "AYUTTHAYA",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 5}, {Threshold: 4}, {Threshold: 3}, {Threshold: 2}},
				},
			},
		}

		deps.heatMapDemand.EXPECT().GetHeatmapCondition(gomock.Any(), gomock.Any()).
			Return(c, nil)

		mockHm := []model.HeatMap{
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "10:00", End: "10:10"},
		}

		deps.repo.EXPECT().
			FindByH3IDIn(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(mockHm, nil)

		deps.repo.EXPECT().
			UpsertAll(gomock.Any()).
			Return(nil)

		deps.repo.EXPECT().
			DeleteByCreatedAtLte(gomock.Any()).
			Return(errors.New("error"))

		_, err := api.CalculateHeatMap([]model.OrderEventPayload{})

		require.Error(tt, err)
	})

	t.Run("Should calculate heatmap correctly (0 threshold for some tier)", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		c := model.Condition{
			Conditions: []model.ConditionItem{
				{
					Region: "AYUTTHAYA",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 5}, {Threshold: 4}, {Threshold: 3}, {Threshold: 2}},
				}, {
					Region: "KHONKAEN",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 0}, {Threshold: 10}, {Threshold: 8}},
				},
			},
		}

		deps.heatMapDemand.EXPECT().GetHeatmapCondition(gomock.Any(), gomock.Any()).
			Return(c, nil)

		mockHm := []model.HeatMap{
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "10:00", End: "10:10"},
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "09:00", End: "09:10"},
			{H3ID: "8864a408bbfffff", Region: "KHONKAEN", Tier: 1, Total: 50, Start: "09:00", End: "09:10"},
			{H3ID: "8864a4803bffff2", Region: "PATTAYA", Tier: 1, Total: 30, Start: "10:00", End: "10:10"},
		}

		deps.repo.EXPECT().
			FindByH3IDIn(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(mockHm, nil)

		deps.repo.EXPECT().
			UpsertAll(gomock.Any()).
			Return(nil)

		deps.repo.EXPECT().
			DeleteByCreatedAtLte(gomock.Any()).
			Return(nil)

		var mockOrders []model.OrderEventPayload
		oc1 := createCreatedAt(03, 05)
		oc2 := createCreatedAt(03, 11)
		for i := 1; i < 10; i++ {
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.794800,
					Lat: 13.781986,
				},
				CreatedAt: oc1,
				Region:    "AYUTTHAYA",
			})

			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.58211814639135,
					Lat: 14.352162661270864,
				},
				CreatedAt: oc1,
				Region:    "KHONKAEN",
			})
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.58211814639135,
					Lat: 14.352162661270864,
				},
				CreatedAt: oc2,
				Region:    "KHONKAEN",
			})
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.5823705,
					Lat: 13.7228842,
				},
				CreatedAt: oc1,
				Region:    "BKK",
			})
		}

		heatmaps, err := api.CalculateHeatMap(mockOrders)
		require.Len(tt, heatmaps, 2)
		require.NoError(tt, err)
		for _, hm := range heatmaps {
			require.Equal(t, "10:00", hm.Start)
			require.Equal(t, "10:10", hm.End)
			if hm.Region == "AYUTTHAYA" {
				require.Equal(t, "8864a4803bfffff", hm.H3ID)
				require.Equal(t, 18, hm.Total)
				require.Equal(t, 1, hm.Tier)
			} else if hm.Region == "KHONKAEN" {
				require.Equal(t, "8864a408bbfffff", hm.H3ID)
				require.Equal(t, 9, hm.Total)
				require.Equal(t, 3, hm.Tier)
			}
		}
	})

	t.Run("Should calculate heatmap correctly (0 threshold)", func(tt *testing.T) {
		api, deps, finish := newHeatMapAPI(tt)
		defer finish()

		c := model.Condition{
			Conditions: []model.ConditionItem{
				{
					Region: "AYUTTHAYA",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 5}, {Threshold: 4}, {Threshold: 3}, {Threshold: 2}},
				}, {
					Region: "KHONKAEN",
					Start:  "10:00",
					End:    "10:10",
					Tiers:  []model.Threshold{{Threshold: 0}, {Threshold: 0}, {Threshold: 0}, {Threshold: 0}},
				},
			},
		}

		deps.heatMapDemand.EXPECT().GetHeatmapCondition(gomock.Any(), gomock.Any()).
			Return(c, nil)

		mockHm := []model.HeatMap{
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "10:00", End: "10:10"},
			{H3ID: "8864a4803bfffff", Region: "AYUTTHAYA", Tier: 1, Total: 9, Start: "09:00", End: "09:10"},
			{H3ID: "8864a408bbfffff", Region: "KHONKAEN", Tier: 1, Total: 50, Start: "09:00", End: "09:10"},
			{H3ID: "8864a4803bffff2", Region: "PATTAYA", Tier: 1, Total: 30, Start: "10:00", End: "10:10"},
		}

		deps.repo.EXPECT().
			FindByH3IDIn(gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(mockHm, nil)

		deps.repo.EXPECT().
			UpsertAll(gomock.Any()).
			Return(nil)

		deps.repo.EXPECT().
			DeleteByCreatedAtLte(gomock.Any()).
			Return(nil)

		var mockOrders []model.OrderEventPayload
		oc1 := createCreatedAt(03, 05)
		oc2 := createCreatedAt(03, 11)
		for i := 1; i < 10; i++ {
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.794800,
					Lat: 13.781986,
				},
				CreatedAt: oc1,
				Region:    "AYUTTHAYA",
			})

			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.58211814639135,
					Lat: 14.352162661270864,
				},
				CreatedAt: oc1,
				Region:    "KHONKAEN",
			})
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.58211814639135,
					Lat: 14.352162661270864,
				},
				CreatedAt: oc2,
				Region:    "KHONKAEN",
			})
			mockOrders = append(mockOrders, model.OrderEventPayload{
				Location: model.Location{
					Lng: 100.5823705,
					Lat: 13.7228842,
				},
				CreatedAt: oc1,
				Region:    "BKK",
			})
		}

		heatmaps, err := api.CalculateHeatMap(mockOrders)
		require.Len(tt, heatmaps, 2)
		require.NoError(tt, err)
		for _, hm := range heatmaps {
			require.Equal(t, "10:00", hm.Start)
			require.Equal(t, "10:10", hm.End)
			if hm.Region == "AYUTTHAYA" {
				require.Equal(t, "8864a4803bfffff", hm.H3ID)
				require.Equal(t, 18, hm.Total)
				require.Equal(t, 1, hm.Tier)
			} else if hm.Region == "KHONKAEN" {
				require.Equal(t, "8864a408bbfffff", hm.H3ID)
				require.Equal(t, 9, hm.Total)
				require.Equal(t, 0, hm.Tier)
			}
		}
	})
}

func createCreatedAt(hour int, minute int) time.Time {
	utc, _ := time.LoadLocation("UTC")
	createdAt := time.Date(2020, 12, 15, hour, minute, 00, 0, utc)
	return createdAt
}

type heatmapAPIDeps struct {
	repo                    *mock_repository.MockHeatMapRepository
	heatMapDemand           *mock_heatmapdemand.MockHeatMapDemand
	driverRepo              *mock_repository.MockDriverRepository
	onTopFareRepo           *mock_repository.MockOnTopFareRepository
	orderRepo               *mock_repository.MockOrderRepository
	matchingRateHeatmapRepo *mock_repository.MockMatchingRateHeatMapRepository
	serviceAreaRepo         *mock_repository.MockServiceAreaRepository
	zoneRepo                *mock_repository.MockZoneRepository
	polygonService          *mock_polygon.MockPolygon
}

func newHeatMapAPI(r gomock.TestReporter) (*heatmap.API, heatmapAPIDeps, func()) {
	ctrl := gomock.NewController(r)

	deps := heatmapAPIDeps{
		repo:                    mock_repository.NewMockHeatMapRepository(ctrl),
		heatMapDemand:           mock_heatmapdemand.NewMockHeatMapDemand(ctrl),
		driverRepo:              mock_repository.NewMockDriverRepository(ctrl),
		onTopFareRepo:           mock_repository.NewMockOnTopFareRepository(ctrl),
		orderRepo:               mock_repository.NewMockOrderRepository(ctrl),
		matchingRateHeatmapRepo: mock_repository.NewMockMatchingRateHeatMapRepository(ctrl),
		serviceAreaRepo:         mock_repository.NewMockServiceAreaRepository(ctrl),
		zoneRepo:                mock_repository.NewMockZoneRepository(ctrl),
		polygonService:          mock_polygon.NewMockPolygon(ctrl),
	}

	mrConfig := heatmap.MatchingRateConfig{
		FirstTierColor:         "FirstTierColor",
		SecondTierColor:        "SecondTierColor",
		ThirdTierColor:         "ThirdTierColor",
		FourthTierColor:        "FourthTierColor",
		FirstTierScorePercent:  80,
		SecondTierScorePercent: 60,
		ThirdTierScorePercent:  40,
		FourthTierScorePercent: 20,
	}
	return heatmap.ProvideHeatMapAPI(
			deps.repo,
			heatmap.Config{
				FirstTierColor:       "FirstTierColor",
				SecondTierColor:      "SecondTierColor",
				ThirdTierColor:       "ThirdTierColor",
				FourthTierColor:      "FourthTierColor",
				MaxDistance:          5000,
				Resolution:           8,
				HeatmapEnabled:       true,
				HeatmapOnTopEnabled:  true,
				MatchingRate:         mrConfig,
				OnTopFareLabelFormat: "เพิ่ม %s บาทจากค่าส่ง",
			},
			deps.heatMapDemand,
			deps.driverRepo,
			deps.onTopFareRepo,
			deps.orderRepo,
			deps.matchingRateHeatmapRepo,
			deps.serviceAreaRepo,
			deps.zoneRepo,
			deps.polygonService,
		),
		deps,
		func() { ctrl.Finish() }
}
