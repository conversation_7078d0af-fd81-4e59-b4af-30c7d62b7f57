//go:build integration_test
// +build integration_test

package heatmap_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

const heatmapTimeFormat = "15:04"

func TestHeatMapApi_Get_HeatMap(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		require.NoError(t, datastore.EnsureIndexForTest(ctn.DBConnectionForTest))
		drv, err := ctn.DriverRepository.FindDriverID(context.Background(), "DRV_PATTAYA_ONLINE")
		require.NoError(t, err)

		hm := model.HeatMap{
			Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
			End:        timeutil.BangkokNow().Add(-5 * time.Minute).Format(heatmapTimeFormat),
			Total:      100,
			H3ID:       "8864a47259fffff",
			Location:   model.NewHeatMapPoint(100.877083, 12.927608),
			Tier:       1,
			CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			Region:     "PATTAYA",
			Thresholds: []model.Threshold{},
		}

		hm2 := model.HeatMap{
			Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
			End:        timeutil.BangkokNow().Add(-5 * time.Minute).Format(heatmapTimeFormat),
			Total:      100,
			H3ID:       "8864a47254fffff",
			Location:   model.NewHeatMapPoint(100.877609, 12.928769),
			Tier:       2,
			CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			Region:     "PATTAYA",
			Thresholds: []model.Threshold{},
		}

		hm3 := model.HeatMap{
			Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
			End:        timeutil.BangkokNow().Add(-5 * time.Minute).Format(heatmapTimeFormat),
			Total:      100,
			H3ID:       "8864a47259fffff",
			Location:   model.NewHeatMapPoint(100.877083, 12.927608),
			Tier:       1,
			CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			Region:     "AYUTTHAYA",
			Thresholds: []model.Threshold{},
		}

		ctn.HeatMapRepository.UpsertAll([]model.HeatMap{hm, hm2, hm3})
		id := utils.GenerateUUID()
		onTop := model.OnTopFare{
			ID:     id,
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: timeutil.BangkokNow().Add(3 * time.Minute).Format("15:04"),
							End:   timeutil.BangkokNow().Add(4 * time.Minute).Format("15:04"),
						},
					},
					FlatRateAmount: 10,
				},
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: timeutil.BangkokNow().Add(-2 * time.Minute).Format("15:04"),
							End:   timeutil.BangkokNow().Add(2 * time.Minute).Format("15:04"),
						},
						{
							Begin: timeutil.BangkokNow().Add(5 * time.Minute).Format("15:04"),
							End:   timeutil.BangkokNow().Add(6 * time.Minute).Format("15:04"),
						},
					},
					FlatRateAmount: 25,
				},
			},
			Region: "PATTAYA",
			Geometry: model.OntopGeometry{
				Type: "MultiPolygon",
				Coordinates: model.OntopCoordinates{{{
					{100.57537078857422, 14.421380422198462},
					{100.46585083007812, 14.30530611750917},
					{100.69381713867186, 14.304640761934547},
					{100.57537078857422, 14.421380422198462},
				}}},
			},
		}
		err = ctn.OnTopFareRepository.Create(context.Background(), onTop)
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/driver/heatmap?distanceInMeters=10000&lat=12.927608&lng=100.877083&onTopFareDistanceInMeters=200000")
		gctx.Authorized(ctn.RedisTokenStore, drv.DriverID)

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var resp heatmap.Res
		gctx.DecodeJSONResponse(&resp)
		assert.Equal(t, 2, len(resp.Data))
		assert.Equal(t, 1, len(resp.OnTopFare))
		assert.Equal(t, "เพิ่ม 25 บาทจากค่าส่ง", resp.OnTopFare[0].Name)

		require.NoError(t, err)
	})

	t.Run("ontop pin heatmap", func(t *testing.T) {
		testcases := []struct {
			name      string
			startTime time.Time
			endTime   time.Time
			found     bool
		}{
			{
				name:      "filter out expired scheme",
				startTime: time.Date(2024, 8, 21, 14, 0, 0, 0, timeutil.BangkokLocation()),
				endTime:   time.Date(2024, 8, 23, 18, 0, 0, 0, timeutil.BangkokLocation()),
				found:     false,
			},
			{
				name:      "filter out upcoming scheme",
				startTime: time.Date(2024, 8, 25, 14, 0, 0, 0, timeutil.BangkokLocation()),
				endTime:   time.Date(2024, 8, 27, 18, 0, 0, 0, timeutil.BangkokLocation()),
				found:     false,
			},
			{
				name:      "found ongoing scheme",
				startTime: time.Date(2024, 8, 23, 14, 0, 0, 0, timeutil.BangkokLocation()),
				endTime:   time.Date(2024, 8, 25, 18, 0, 0, 0, timeutil.BangkokLocation()),
				found:     true,
			},
		}

		for _, tc := range testcases {
			t.Run(tc.name, func(t *testing.T) {
				date := time.Date(2024, 8, 24, 16, 0, 0, 0, timeutil.BangkokLocation())
				timeutils.FreezeWithTime(date.Unix() * 1000)
				defer timeutils.Unfreeze()

				ctn := ittest.NewContainer(t)
				require.NoError(t, datastore.EnsureIndexForTest(ctn.DBConnectionForTest))
				drv, err := ctn.DriverRepository.FindDriverID(context.Background(), "DRV_PATTAYA_ONLINE")
				require.NoError(t, err)

				id := utils.GenerateUUID()
				onTop := model.OnTopFare{
					ID:     id,
					Scheme: model.FlatRateScheme,
					Name:   "expiredOntop",
					Status: model.StatusActive,
					Conditions: []model.OntopCondition{
						{
							Status: model.StatusActive,
							Days: []model.Days{
								model.Monday,
								model.Tuesday,
								model.Wednesday,
								model.Thursday,
								model.Friday,
								model.Saturday,
								model.Sunday,
							},
							Time: []model.StartEndTime{
								{
									Begin: timeutil.BangkokNow().Add(3 * time.Minute).Format("15:04"),
									End:   timeutil.BangkokNow().Add(4 * time.Minute).Format("15:04"),
								},
							},
							FlatRateAmount: 10,
						},
					},
					Region: "PATTAYA",
					Restaurants: []model.OnTopRestaurant{
						{
							RestaurantId:   "id",
							RestaurantName: "ครััวคุณต๋อย",
							Loc: model.OntopPoint{
								Type:        "Point",
								Coordinates: []float64{100.54, 13.74},
							},
						},
					},
					EnableActiveTime: true,
					StartTime:        tc.startTime,
					EndTime:          tc.endTime,
				}

				err = ctn.OnTopFareRepository.Create(context.Background(), onTop)
				gctx := testutil.NewContextWithRecorder()
				gctx.SetGET("/v1/driver/heatmap?distanceInMeters=10000&lat=12.927608&lng=100.877083&onTopFareDistanceInMeters=200000")
				gctx.Authorized(ctn.RedisTokenStore, drv.DriverID)

				ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

				gctx.AssertResponseCode(t, 200)
				var resp heatmap.Res
				gctx.DecodeJSONResponse(&resp)
				if tc.found {
					assert.Equal(t, 1, len(resp.OnTopFare))
				} else {
					assert.Equal(t, 0, len(resp.OnTopFare))
				}
				require.NoError(t, err)
			})
		}
	})

	t.Run("ontop zone heatmap success", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		require.NoError(t, datastore.EnsureIndexForTest(ctn.DBConnectionForTest))
		require.NoError(t, ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_heatmap"))

		drv, err := ctn.DriverRepository.FindDriverID(context.Background(), "DRV_PATTAYA_ONLINE")
		require.NoError(t, err)

		hm := model.HeatMap{
			Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
			End:        timeutil.BangkokNow().Add(-5 * time.Minute).Format(heatmapTimeFormat),
			Total:      100,
			H3ID:       "8864a47259fffff",
			Location:   model.NewHeatMapPoint(100.877083, 12.927608),
			Tier:       1,
			CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			Region:     "PATTAYA",
			Thresholds: []model.Threshold{},
		}

		ctn.HeatMapRepository.UpsertAll([]model.HeatMap{hm})
		id := utils.GenerateUUID()
		onTop := model.OnTopFare{
			ID:     id,
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: timeutil.BangkokNow().Add(3 * time.Minute).Format("15:04"),
							End:   timeutil.BangkokNow().Add(4 * time.Minute).Format("15:04"),
						},
					},
					FlatRateAmount: 10,
				},
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: timeutil.BangkokNow().Add(-2 * time.Minute).Format("15:04"),
							End:   timeutil.BangkokNow().Add(2 * time.Minute).Format("15:04"),
						},
						{
							Begin: timeutil.BangkokNow().Add(5 * time.Minute).Format("15:04"),
							End:   timeutil.BangkokNow().Add(6 * time.Minute).Format("15:04"),
						},
					},
					FlatRateAmount: 25,
				},
			},
			Region:   "PATTAYA",
			ZoneCode: "NAN",
		}
		err = ctn.OnTopFareRepository.Create(context.Background(), onTop)
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/driver/heatmap?distanceInMeters=10000&lat=12.927608&lng=100.877083&onTopFareDistanceInMeters=200000")
		gctx.Authorized(ctn.RedisTokenStore, drv.DriverID)

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var resp heatmap.Res
		gctx.DecodeJSONResponse(&resp)
		assert.Equal(t, 1, len(resp.OnTopFare))

		require.NoError(t, err)
	})

	t.Run("matching rate heatmap success", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		require.NoError(t, datastore.EnsureIndexForTest(ctn.DBConnectionForTest))
		drv, err := ctn.DriverRepository.FindDriverID(context.Background(), "GRAB-001")

		require.NoError(t, err)

		hm := model.MatchingRateHeatMap{
			UnmatchedScorePercent: 99.0,
			UnmatchedOrder:        999,
			H3Index:               "8864a47259fffff",
			Location:              model.NewHeatMapPoint(100.877083, 12.927608),
			Region:                "PATTAYA",
		}

		hm2 := model.MatchingRateHeatMap{
			UnmatchedScorePercent: 86.0,
			UnmatchedOrder:        980,
			H3Index:               "8864a47254fffff",
			Location:              model.NewHeatMapPoint(100.877083, 12.927608),
			Region:                "AYUTTHAYA",
		}

		hm3 := model.MatchingRateHeatMap{
			UnmatchedScorePercent: 35.0,
			UnmatchedOrder:        50,
			H3Index:               "8864a47259fffff",
			Location:              model.NewHeatMapPoint(100.877083, 12.927608),
			Region:                "AYUTTHAYA",
		}

		ctn.MatchingRateHeatMapRepository.SaveAll([]model.MatchingRateHeatMap{hm, hm2, hm3})
		id := utils.GenerateUUID()
		onTop := model.OnTopFare{
			ID:     id,
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: timeutil.BangkokNow().Add(-3 * time.Hour).Format("15:04"),
							End:   timeutil.BangkokNow().Add(3 * time.Hour).Format("15:04"),
						},
						{
							Begin: timeutil.BangkokNow().Add(4 * time.Hour).Format("15:04"),
							End:   timeutil.BangkokNow().Add(5 * time.Hour).Format("15:04"),
						},
						{
							Begin: timeutil.BangkokNow().Add(6 * time.Hour).Format("15:04"),
							End:   timeutil.BangkokNow().Add(7 * time.Hour).Format("15:04"),
						},
					},
					FlatRateAmount: 10,
				},
			},
			Region: "AYUTTHAYA",
			Geometry: model.OntopGeometry{
				Type: "MultiPolygon",
				Coordinates: model.OntopCoordinates{{{
					{100.57537078857422, 14.421380422198462},
					{100.46585083007812, 14.30530611750917},
					{100.69381713867186, 14.304640761934547},
					{100.57537078857422, 14.421380422198462},
				}}},
			},
		}
		err = ctn.OnTopFareRepository.Create(context.Background(), onTop)
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/driver/heatmap?distanceInMeters=10000&lat=12.927608&lng=100.877083&onTopFareDistanceInMeters=200000")
		gctx.Authorized(ctn.RedisTokenStore, drv.DriverID)

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var resp heatmap.Res
		gctx.DecodeJSONResponse(&resp)
		assert.Equal(t, 2, len(resp.Data))
		assert.Equal(t, 1, resp.Data[0].Tier)
		assert.Equal(t, 4, resp.Data[1].Tier)
		assert.Equal(t, 1, len(resp.OnTopFare))

		require.NoError(t, err)
	})

	t.Run("success. driver's region does not matched", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		require.NoError(t, datastore.EnsureIndexForTest(ctn.DBConnectionForTest))

		drv, err := ctn.DriverRepository.FindDriverID(context.Background(), "DRV_PATTAYA_ONLINE")
		require.NoError(t, err)

		hm := model.HeatMap{
			Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
			End:        timeutil.BangkokNow().Add(-10 * time.Minute).Format(heatmapTimeFormat),
			Total:      100,
			H3ID:       "8864a47259fffff",
			Location:   model.NewHeatMapPoint(100.877083, 12.927608),
			Tier:       1,
			CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			Region:     "BKK",
			Thresholds: []model.Threshold{},
		}

		hm2 := model.HeatMap{
			Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
			End:        timeutil.BangkokNow().Add(-10 * time.Minute).Format(heatmapTimeFormat),
			Total:      100,
			H3ID:       "8864a47254fffff",
			Location:   model.NewHeatMapPoint(100.877609, 12.928769),
			Tier:       2,
			CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			Region:     "AYUTTHAYA",
			Thresholds: []model.Threshold{},
		}

		hm3 := model.HeatMap{
			Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
			End:        timeutil.BangkokNow().Add(-10 * time.Minute).Format(heatmapTimeFormat),
			Total:      100,
			H3ID:       "8864a47259fffff",
			Location:   model.NewHeatMapPoint(100.877083, 12.927608),
			Tier:       1,
			CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
			Region:     "AYUTTHAYA",
			Thresholds: []model.Threshold{},
		}

		err = ctn.HeatMapRepository.UpsertAll([]model.HeatMap{hm, hm2, hm3})
		require.NoError(t, err)

		id := utils.GenerateUUID()
		onTop := model.OnTopFare{
			ID:     id,
			Scheme: model.FlatRateScheme,
			Name:   "fakeOnTop",
			Status: model.StatusActive,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days: []model.Days{
						model.Monday,
						model.Tuesday,
						model.Wednesday,
						model.Thursday,
						model.Friday,
						model.Saturday,
						model.Sunday,
					},
					Time: []model.StartEndTime{
						{
							Begin: timeutil.BangkokNow().Add(-1 * time.Hour).Format("15:04"),
							End:   timeutil.BangkokNow().Add(3 * time.Hour).Format("15:04"),
						},
					},
					FlatRateAmount: 10,
				},
			},
			Region: "AYUTTHAYA",
			Geometry: model.OntopGeometry{
				Type: "MultiPolygon",
				Coordinates: model.OntopCoordinates{{{
					{100.57537078857422, 14.421380422198462},
					{100.46585083007812, 14.30530611750917},
					{100.69381713867186, 14.304640761934547},
					{100.57537078857422, 14.421380422198462},
				}}},
			},
		}
		err = ctn.OnTopFareRepository.Create(context.Background(), onTop)

		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/driver/heatmap?distanceInMeters=10000&lat=12.927608&lng=100.877083")
		gctx.Authorized(ctn.RedisTokenStore, drv.DriverID)

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var resp heatmap.Res
		gctx.DecodeJSONResponse(&resp)
		assert.Equal(t, 0, len(resp.Data))
		assert.Equal(t, 0, len(resp.OnTopFare))

		require.NoError(t, err)
	})

	t.Run("Test several different case of remaining and upcoming", func(t *testing.T) {
		timeutils.Now = func() time.Time {
			return time.Date(2022, 2, 17, 20, 40, 30, 0, timeutil.BangkokLocation())
		}

		now := timeutil.BangkokNow()

		ctn := ittest.NewContainer(t)

		setupHeatmapDataInDB(t, ctn, "PATTAYA", "8864a47259fffff", model.NewHeatMapPoint(100.877083, 12.927608))

		testcases := []struct {
			input                     []model.StartEndTime
			expectedRemainingUpcoming string
		}{{
			input: []model.StartEndTime{
				{
					Begin: now.Add(-1 * time.Hour).Format("15:04"),
					End:   now.Add(1 * time.Hour).Format("15:04"),
				},
				{
					Begin: now.Add(4 * time.Hour).Format("15:04"),
					End:   now.Add(6 * time.Hour).Format("15:04"),
				},
			},
			expectedRemainingUpcoming: "เหลือ 59 นาที",
		}, {
			input: []model.StartEndTime{
				{
					Begin: now.Add(1 * time.Hour).Format("15:04"),
					End:   now.Add(2 * time.Hour).Format("15:04"),
				},
				{
					Begin: now.Add(4 * time.Hour).Format("15:04"),
					End:   now.Add(6 * time.Hour).Format("15:04"),
				},
			},
			expectedRemainingUpcoming: fmt.Sprintf("เริ่ม %s - %s", now.Add(1*time.Hour).Format("15:04"), now.Add(2*time.Hour).Format("15:04")),
		}}

		for index, tc := range testcases {
			t.Run(fmt.Sprintf("Case: %d", index+1), func(tt *testing.T) {
				onTop := setupPattayaOntopSchemeWithTimerangeInDB(t, ctn, tc.input)
				resp := getOntopDetail(t, ctn, "?distanceInMeters=10000&lat=14.387610028157496&lng=100.53805230768597&onTopFareDistanceInMeters=1000")

				assert.Equal(t, 1, len(resp.OnTopFare))
				assert.Equal(t, "เพิ่ม 5 บาทจากค่าส่ง", resp.OnTopFare[0].Name)
				require.Contains(t, tc.expectedRemainingUpcoming, resp.OnTopFare[0].RemainingUpcoming)

				// Clean up the ontop repository since we need to run this container again in next the testcase.
				err := ctn.OnTopFareRepository.Delete(context.Background(), onTop)
				require.NoError(t, err)
			})
		}

		timeutils.Now = time.Now
	})

	t.Run("filtered_ontop_zone_heatmap", func(t *testing.T) {
		timeutils.Now = func() time.Time {
			return time.Date(2022, 2, 17, 20, 40, 30, 0, timeutil.BangkokLocation())
		}

		now := timeutil.BangkokNow()
		ctn := ittest.NewContainer(t)

		setupHeatmapDataInDB(t, ctn, "PATTAYA", "8864a47259fffff", model.NewHeatMapPoint(100.877083, 12.927608))

		createOnTopFareFlatRate := func(flatRateAmount float64) model.OnTopFare {
			id := utils.GenerateUUID()
			onTop := model.OnTopFare{
				ID:     id,
				Scheme: model.FlatRateScheme,
				Name:   fmt.Sprintf("fakeOnTop-%s", id),
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						FlatRateAmount: flatRateAmount,
						Time: []model.StartEndTime{
							{
								Begin: now.Add(1 * time.Hour).Format("15:04"),
								End:   now.Add(2 * time.Hour).Format("15:04"),
							},
							{
								Begin: now.Add(4 * time.Hour).Format("15:04"),
								End:   now.Add(6 * time.Hour).Format("15:04"),
							},
						},
					},
				},
				Region: "PATTAYA",
				Geometry: model.OntopGeometry{
					Type: "MultiPolygon",
					Coordinates: model.OntopCoordinates{{{
						{100.57537078857422, 14.421380422198462},
						{100.46585083007812, 14.30530611750917},
						{100.69381713867186, 14.304640761934547},
						{100.57537078857422, 14.421380422198462},
					}}},
				},
			}

			return onTop
		}

		createOnTopFareFlexibleRate := func(normalAmount, bundleAmount types.Money) model.OnTopFare {
			id := utils.GenerateUUID()
			onTop := model.OnTopFare{
				ID:     id,
				Scheme: model.FlexibleFlatRateScheme,
				Name:   fmt.Sprintf("fakeOnTop-%s", id),
				Status: model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						FlatRateAmount: 0,
						FlatRatePrices: []model.FlatRatePrice{{
							FlatRateType: model.FlexibleFlatRate,
							NormalAmount: normalAmount,
							BundleAmount: bundleAmount,
						}},
						Time: []model.StartEndTime{
							{
								Begin: now.Add(1 * time.Hour).Format("15:04"),
								End:   now.Add(2 * time.Hour).Format("15:04"),
							},
							{
								Begin: now.Add(4 * time.Hour).Format("15:04"),
								End:   now.Add(6 * time.Hour).Format("15:04"),
							},
						},
					},
				},
				Region: "PATTAYA",
				Geometry: model.OntopGeometry{
					Type: "MultiPolygon",
					Coordinates: model.OntopCoordinates{{{
						{100.57537078857422, 14.421380422198462},
						{100.46585083007812, 14.30530611750917},
						{100.69381713867186, 14.304640761934547},
						{100.57537078857422, 14.421380422198462},
					}}},
				},
			}

			return onTop
		}

		onTopNormalFlatRate := createOnTopFareFlatRate(5)
		onTopZeroRateFlatRate := createOnTopFareFlatRate(0)

		onTopNormalFlexibleRate := createOnTopFareFlexibleRate(10, 10)
		onTopNormalOnlyRateFlexibleRate := createOnTopFareFlexibleRate(0, 10)
		onTopBundleOnlyRateFlexibleRate := createOnTopFareFlexibleRate(0, 10)
		onTopZeroRateFlexibleRate := createOnTopFareFlexibleRate(0, 0)

		setupOnTopFares := func(onTops []model.OnTopFare) {
			for _, ot := range onTops {
				err := ctn.OnTopFareRepository.Create(context.Background(), ot)
				require.NoError(t, err)
			}
		}

		cleanUpTest := func(onTops []model.OnTopFare) {
			for _, ot := range onTops {
				err := ctn.OnTopFareRepository.Delete(context.Background(), ot)
				require.NoError(t, err)
			}
		}

		testcases := []struct {
			name     string
			input    []model.OnTopFare
			expected []model.OnTopFare
		}{{
			name:     "normal case",
			input:    []model.OnTopFare{onTopNormalFlatRate},
			expected: []model.OnTopFare{onTopNormalFlatRate},
		}, {
			name:     "zero rate case",
			input:    []model.OnTopFare{onTopZeroRateFlatRate},
			expected: []model.OnTopFare{},
		}, {
			name:     "normal flexible case",
			input:    []model.OnTopFare{onTopNormalFlexibleRate},
			expected: []model.OnTopFare{onTopNormalFlexibleRate},
		}, {
			name:     "normal only rate flexible case",
			input:    []model.OnTopFare{onTopNormalOnlyRateFlexibleRate},
			expected: []model.OnTopFare{onTopNormalOnlyRateFlexibleRate},
		}, {
			name:     "bundle only rate flexible case",
			input:    []model.OnTopFare{onTopBundleOnlyRateFlexibleRate},
			expected: []model.OnTopFare{onTopBundleOnlyRateFlexibleRate},
		}, {
			name:     "zero rate flexible case",
			input:    []model.OnTopFare{onTopZeroRateFlexibleRate},
			expected: []model.OnTopFare{},
		}, {
			name:     "combine multiple flat rate on top",
			input:    []model.OnTopFare{onTopNormalFlatRate, onTopZeroRateFlatRate},
			expected: []model.OnTopFare{onTopNormalFlatRate},
		}, {
			name: "combine multiple flexible rate on top",
			input: []model.OnTopFare{
				onTopNormalFlexibleRate,
				onTopNormalOnlyRateFlexibleRate,
				onTopBundleOnlyRateFlexibleRate,
				onTopZeroRateFlexibleRate,
			},
			expected: []model.OnTopFare{
				onTopNormalFlexibleRate,
				onTopNormalOnlyRateFlexibleRate,
				onTopBundleOnlyRateFlexibleRate,
			},
		}, {
			name: "combine multiple flexible rate on top",
			input: []model.OnTopFare{
				onTopNormalFlatRate,
				onTopZeroRateFlatRate,
				onTopNormalFlexibleRate,
				onTopNormalOnlyRateFlexibleRate,
				onTopBundleOnlyRateFlexibleRate,
				onTopZeroRateFlexibleRate,
			},
			expected: []model.OnTopFare{
				onTopNormalFlatRate,
				onTopNormalFlexibleRate,
				onTopNormalOnlyRateFlexibleRate,
				onTopBundleOnlyRateFlexibleRate,
			},
		}}

		for i, tc := range testcases {
			t.Run(fmt.Sprintf("Case: %d %s", i+1, tc.name), func(t *testing.T) {
				setupOnTopFares(tc.input)
				resp := getOntopDetail(t, ctn, "?distanceInMeters=10000&lat=14.387610028157496&lng=100.53805230768597&onTopFareDistanceInMeters=1000")
				assert.Equal(t, len(tc.expected), len(resp.OnTopFare))
				cleanUpTest(tc.input)
			})
		}
	})
}

func setupHeatmapDataInDB(t *testing.T, ctn *ittest.IntegrationTestContainer, region string, h3ID string, loc model.HeatMapLocation) {
	hm := model.HeatMap{
		Start:      timeutil.BangkokNow().Add(-19 * time.Minute).Format(heatmapTimeFormat),
		End:        timeutil.BangkokNow().Add(-10 * time.Minute).Format(heatmapTimeFormat),
		Total:      100,
		H3ID:       h3ID,
		Location:   loc,
		Tier:       1,
		CreatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
		UpdatedAt:  timeutil.BangkokNow().Add(-19 * time.Minute),
		Region:     region,
		Thresholds: []model.Threshold{},
	}

	err := ctn.HeatMapRepository.UpsertAll([]model.HeatMap{hm})
	require.NoError(t, err)
}

func getOntopDetail(t *testing.T, ctn *ittest.IntegrationTestContainer, params string) heatmap.Res {
	drv, err := ctn.DriverRepository.FindDriverID(context.Background(), "DRV_PATTAYA_ONLINE")
	require.NoError(t, err)

	gctx := testutil.NewContextWithRecorder()
	gctx.SetGET("%s", fmt.Sprintf("/v1/driver/heatmap%s", params))
	gctx.Authorized(ctn.RedisTokenStore, drv.DriverID)

	ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

	gctx.AssertResponseCode(t, 200)
	var resp heatmap.Res
	gctx.DecodeJSONResponse(&resp)

	return resp
}

func setupPattayaOntopSchemeWithTimerangeInDB(t *testing.T, ctn *ittest.IntegrationTestContainer, timeranges []model.StartEndTime) model.OnTopFare {
	id := utils.GenerateUUID()
	onTop := model.OnTopFare{
		ID:     id,
		Scheme: model.FlatRateScheme,
		Name:   "fakeOnTop",
		Status: model.StatusActive,
		Conditions: []model.OntopCondition{
			{
				Status: model.StatusActive,
				Days: []model.Days{
					model.Monday,
					model.Tuesday,
					model.Wednesday,
					model.Thursday,
					model.Friday,
					model.Saturday,
					model.Sunday,
				},
				FlatRateAmount: 5,
				Time:           timeranges,
			},
		},
		Region: "PATTAYA",
		Geometry: model.OntopGeometry{
			Type: "MultiPolygon",
			Coordinates: model.OntopCoordinates{{{
				{100.57537078857422, 14.421380422198462},
				{100.46585083007812, 14.30530611750917},
				{100.69381713867186, 14.304640761934547},
				{100.57537078857422, 14.421380422198462},
			}}},
		},
	}

	err := ctn.OnTopFareRepository.Create(context.Background(), onTop)
	require.NoError(t, err)

	return onTop
}
