package heatmap

import (
	"context"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/uber/h3-go"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/heatmapdemand"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/polygonutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const heatmapTimeFormat = "15:04"

type MapKey struct {
	Start   string
	End     string
	Region  model.RegionCode
	H3Index h3.H3Index
}

type MapValue struct {
	Location  model.Location
	Condition model.ConditionItem
	Total     int
}

type API struct {
	HeatmapRepo             repository.HeatMapRepository
	Cfg                     Config
	HeatMapDemand           heatmapdemand.HeatMapDemand
	DriverRepo              repository.DriverRepository
	OnTopFareRepo           repository.OnTopFareRepository
	OrderRepo               repository.OrderRepository
	MatchingRateHeatmapRepo repository.MatchingRateHeatMapRepository
	ServiceAreaRepo         repository.ServiceAreaRepository
	ZoneRepo                repository.ZoneRepository
	PolygonService          polygon.Polygon
}

func ProvideHeatMapAPI(heatmapRepo repository.HeatMapRepository, cfg Config, heatMapDemand heatmapdemand.HeatMapDemand,
	driverRepository repository.DriverRepository, onTopFareRepo repository.OnTopFareRepository,
	orderRepository repository.OrderRepository, matchingRateHeatmapRepo repository.MatchingRateHeatMapRepository,
	serviceAreaRepo repository.ServiceAreaRepository, zoneRepo repository.ZoneRepository, polygonService polygon.Polygon) *API {
	return &API{
		HeatmapRepo:             heatmapRepo,
		Cfg:                     cfg,
		HeatMapDemand:           heatMapDemand,
		DriverRepo:              driverRepository,
		OnTopFareRepo:           onTopFareRepo,
		OrderRepo:               orderRepository,
		MatchingRateHeatmapRepo: matchingRateHeatmapRepo,
		ServiceAreaRepo:         serviceAreaRepo,
		ZoneRepo:                zoneRepo,
		PolygonService:          polygonService,
	}
}

func (api *API) Get(gctx *gin.Context) {
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gctx.Request.Context())
	var query repository.HeatMapQuery

	if err := gctx.Bind(&query); err != nil {
		err := gctx.Error(err)
		if err != nil {
			logrus.Warn("Get Heatmap Bind HeatMapQuery err", err)
		}
		return
	}

	driverID := driver.DriverIDFromGinContext(gctx)
	d, err := api.DriverRepo.FindDriverID(ctx, driverID, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Warnf("Get Heatmap Error - Driver ID: %s, Error: %v", driverID, err)
		apiutil.OK(gctx, NewRes([]ItemRes{}, []OnTopFareItemRes{}))
		return
	}

	var heatMapRes []ItemRes
	var onTopFareRes []OnTopFareItemRes
	if api.Cfg.HeatmapEnabled {
		heatMapRes = api.getHeatMap(gctx, query, d)
	}
	if api.Cfg.HeatmapOnTopEnabled {
		onTopFareRes = api.getOnTopFareHeatMap(gctx, query, d)
	}
	apiutil.OK(gctx, NewRes(heatMapRes, onTopFareRes))
}

func (api *API) getHeatMap(gctx *gin.Context, query repository.HeatMapQuery, d *model.Driver) []ItemRes {
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gctx.Request.Context())
	var heatMapRes []ItemRes
	ar, err := api.ServiceAreaRepo.GetByRegion(ctx, d.Region.String())
	if err != nil {
		logrus.Warnf("Heatmap GetByRegion - Driver ID: %s, Region: %s, Error: %v", d.DriverID, d.Region, err)
		return []ItemRes{}
	}
	query.Region = d.Region.String()
	heatmap := ar.HeatmapType
	if len(ar.HeatMapByTimes) > 0 {
		for _, hm := range ar.HeatMapByTimes {
			start := timeutil.ParseEffectiveHour(hm.StartTime)
			end := timeutil.ParseEffectiveHour(hm.EndTime)
			matched := timeutil.IsBetweenEqual(timeutil.BangkokNow(), start, end)
			if matched {
				heatmap = hm.HeatmapType
				break
			}
		}
	}

	switch heatmap {

	case model.DemandHm:
		{
			heatMapRes = api.getDemandHeatMap(gctx, query)
		}
	case model.MatchingRateHm:
		{
			heatMapRes = api.getMatchingRateHeatMap(query)
		}
	default:
		heatMapRes = api.getDemandHeatMap(gctx, query)
	}

	return heatMapRes
}

func (api *API) getDemandHeatMap(gctx *gin.Context, query repository.HeatMapQuery) []ItemRes {
	if query.Time.IsZero() {
		query.Time = timeutil.BangkokNow().Add(time.Duration(api.Cfg.HeatMapTimeSlotInMinute) * time.Minute)
	}

	if query.DistanceInMeters == 0 {
		query.DistanceInMeters = api.Cfg.MaxDistance
	}

	heatmaps, err := api.HeatmapRepo.Find(query, repository.WithReadSecondaryPreferred)
	if err != nil {
		err := gctx.Error(err)
		if err != nil {
			logrus.Warn("Get Heatmap err", err)
		}
		return []ItemRes{}
	}

	hMap := make(map[string]ItemRes)
	for _, in := range heatmaps {
		heatmapRes := NewHeatMapRes(in)
		heatmapRes.Color = getHeatMapColor(in.Tier, api.Cfg)
		heatmapRes.Tier = in.Tier

		v, e := hMap[heatmapRes.H3Id]
		if in.Tier != 0 && !e || in.Tier != 0 && in.Tier < v.Tier {
			hMap[heatmapRes.H3Id] = *heatmapRes
		}
	}

	res := make([]ItemRes, 0, len(hMap))
	for _, itemRes := range hMap {
		res = append(res, itemRes)
	}
	return res
}

func (api *API) getOnTopFareHeatMap(gctx *gin.Context, query repository.HeatMapQuery, d *model.Driver) []OnTopFareItemRes {
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gctx.Request.Context())

	var ots = make([]model.OnTopFare, 0)

	if query.OnTopFareDistanceInMeters == 0 {
		query.OnTopFareDistanceInMeters = api.Cfg.OnTopFareMaxDistance
	}

	region, err := api.PolygonService.GetRegion(ctx, string(d.Region))
	if err != nil {
		err := gctx.Error(err)
		if err != nil {
			logrus.Warn("Get On Top Fare Pin Heatmap err", err)
		}
		return []OnTopFareItemRes{}
	}
	regionOntopPolygon := model.OntopGeometry{
		Type:        "MultiPolygon",
		Coordinates: polygonutil.FromGeomMultiPolygonToFloat64(polygonutil.FromGeomPolygonToGeomMultiPolygon(region.Geometry.Coordinates)),
	}

	onTops, err := api.OnTopFareRepo.GetOnTopFareHeatMap(ctx, query.Lat, query.Lng, d.Region.String(), regionOntopPolygon, query.OnTopFareDistanceInMeters)
	if err != nil {
		err := gctx.Error(err)
		if err != nil {
			logrus.Warn("Get On Top Fare Heatmap err", err)
		}
		return []OnTopFareItemRes{}
	}

	for _, ontop := range onTops {
		if ontop.IsActive(timeutil.BangkokNow().UTC()) {
			ots = append(ots, ontop)
		}
	}

	onTopPins, err := api.OnTopFareRepo.GetOnTopFarePinHeatMap(ctx, toOnTopFarePinHeatMapQuery(query, d), repository.WithReadSecondaryPreferred)
	if err != nil {
		err := gctx.Error(err)
		if err != nil {
			logrus.Warn("Get On Top Fare Pin Heatmap err", err)
		}
		return []OnTopFareItemRes{}
	}

	for _, ontoppin := range onTopPins {
		if ontoppin.IsActive(timeutil.BangkokNow().UTC()) {
			ots = append(ots, ontoppin)
		}
	}
	return getOnTopFareItemRes(ots, api.Cfg)
}

func toOnTopFarePinHeatMapQuery(q repository.HeatMapQuery, d *model.Driver) persistence.OnTopFarePinHeatMapQuery {
	return persistence.OnTopFarePinHeatMapQuery{
		Location: model.Location{
			Lat: q.Lat,
			Lng: q.Lng,
		},
		Region:           d.Region.String(),
		DistanceInMeters: q.OnTopFareDistanceInMeters,
		Scheme:           []model.OnTopFareScheme{model.FlatRateScheme, model.FlexibleFlatRateScheme},
		Status:           string(model.StatusActive),
	}
}

func (api *API) CalculateHeatMap(orders []model.OrderEventPayload) ([]model.HeatMap, error) {

	start := timeutil.BangkokNow().Add(-20 * time.Minute).Format(heatmapTimeFormat)
	end := timeutil.BangkokNow().Add(10 * time.Minute).Format(heatmapTimeFormat)
	condition, err := api.HeatMapDemand.GetHeatmapCondition(start, end)
	if err != nil {
		logrus.Warn("get heatmap condition error : ", err)
		return nil, err
	}
	resolution := api.Cfg.Resolution
	ordersGrouped, h3IDs := getUniqueH3IdAndOrderGroup(orders, condition, resolution)

	heatMaps, err := api.HeatmapRepo.FindByH3IDIn(h3IDs, repository.Opt().ReadOpt())
	if err != nil {
		logrus.Warn("get existing heatmap from database error : ", err)
		return nil, err
	}

	var listHm []model.HeatMap
	for ogk, ogv := range ordersGrouped {
		h3Id := strconv.FormatUint(uint64(ogk.H3Index), 16)
		exist, hm := findExistingHeatMap(heatMaps, h3Id, ogk)

		if exist {
			hm.Total += ogv.Total
			hm.Tier = ogv.Condition.GetHeatMapTier(hm.Total)
			hm.Thresholds = ogv.Condition.Tiers
			hm.UpdatedAt = time.Now()
			listHm = append(listHm, hm)
		} else {
			h3Geo := h3.ToGeo(ogk.H3Index)
			heatmap := createNewHeatMap(h3Geo, ogv, ogk, h3Id)
			listHm = append(listHm, heatmap)
		}
	}

	err = api.HeatmapRepo.UpsertAll(listHm)

	if err != nil {
		logrus.Warn("save heatmap error:", err)
		return nil, err
	}

	err = api.HeatmapRepo.DeleteByCreatedAtLte(time.Now().Add(-30 * time.Minute))
	if err != nil {
		logrus.Warn("delete heatmap error:", err)
		return nil, err
	}

	return listHm, nil
}

func createNewHeatMap(h3Geo h3.GeoCoord, ogv MapValue, ogk MapKey, h3Id string) model.HeatMap {
	total := ogv.Total
	tier := ogv.Condition.GetHeatMapTier(total)
	now := time.Now().UTC()
	heatmap := model.HeatMap{
		Start:      ogk.Start,
		End:        ogk.End,
		Total:      total,
		H3ID:       h3Id,
		Location:   model.NewHeatMapPoint(h3Geo.Longitude, h3Geo.Latitude),
		Tier:       tier,
		CreatedAt:  now,
		UpdatedAt:  now,
		Region:     ogk.Region.String(),
		Thresholds: ogv.Condition.Tiers,
	}
	return heatmap
}

func findExistingHeatMap(heatMaps []model.HeatMap, h3Id string, ogk MapKey) (exist bool, hm model.HeatMap) {

	for _, heatMap := range heatMaps {
		if h3Id == heatMap.H3ID &&
			heatMap.Region == ogk.Region.String() &&
			len(heatMap.Start) != 0 &&
			len(heatMap.End) != 0 &&
			heatMap.Start <= ogk.Start &&
			heatMap.End >= ogk.End {
			exist = true
			hm = heatMap
			break
		}
	}
	return
}

func getUniqueH3IdAndOrderGroup(orders []model.OrderEventPayload, cond model.Condition, resolution int) (map[MapKey]MapValue, []string) {
	oMap := make(map[MapKey]MapValue)
	var uKeys []string
	keys := make(map[string]bool)
	for _, o := range orders {
		geo := h3.GeoCoord{Latitude: o.Location.Lat, Longitude: o.Location.Lng}
		h3Index := h3.FromGeo(geo, resolution)
		h3Id := strconv.FormatUint(uint64(h3Index), 16)
		if _, v := keys[h3Id]; !v {
			keys[h3Id] = true
			uKeys = append(uKeys, h3Id)
		}
		condExisting, ci := cond.GetConditionItem(o)

		hmk := MapKey{
			Start:   ci.Start,
			End:     ci.End,
			Region:  model.RegionCode(o.Region),
			H3Index: h3Index,
		}

		hmv, mExisting := oMap[hmk]

		if mExisting && condExisting {
			hmv.Total += 1
			hmv.Condition = ci
			hmv.Location = model.Location{Lng: o.Location.Lng, Lat: o.Location.Lat}
			oMap[hmk] = hmv
		} else if condExisting {

			hmv := MapValue{
				Total:     1,
				Location:  model.Location{Lng: o.Location.Lng, Lat: o.Location.Lat},
				Condition: ci,
			}
			oMap[hmk] = hmv
		}
	}
	return oMap, uKeys
}

func (api *API) getMatchingRateHeatMap(query repository.HeatMapQuery) []ItemRes {
	if query.DistanceInMeters == 0 {
		query.DistanceInMeters = api.Cfg.MatchingRate.MaxDistance
	}
	hm, err := api.MatchingRateHeatmapRepo.Find(query, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Warn("Get matching rate heatmap err", err)
		return []ItemRes{}
	}
	res := make([]ItemRes, 0, len(hm))
	for _, it := range hm {
		hmRes := NewMatchingRateHeatMapRes(it)
		t, c := getMatchingRateHeatMapTierAndColor(it, api.Cfg)
		if t > 0 && it.UnmatchedOrder > api.Cfg.MatchingRate.UnmatchedMinOrder {
			hmRes.Tier = t
			hmRes.Color = c
			res = append(res, *hmRes)
		}
	}
	return res
}

func getMatchingRateHeatMapTierAndColor(hm model.MatchingRateHeatMap, cfg Config) (int, string) {
	t, c := 0, cfg.ZeroTierColor
	if hm.UnmatchedScorePercent > cfg.MatchingRate.FirstTierScorePercent {
		t = 1
		c = cfg.MatchingRate.FirstTierColor
	} else if hm.UnmatchedScorePercent > cfg.MatchingRate.SecondTierScorePercent {
		t = 2
		c = cfg.MatchingRate.SecondTierColor
	} else if hm.UnmatchedScorePercent > cfg.MatchingRate.ThirdTierScorePercent {
		t = 3
		c = cfg.MatchingRate.ThirdTierColor
	} else if hm.UnmatchedScorePercent > cfg.MatchingRate.FourthTierScorePercent {
		t = 4
		c = cfg.MatchingRate.FourthTierColor
	}

	return t, c

}

func getHeatMapColor(tier int, cfg Config) string {
	switch tier {
	case 1:
		return cfg.FirstTierColor
	case 2:
		return cfg.SecondTierColor
	case 3:
		return cfg.ThirdTierColor
	case 4:
		return cfg.FourthTierColor
	default:
		return cfg.ZeroTierColor
	}
}
