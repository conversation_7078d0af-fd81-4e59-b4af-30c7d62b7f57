package heatmap

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type ItemRes struct {
	H3Id  string `json:"h3id"`
	Color string `json:"color"`
	Tier  int    `json:"tier"`
}

type OnTopHeatMapRes struct {
	OnTopFare         model.OnTopFare
	OnTopCondition    model.OntopCondition
	OnTopConditionIdx int
}

type RestaurantRes struct {
	ID   string  `json:"id"`
	Name string  `json:"name"`
	Desc string  `json:"description"`
	Lat  float64 `json:"lat"`
	Lng  float64 `json:"lng"`
}

type OnTopFareItemRes struct {
	Color                string                `json:"color"`
	BackgroundColor      string                `json:"backgroundColor"`
	Geometry             *OnTopFareGeometryRes `json:"geometry,omitempty"`
	FlatRateAmount       float64               `json:"flatRateAmount"`
	BundleFlatRateAmount float64               `json:"bundleFlatRateAmount"`
	Days                 []model.Days          `json:"days"`
	Time                 []OnTopTimeRes        `json:"time"`
	Name                 string                `json:"name"`
	RemainingUpcoming    string                `json:"remainingUpcoming"`
	StatusLabel          string                `json:"statusLabel"`
	IsActived            bool                  `json:"isActived"`
	ID                   string                `json:"id"`
	FirstStartTime       time.Time             `json:"-"`
	ServiceTypes         []model.Service       `json:"serviceTypes"`
	Places               []RestaurantRes       `json:"places,omitempty"`
	ConditionIdx         int                   `json:"conditionIdx"`
	UpdatedAt            *time.Time            `json:"updatedAt,omitempty"`
}

type OnTopTimeRes struct {
	Begin string `json:"begin"`
	End   string `json:"end"`
}

type OnTopFareGeometryRes struct {
	Type        string                 `json:"type"`
	Coordinates model.OntopCoordinates `json:"coordinates"`
}

func NewHeatMapRes(heatmap model.HeatMap) *ItemRes {
	return &ItemRes{
		H3Id: heatmap.H3ID,
	}
}

func NewMatchingRateHeatMapRes(heatmap model.MatchingRateHeatMap) *ItemRes {
	return &ItemRes{
		H3Id: heatmap.H3Index,
	}
}

type Res struct {
	Data      []ItemRes          `json:"data"`
	OnTopFare []OnTopFareItemRes `json:"onTopFare"`
}

func NewRes(heatmap []ItemRes, onTopFareHeatMap []OnTopFareItemRes) *Res {
	return &Res{heatmap, onTopFareHeatMap}
}

func toOnTopTimeRes(times []model.StartEndTime) []OnTopTimeRes {
	timeRes := make([]OnTopTimeRes, 0, len(times))
	for _, tt := range times {
		timeRes = append(timeRes, OnTopTimeRes{
			Begin: tt.Begin,
			End:   tt.End,
		})
	}
	return timeRes
}

func toRestaurantRes(r []model.OnTopRestaurant) []RestaurantRes {
	res := make([]RestaurantRes, 0)
	for _, rt := range r {
		res = append(res, RestaurantRes{
			ID:   rt.RestaurantId,
			Name: rt.RestaurantName,
			Desc: rt.Description,
			Lat:  rt.Loc.Coordinates[1],
			Lng:  rt.Loc.Coordinates[0],
		})
	}
	return res
}

func toOnTopFareGeometryRes(geo model.OntopGeometry) *OnTopFareGeometryRes {
	if geo.Coordinates == nil {
		return nil
	}

	return &OnTopFareGeometryRes{
		Type:        geo.Type,
		Coordinates: geo.Coordinates,
	}
}

func toOnTopName(otHm OnTopHeatMapRes, format string) string {
	if otHm.OnTopFare.Label != "" {
		return otHm.OnTopFare.Label
	}
	amount := strconv.FormatFloat(otHm.OnTopCondition.FlatRateAmount, 'f', -1, 64)
	return fmt.Sprintf(format, amount)
}

// IsOnTopInTime look like it's complex. need to refactor.
func IsOnTopInTime(it model.OntopCondition, days []time.Time, heatmapStart time.Time, heatmapEnd time.Time) (bool, string, bool, time.Time) {
	isInTime := false
	remainingUpcoming := ""
	isActived := false
	firstStartTime := time.Time{}

	if len(days) == 0 {
		return isInTime, remainingUpcoming, isActived, firstStartTime
	}

	for _, t := range it.Time {
		start := strings.Split(t.Begin, ":")
		end := strings.Split(t.End, ":")
		startHr, _ := strconv.Atoi(start[0])
		startMin, _ := strconv.Atoi(start[1])
		startSec := 0
		endHr, _ := strconv.Atoi(end[0])
		endMin, _ := strconv.Atoi(end[1])
		endSec := 0

		if len(start) == 3 && len(end) == 3 {
			bsec, _ := strconv.Atoi(start[2])
			startSec = bsec
			esec, _ := strconv.Atoi(end[2])
			endSec = esec
		}

		for _, day := range days {
			day = day.In(timeutil.BangkokLocation())
			dYear, dMonth, dDay := day.Date()
			startTime := time.Date(dYear, dMonth, dDay, startHr, startMin, startSec, 0, timeutil.BangkokLocation())
			endTime := time.Date(dYear, dMonth, dDay, endHr, endMin, endSec, 0, timeutil.BangkokLocation())
			dayUpper := strings.ToUpper(day.Format("Mon"))
			d := model.Days(dayUpper)

			if heatmapStart.Before(endTime) && startTime.Before(heatmapEnd) && isInDay(it, d) && (firstStartTime.IsZero() || startTime.Before(firstStartTime)) {
				remainingUpcoming, isActived = getOnTopRemainingUpcoming(startTime, heatmapStart, endTime, it)
				isInTime = true
				firstStartTime = startTime
			}
		}
	}
	return isInTime, remainingUpcoming, isActived, firstStartTime
}

func getOnTopFareItemRes(onTops []model.OnTopFare, config Config) []OnTopFareItemRes {
	onTopHeatMaps := make([]OnTopHeatMapRes, 0)
	for _, ot := range onTops {
		for conditionIdx, condition := range ot.Conditions {
			if condition.Status == model.StatusActive {
				onHm := OnTopHeatMapRes{ot, condition, conditionIdx}
				onTopHeatMaps = append(onTopHeatMaps, onHm)
			}
		}
	}

	hStart := timeutil.BangkokNow()
	hEnd := timeutil.BangkokNow().Add(time.Duration(config.OnTopFareNextHours) * time.Hour)
	hDays := getHeatMapDay(hStart, hEnd)

	onTopFareMap := make(map[string]OnTopFareItemRes)
	for _, otHm := range onTopHeatMaps {
		isInDay := isOnTopInDay(otHm.OnTopCondition, hDays)

		isInTime, remainingUpcoming, isActived, firstStartTime := IsOnTopInTime(otHm.OnTopCondition, hDays, hStart, hEnd)
		if isInDay && isInTime {
			item := toOnTopFareItemRes(config, otHm, remainingUpcoming, isActived, firstStartTime)
			key := item.ID
			for _, s := range otHm.OnTopCondition.ServiceTypes {
				key = key + s.String()
			}
			otHm, v := onTopFareMap[key]
			if !v || (item.FirstStartTime.Before(otHm.FirstStartTime) && !otHm.IsActived) {
				onTopFareMap[key] = item
			}
		}
	}

	onTopFareRes := make([]OnTopFareItemRes, 0, len(onTopFareMap))
	for _, it := range onTopFareMap {
		onTopFareRes = append(onTopFareRes, it)
	}

	filteredOnTopFareRes := filterOnTopFareByRateAmountIsNotZero(onTopFareRes)

	return filteredOnTopFareRes
}

func filterOnTopFareByRateAmountIsNotZero(onTopFareRes []OnTopFareItemRes) []OnTopFareItemRes {
	filteredOnTopFareRes := make([]OnTopFareItemRes, 0, len(onTopFareRes))
	for _, it := range onTopFareRes {
		if it.FlatRateAmount != 0 || it.BundleFlatRateAmount != 0 {
			filteredOnTopFareRes = append(filteredOnTopFareRes, it)
		}
	}
	return filteredOnTopFareRes
}

func toOnTopFareItemRes(config Config, otHm OnTopHeatMapRes, remainingUpcoming string, actived bool, firstStartTime time.Time) OnTopFareItemRes {
	color, backgroundColor, statusLabel := "", "", ""
	if actived {
		backgroundColor = config.OnTopFareActiveBackgroundColor
		color = config.OnTopFareActiveColor
		statusLabel = config.OnTopFareActiveStatusLabel
	} else {
		backgroundColor = config.OnTopFareInActiveBackgroundColor
		color = config.OnTopFareInActiveColor
		statusLabel = config.OnTopFareInActiveStatusLabel
	}

	normalOnTopAmount := otHm.OnTopCondition.FlatRateAmount
	bundleOnTopAmount := otHm.OnTopCondition.FlatRateAmount
	if otHm.OnTopFare.Scheme == model.FlexibleFlatRateScheme {
		for _, ffr := range otHm.OnTopCondition.FlatRatePrices {
			if ffr.FlatRateType == model.DefaultFlatRate {
				normalOnTopAmount = ffr.NormalAmount.Float64()
				bundleOnTopAmount = ffr.NormalAmount.Float64()
				break
			} else if ffr.FlatRateType == model.FlexibleFlatRate {
				normalOnTopAmount = ffr.NormalAmount.Float64()
				bundleOnTopAmount = ffr.BundleAmount.Float64()
				break
			}
		}
	}

	var updatedAt *time.Time
	if !otHm.OnTopFare.UpdatedAt.IsZero() {
		updatedAt = types.NewTime(otHm.OnTopFare.UpdatedAt)
	}

	return OnTopFareItemRes{
		color,
		backgroundColor,
		toOnTopFareGeometryRes(otHm.OnTopFare.Geometry),
		normalOnTopAmount,
		bundleOnTopAmount,
		otHm.OnTopCondition.Days,
		toOnTopTimeRes(otHm.OnTopCondition.Time),
		toOnTopName(otHm, config.OnTopFareLabelFormat),
		remainingUpcoming,
		statusLabel,
		actived,
		otHm.OnTopFare.ID,
		firstStartTime,
		otHm.OnTopCondition.ServiceTypes,
		toRestaurantRes(otHm.OnTopFare.Restaurants),
		otHm.OnTopConditionIdx,
		updatedAt,
	}
}

func isOnTopInDay(it model.OntopCondition, days []time.Time) bool {
	isInDay := false
	for _, day := range it.Days {
		for _, d := range days {
			if model.Days(strings.ToUpper(d.Format("Mon"))) == day {
				isInDay = true
				break
			}
		}
	}
	return isInDay
}

func isInDay(it model.OntopCondition, otDay model.Days) bool {
	inDay := false
	for _, day := range it.Days {
		if day == otDay {
			inDay = true
			break
		}
	}
	return inDay
}

func getHeatMapDay(hStart time.Time, hEnd time.Time) []time.Time {
	hDays := make([]time.Time, 0)
	for rd := rangeDateToDay(hStart, hEnd); ; {
		d := rd()
		if d.IsZero() {
			break
		}
		hDays = append(hDays, d)
	}
	return hDays
}

func getOnTopRemainingUpcoming(startTime time.Time, heatmapStart time.Time, endTime time.Time, ot model.OntopCondition) (string, bool) {
	remainingUpcoming := ""
	isActived := false

	dayUpper := strings.ToUpper(timeutil.BangkokNow().Format("Mon"))
	otDay := model.Days(dayUpper)
	if startTime.Before(heatmapStart) && endTime.After(heatmapStart) && isInDay(ot, otDay) {
		remaining := getOnTopRemaining(heatmapStart, endTime)
		remainingUpcoming = fmt.Sprintf("เหลือ %v", remaining)
		isActived = true
	} else {
		remainingUpcoming = fmt.Sprintf("เริ่ม %v - %v", startTime.Format(heatmapTimeFormat), endTime.Format(heatmapTimeFormat))
	}
	return remainingUpcoming, isActived
}

func getOnTopRemaining(heatmapStart time.Time, endTime time.Time) string {
	duration := endTime.Sub(heatmapStart)
	hours, minutes, seconds := timeutil.ExplodeDuration(duration)
	remaining := ""
	if hours > 0 {
		remaining = fmt.Sprintf("%d.%d ชั่วโมง", hours, minutes)
	} else if minutes > 0 {
		remaining = fmt.Sprintf("%d นาที", minutes)
	} else if seconds > 0 {
		remaining = fmt.Sprintf("%d วินาที", seconds)
	}
	return remaining
}

func rangeDateToDay(start, end time.Time) func() time.Time {
	y, m, d := start.Date()
	start = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
	y, m, d = end.Date()
	end = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)

	return func() time.Time {
		if start.After(end) {
			return time.Time{}
		}
		date := start
		start = start.AddDate(0, 0, 1)
		return date
	}
}
