package egs

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type UpdateInstallmentStatusReq struct {
	InstallmentID        string                  `json:"installmentId"`
	Status               model.InstallmentStatus `json:"status"`
	DriverID             string                  `json:"driverId"`
	Amount               float64                 `json:"amount"`
	LastAttempt          bool                    `json:"lastAttempt"`
	ExpectedDeliveryDate time.Time               `json:"expectedDeliveryDate"`
}

type NotificationEvent string

const (
	NotificationEventOrderStatusChanged NotificationEvent = "EVENT_ORDER_STATUS_CHANGED"
)

type NotificationModelReq struct {
	DriverID string            `json:"driverId"`
	Event    NotificationEvent `json:"event"`
	Title    string            `json:"title"`
	Body     string            `json:"body"`
}

type PushNotificationHookReq struct {
	Notifications []NotificationModelReq `json:"notifications"`
}

type UpdateInstallmentProductDetailReq struct {
	InstallmentID    string              `json:"installmentId"`
	StockSKU         string              `json:"stockSku"`
	StockID          string              `json:"stockId"`
	NewProductDetail model.ProductDetail `json:"productDetail"`
}
