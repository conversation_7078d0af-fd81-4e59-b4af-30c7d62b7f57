package egs

import (
	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	EnableEgsOms                         bool   `envconfig:"ENABLE_EGS_OMS" default:"false"`
	EgsInstallmentCutOffTime             string `envconfig:"EGS_INSTALLMENT_CUTOFF_TIME" default:"20:30"`
	EgsExpectedDeliveryDateOverrideSlack bool   `envconfig:"EGS_EXPECTED_DELIVERY_DATE_OVERRIDE_SLACK" default:"true"`
}

func ProvideHookConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
