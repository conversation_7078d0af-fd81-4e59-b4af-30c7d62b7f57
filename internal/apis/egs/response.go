package egs

import (
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type UpdateInstallmentStatusRes struct {
	InstallmentID string
}

type UpdateInstallmentProductDetailRes struct {
	InstallmentID             string                `json:"installmentId"`
	InstallmentPrincipalPrice types.Money           `json:"installmentPrincipalPrice"`
	ProductDetails            []model.ProductDetail `json:"productDetails"`
}

type DriverPurchasingPowerDependencyDataResponse struct {
	TotalInstallmentDailyAmount      float64                     `json:"totalInstallmentDailyAmount"`
	ToalInstallmentOutstandingAmount float64                     `json:"totalInstallmentOutstandingAmount"`
	TotalIncomeSummaryDailyAmount    float64                     `json:"totalIncomeSummaryDailyAmount"`
	FinancialRisk                    DriverFinancialRiskResponse `json:"financialRisk"`
}

type DriverFinancialRiskResponse struct {
	DSCR           float64 `json:"dscr"`
	MaxTenor       int     `json:"maxTenor"`
	RawMaxExposure float64 `json:"rawMaxExposure"`
}
