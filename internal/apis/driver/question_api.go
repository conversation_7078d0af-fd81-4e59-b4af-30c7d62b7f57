package driver

import (
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func (api *RegistrationAPI) GetQuestions(gctx *gin.Context) {
	lineAccessToken := gctx.GetHeader(KeyLineAccessToken)
	if lineAccessToken == "" {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "x-line-access-token header is required"))
		return
	}
	profile, err := api.tokenVerifier.GetUserProfile(gctx, lineAccessToken)
	if err != nil {
		logrus.WithContext(gctx).Warnf("GetQuestions get user profile from line access token error: %v", err)
		apiutil.ErrInternalError(gctx, apiutil.NewFromString(absapi.ERRCODE_INTERNAL_ERROR, "get user profile from line access token error"))
		return
	}
	api.doGetQuestions(gctx, profile.UserID)
}

func (api *RegistrationAPI) GetQuestionsByLineUid(gctx *gin.Context) {
	lineUID := gctx.Param(KeyLineUid)
	api.doGetQuestions(gctx, lineUID)
}

func (api *RegistrationAPI) doGetQuestions(gctx *gin.Context, lineUID string) {
	ctx := gctx.Request.Context()
	found := api.RegistrationRepo.IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(lineUID))
	if !found {
		apiutil.ErrNotFound(gctx, apiError.ErrRegistrationNotExists())
		return
	}

	questions, err := api.QuestionRepo.GetQuestionAnswers(ctx)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	q := NewQuestionResp(questions)
	apiutil.OK(gctx, q)
}

func (api *RegistrationAPI) ValidateQuestions(gctx *gin.Context) {
	path1, path2 := gctx.Param("path1"), gctx.Param("path2")
	if path1 != "" && path2 == "validate" {
		api.doValidateQuestions(gctx, path1)
		return
	} else if path1 == "validate" && path2 == "" {
		lineAccessToken := gctx.GetHeader(KeyLineAccessToken)
		if lineAccessToken == "" {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "x-line-access-token header is required"))
			return
		}
		profile, err := api.tokenVerifier.GetUserProfile(gctx, lineAccessToken)
		if err != nil {
			logrus.WithContext(gctx).Warnf("ValidateQuestions get user profile from line access token error: %v", err)
			apiutil.ErrInternalError(gctx, apiutil.NewFromString(absapi.ERRCODE_INTERNAL_ERROR, "get user profile from line access token error"))
			return
		}
		api.doValidateQuestions(gctx, profile.UserID)
	} else {
		apiutil.ErrNotFound(gctx, apiutil.NewFromString(absapi.ERRCODE_NOT_FOUND, "Page not found"))
	}
}

func (api *RegistrationAPI) doValidateQuestions(gctx *gin.Context, lineUid string) {
	ctx := gctx.Request.Context()
	req, err := NewAnswerReq(gctx, lineUid)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	// `req.lineUID` can be in either MID or UID format
	driver, err := api.RegistrationRepo.GetByLineUID(ctx, req.lineUID)
	if err != nil {
		apiutil.ErrNotFound(gctx, apiutil.NewFromError("LINE_ID_INVALID", err))
		return
	}

	questions, err := api.QuestionRepo.GetQuestionAnswers(ctx)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	question := model.QuestionAnswers{
		Questions: questions,
	}
	userAnswers := req.GetAnswerRequest()
	validateResult := question.Validate(userAnswers)
	driver.Trained = validateResult.Pass

	err = api.RegistrationRepo.Update(ctx, driver)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, struct {
		Pass      bool `json:"pass"`
		Score     int  `json:"score"`
		FullScore int  `json:"fullScore"`
	}{
		Pass:      validateResult.Pass,
		Score:     validateResult.Score,
		FullScore: validateResult.FullScore,
	})
}
