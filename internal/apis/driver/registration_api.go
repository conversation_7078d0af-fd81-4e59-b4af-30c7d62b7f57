package driver

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"golang.org/x/sync/errgroup"

	"git.wndv.co/go/logx/v2"
	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/executor"
	"git.wndv.co/lineman/fleet-distribution/internal/messages"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const (
	KeyLineUid                    = "lineuid"
	KeyLineAccessToken            = "x-line-access-token"
	errRegistrationAlreaqdyExists = "REGISTRATION_ALREADY_EXISTS"
)

// RegistrationAPI provides an api to register driver.
type RegistrationAPI struct {
	Config           RegisterHandlerConfig
	GlobalConfig     config.GlobalConfig
	VosService       service.VOSService
	DriverService    service.DriverServiceInterface
	RegistrationRepo repository.DriverRegistrationRepository
	MsgSvc           messages.MessageServiceInterface
	QuestionRepo     repository.QuestionRepository

	whitelistPhoneRepo  repository.WhitelistPhoneRepository
	srvareaRepo         repository.ServiceAreaRepository
	driverRepo          repository.DriverRepository
	txnRepo             repository.TransactionRepository
	driverTxnRepo       repository.DriverTransactionRepository
	taskExecutor        executor.TaskExecutor
	provinceRepo        repository.ProvinceRepository
	otpRepo             repository.OTPSessionRepo
	repSvc              rep.REPService
	vosCfg              file.VOSConfig
	driverProfileReqCfg config.DriverProfileRequestConfig
	tokenVerifier       auth.TokenVerifier
	lineInternalClient  lineinternal.Client
	FeatureFlagService  featureflag.Service
}

// PreRegister - this route seem to be deprecated
//
// Route: POST "/v1/driver/register"
func (api *RegistrationAPI) PreRegister(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	var req PreRegisterReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	driverRegistration := req.DriverRegistration()
	// `LineUID` can be eiter LINE MID or LINE UID
	isExists := api.RegistrationRepo.IsExistsByLineUid(ctx, driverRegistration.LineUID)
	if isExists {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, DriverAlreadyRegisteredMessage))
		return
	}

	lalamoveProvince := strings.Split(api.Config.LalamoveProvince, ",")
	var isLLMProvince bool
	for _, llmprovince := range lalamoveProvince {
		if strings.TrimSpace(req.InterestingProvince) == strings.TrimSpace(llmprovince) {
			isLLMProvince = true
			break
		}
	}

	var message string
	if isLLMProvince {
		if err := driverRegistration.Archive(DriverPreRegisterReasonArchived); err != nil {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, DriverAlreadyRegisteredMessage))
			return
		}
		message = DriverRegisteredLalamoveProvinceMessage
	} else {
		if err := driverRegistration.RequestUpdate(DriverPreRegisterReason); err != nil {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, DriverAlreadyRegisteredMessage))
			return
		}
		message = DriverRegisteredWelcomeMessage
	}

	if err := api.RegistrationRepo.Insert(ctx, &driverRegistration); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	// it can got canceled if we send context from http request.
	err := api.MsgSvc.SendMessageContext(
		context.Background(),
		req.LineUID,
		message,
	)
	if err != nil {
		logrus.Errorf("Cannot send message to driver on PreRegister. err=%s", err)
	}
	apiutil.OK(gctx, struct{}{})
}

func (api *RegistrationAPI) Register(gctx *gin.Context) {
	logrus.Infof("create registration info : \n%s\n", gctx.Request.FormValue("registration"))
	req, apiErr := NewRegistrationReq(gctx)
	if apiErr != nil {
		_ = gctx.Error(apiErr)
		return
	}

	ctx := gctx.Request.Context()
	var r *model.DriverRegistration
	r, files, err := req.DriverRegistration()
	if err != nil {
		switch t := err.(type) {
		case apiError.MultipleError:
			apiutil.ErrBadRequest(gctx, t.APIError())
		default:
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		}
		return
	}

	// Check only Revise flow becasue Normal Flow will send expiration date and image of Driver ID Card in different request (separately)
	if _, ok := req.(*ReviseRegistrationReq); ok {
		if api.driverProfileReqCfg.EnableRequireDriverIdCardImageExpireDateFlag {
			var isImageExisted bool
			for _, item := range files {
				if item.Name == CitizenIDImageName {
					isImageExisted = true
					break
				}
			}
			isExpireDateExisted := r.CitizenIDExpiredDate != nil && !r.CitizenIDExpiredDate.IsZero()
			var err error
			switch {
			case isExpireDateExisted && timeutil.BangkokNow().After(*r.CitizenIDExpiredDate):
				err = apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, api.driverProfileReqCfg.InvalidExpirationDateText)
			case isImageExisted && !isExpireDateExisted:
				err = apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, api.driverProfileReqCfg.IDCardExpRequiredText)
			case !isImageExisted && isExpireDateExisted:
				err = apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, api.driverProfileReqCfg.IDCardImageRequiredText)
			}
			if err != nil {
				apiutil.ErrBadRequest(gctx, err.(*absapi.Error))
				return
			}
		}
	}
	if api.driverProfileReqCfg.EnableRequireDriverLegislationImageExpireDateFlag {
		var isImageExisted bool
		for _, item := range files {
			if item.Name == LegislationImageName {
				isImageExisted = true
				break
			}
		}
		isExpireDateExisted := r.Vehicle.LegislationExpiredDate != nil && !r.Vehicle.LegislationExpiredDate.IsZero()
		var err error
		switch {
		case isExpireDateExisted && timeutil.BangkokNow().After(*r.Vehicle.LegislationExpiredDate):
			err = apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, api.driverProfileReqCfg.InvalidExpirationDateText)
		case isImageExisted && !isExpireDateExisted:
			err = apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, api.driverProfileReqCfg.LegislationExpRequiredText)
		case !isImageExisted && isExpireDateExisted:
			err = apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, api.driverProfileReqCfg.LegislationImageRequiredText)
		}
		if err != nil {
			apiutil.ErrBadRequest(gctx, err.(*absapi.Error))
			return
		}
	}

	lineAccessToken := gctx.GetHeader(KeyLineAccessToken)
	if lineAccessToken != "" {
		profile, err := api.tokenVerifier.GetUserProfile(gctx, lineAccessToken)
		if err != nil {
			logrus.WithContext(gctx).Warnf("RegistrationAPI Register get user profile from line access token error: %v", err)
			apiutil.ErrInternalError(gctx, apiutil.NewFromString(absapi.ERRCODE_INTERNAL_ERROR, "get user profile from line access token error"))
			return
		}
		r.LineUID = crypt.NewLazyEncryptedString(profile.UserID)
	}

	province, err := api.provinceRepo.GetByName(ctx, r.InterestingProvince)
	if err != nil || province.Hidden {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString("NOT_FOUND", "interesting province is not found"))
		return
	}
	r.Region = province.RegionCode

	// `LineUID` can be eiter LINE MID or LINE UID
	previousCheckedLineUID := r.LineUID.String()
	if api.RegistrationRepo.IsExistsByLineUid(ctx, r.LineUID) {
		_ = gctx.Error(apiutil.NewFromString(errRegistrationAlreaqdyExists, "registration already exists."))
		return
	}

	// this account might be a new person because we can't find a Driver via profile.UserID
	{
		lineUID, lineMID, extractErr := api.extractLINEUserID(ctx, r.LineUID.String())
		r.LINEUserID = lineUID
		r.LineUID = crypt.NewLazyEncryptedString(lineMID)
		if extractErr != nil {
			// only log an error becasue this process is an add-on, just to encrypt/decrypt LINE user id and store into a database
			// if this process is error, the driver should be able to register but we will miss some field which need to be patched later
			logx.Error().Context(ctx).
				Err(extractErr).
				Str("method", "Register").
				Msg("unable to extract LINE user id")
		}
	}

	// let try to find a driver again by decrypt a LINE UID, if possible
	if api.isDriverExistedCheckByLINEMID(ctx, previousCheckedLineUID, r) {
		logx.Error().Context(ctx).
			Str("method", "Registration").
			Msg("[MID-MIGRATION] found an existed driver by finding with LINE MID after tried with LINE UID")
		_ = gctx.Error(apiutil.NewFromString(errRegistrationAlreaqdyExists, "registration already exists."))
		return
	}

	var regisRes *RegistrationRes
	switch req.(type) {
	case *ReviseRegistrationReq:
		var isApproved bool
		registration, isApproved, err := api.reviseRegistration(ctx, r, files, province)
		if err != nil {
			switch t := err.(type) {
			case apiError.MultipleError:
				apiutil.ErrBadRequest(gctx, t.APIError())
			default:
				apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
			}
			return
		}

		regisRes = NewRegistrationReviseRes(*registration, isApproved)
	default:
		registration, err := api.registration(ctx, r, files)
		if err != nil {
			switch t := err.(type) {
			case apiError.MultipleError:
				apiutil.ErrBadRequest(gctx, t.APIError())
			default:
				apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
			}
			return
		}
		regisRes = NewRegistrationRes(*registration)
	}

	if r.LINEUserID == "" {
		logx.Error().Context(ctx).Msgf("unable to encrypt mid for drivers_registration: [%s]", regisRes.ID)
	}
	apiutil.Created(gctx, regisRes)
}

func createRegistrationRevisePayload(req *ReviseRegistrationReq, registration *model.DriverRegistration) rep.RegistrationRevisePayload {
	payload := rep.RegistrationRevisePayload{
		ID:        registration.ID.Hex(),
		Phone:     registration.Phone.String(),
		Region:    registration.Region,
		CreatedAt: timeutil.BangkokNow(),
	}

	dExp := req.DriverExperienced()
	if dExp != nil {
		payload.DriverExperiencedInformation = rep.DriverExperiencedInformationPayload{
			AppName:     dExp.AppName,
			Experienced: dExp.Experienced,
		}
	}

	return payload
}

// GetRegistrationByLineUid - deprecated
func (api *RegistrationAPI) GetRegistrationByLineUid(gctx *gin.Context) {
	api.doGetRegistration(gctx, gctx.Param(KeyLineUid))
}

func (api *RegistrationAPI) GetRegistration(gctx *gin.Context) {
	lineAccessToken := gctx.GetHeader(KeyLineAccessToken)
	if lineAccessToken == "" {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "x-line-access-token header is required"))
		return
	}
	profile, err := api.tokenVerifier.GetUserProfile(gctx, lineAccessToken)
	if err != nil {
		logrus.WithContext(gctx).Warnf("RegistrationAPI GetRegistration get user profile from line access token error: %v", err)
		apiutil.ErrInternalError(gctx, apiutil.NewFromString(absapi.ERRCODE_INTERNAL_ERROR, "get user profile from line access token error"))
		return
	}
	api.doGetRegistration(gctx, profile.UserID)
}

func (api *RegistrationAPI) doGetRegistration(gctx *gin.Context, lineUid string) {
	ctx := gctx.Request.Context()
	reg, err := api.RegistrationRepo.GetByLineUID(ctx, lineUid)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiError.ErrRegistrationNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		}
		return
	}

	apiutil.OK(gctx, NewRegistrationRes(*reg))
}

// UpdateRegistrationByLineUid - deprecated
func (api *RegistrationAPI) UpdateRegistrationByLineUid(gctx *gin.Context) {
	api.doUpdateRegistration(gctx, gctx.Param(KeyLineUid))
}

func (api *RegistrationAPI) UpdateRegistration(gctx *gin.Context) {
	lineAccessToken := gctx.GetHeader(KeyLineAccessToken)
	if lineAccessToken == "" {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absapi.ERRCODE_INVALID_REQUEST, "x-line-access-token header is required"))
		return
	}
	profile, err := api.tokenVerifier.GetUserProfile(gctx, lineAccessToken)
	if err != nil {
		logrus.WithContext(gctx).Warnf("RegistrationAPI UpdateRegistration get user profile from line access token error: %v", err)
		apiutil.ErrInternalError(gctx, apiutil.NewFromString(absapi.ERRCODE_INTERNAL_ERROR, "get user profile from line access token error"))
		return
	}
	api.doUpdateRegistration(gctx, profile.UserID)
}

func (api *RegistrationAPI) doUpdateRegistration(gctx *gin.Context, lineUid string) {
	ctx := gctx.Request.Context()
	registration, err := api.RegistrationRepo.GetByLineUID(ctx, lineUid)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiError.ErrRegistrationNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		}
		return
	}

	var isReviseRegisterFlow bool
	province, err := api.provinceRepo.GetByName(ctx, registration.InterestingProvince)
	if err != nil {
		isReviseRegisterFlow = false //fallback for pre-register without interesting region info
	} else {
		isReviseRegisterFlow = province.IsReviseRegisterFlow()
	}
	if isReviseRegisterFlow {
		api.updateReviseRegistration(gctx, registration)
		return
	}

	api.updateNormalRegistration(gctx, registration)
}

func (api *RegistrationAPI) updateReviseRegistration(gctx *gin.Context, registration *model.DriverRegistration) {
	req, apiErr := NewUpdateRegistrationReviseReq(gctx)
	if apiErr != nil {
		apiutil.ErrBadRequest(gctx, apiErr)
		return
	}

	ctx := gctx.Request.Context()
	files, err := req.Update(registration)
	if err != nil {
		switch t := err.(type) {
		case apiError.MultipleError:
			apiutil.ErrBadRequest(gctx, t.APIError())
		default:
			apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		}
		return
	}

	egs, _ := errgroup.WithContext(ctx)
	for _, rf := range files {
		rFile := rf
		egs.Go(func() error {
			defer safe.Recover()

			r, err := rFile.File.Open()
			if err != nil {
				return err
			}
			defer r.Close()

			prefix := fmt.Sprintf("%s/%s", registration.ID.Hex(), rFile.Name)
			switch rFile.Name {
			case DriverImageName:
				result, err := api.VosService.UploadToVOSCDN(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath, file.WithPublic)
				if err != nil {
					return err
				}
				registration.AvatarURL = fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, result.ID())
			case DriverLicenseImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.DriverLicense.PhotoURL = result.ID()
			case CitizenIDImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.CitizenIDCardPhotoURL = result.ID()
			case VehicleImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.PhotoURL = result.ID()
			case VehicleRegistrationImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.RegistrationPhotoURL = result.ID()
			case LegislationImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LegislationPhotoURL = result.ID()
			case LendingVehicleImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LendingVehiclePhotoURL = result.ID()
			}
			return nil
		})
	}

	if err := egs.Wait(); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	if err := api.RegistrationRepo.Update(ctx, registration); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, NewRegistrationRes(*registration))
}

func (api *RegistrationAPI) updateNormalRegistration(gctx *gin.Context, registration *model.DriverRegistration) {
	req, apiErr := NewUpdateRegistrationReq(gctx)
	if apiErr != nil {
		apiutil.ErrBadRequest(gctx, apiErr)
		return
	}

	ctx := gctx.Request.Context()
	files, err := req.Update(registration)
	if err != nil {
		switch t := err.(type) {
		case apiError.MultipleError:
			apiutil.ErrBadRequest(gctx, t.APIError())
		default:
			apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		}
		return
	}

	egs, _ := errgroup.WithContext(ctx)
	for _, rf := range files {
		rFile := rf
		egs.Go(func() error {
			defer safe.Recover()

			r, err := rFile.File.Open()
			if err != nil {
				return err
			}
			defer r.Close()

			prefix := fmt.Sprintf("%s/%s", registration.ID.Hex(), rFile.Name)
			switch rFile.Name {
			case DriverImageName:
				result, err := api.VosService.UploadToVOSCDN(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath, file.WithPublic)
				if err != nil {
					return err
				}
				registration.AvatarURL = fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, result.ID())
			case DriverLicenseImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.DriverLicense.PhotoURL = result.ID()
			case CitizenIDImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.CitizenIDCardPhotoURL = result.ID()
			case BookBankImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Banking.PhotoURL = result.ID()
			case VehicleImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.PhotoURL = result.ID()
			case VehicleRegistrationImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.RegistrationPhotoURL = result.ID()
			case LegislationImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LegislationPhotoURL = result.ID()
			case LendingVehicleImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LendingVehiclePhotoURL = result.ID()
			}

			return nil
		})
	}

	if err := egs.Wait(); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	/* Need to set InfoCompleted here because logic set info is set after call req.DriverRegistration() */
	registration.UpdateProfileStatus()

	if err := api.RegistrationRepo.Update(ctx, registration); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, NewRegistrationRes(*registration))
}

func (api *RegistrationAPI) registration(ctx context.Context, registration *model.DriverRegistration, files []RegistrationFile) (*model.DriverRegistration, error) {
	egs, _ := errgroup.WithContext(ctx)
	for _, rf := range files {
		rFile := rf
		egs.Go(func() error {
			defer safe.Recover()

			r, err := rFile.File.Open()
			if err != nil {
				return err
			}
			defer r.Close()

			prefix := fmt.Sprintf("%s/%s", registration.ID.Hex(), rFile.Name)
			switch rFile.Name {
			case DriverImageName:
				result, err := api.VosService.UploadToVOSCDN(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath, file.WithPublic)
				if err != nil {
					return err
				}
				registration.AvatarURL = fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, result.ID())
			case DriverLicenseImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.DriverLicense.PhotoURL = result.ID()
			case CitizenIDImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.CitizenIDCardPhotoURL = result.ID()
			case BookBankImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Banking.PhotoURL = result.ID()
			case VehicleImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.PhotoURL = result.ID()
			case VehicleRegistrationImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.RegistrationPhotoURL = result.ID()
			case LegislationImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LegislationPhotoURL = result.ID()
			case LendingVehicleImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LendingVehiclePhotoURL = result.ID()
			}

			return nil
		})
	}

	if err := egs.Wait(); err != nil {
		return nil, err
	}

	registration.IsDuplicatedCitizenID = api.RegistrationRepo.IsExistsByCitizenID(ctx, registration.CitizenID)
	val := api.driverRepo.IsExistsByCitizenID(ctx, registration.CitizenID)
	registration.IsDriverDuplicatedCitizenID = &val

	/* Need to set InfoCompleted here because logic set info is set after call req.DriverRegistration() */
	registration.UpdateProfileStatus()

	if err := api.RegistrationRepo.Insert(ctx, registration); err != nil {
		if err == repository.ErrDuplicateLineUid {
			return nil, errors.New(errRegistrationAlreaqdyExists)
		} else {
			return nil, err
		}
	}

	return registration, nil
}

func (api *RegistrationAPI) reviseRegistration(ctx context.Context, registration *model.DriverRegistration, files []RegistrationFile, province model.Province) (*model.DriverRegistration, bool, error) {
	egs, _ := errgroup.WithContext(ctx)
	for _, rf := range files {
		rFile := rf
		egs.Go(func() error {
			defer safe.Recover()

			r, err := rFile.File.Open()
			if err != nil {
				return err
			}
			defer r.Close()

			prefix := fmt.Sprintf("%s/%s", registration.ID.Hex(), rFile.Name)
			switch rFile.Name {
			case DriverImageName:
				result, err := api.VosService.UploadToVOSCDN(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath, file.WithPublic)
				if err != nil {
					return err
				}
				registration.AvatarURL = fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, result.ID())
			case DriverLicenseImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.DriverLicense.PhotoURL = result.ID()
			case CitizenIDImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.CitizenIDCardPhotoURL = result.ID()
			case LegislationImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LegislationPhotoURL = result.ID()
			case LendingVehicleImageName:
				result, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, rFile.ContentType, r, service.RegistrationFilePath)
				if err != nil {
					return err
				}
				registration.Vehicle.LendingVehiclePhotoURL = result.ID()
			}

			return nil
		})
	}

	if err := egs.Wait(); err != nil {
		return nil, false, err
	}

	registration.IsDuplicatedCitizenID = api.RegistrationRepo.IsExistsByCitizenID(ctx, registration.CitizenID)
	val := api.driverRepo.IsExistsByCitizenID(ctx, registration.CitizenID)
	registration.IsDriverDuplicatedCitizenID = &val
	registration.UpdateProfileStatus()

	if err := api.RegistrationRepo.Insert(ctx, registration); err != nil {
		if err == repository.ErrDuplicateLineUid {
			return nil, false, errors.New(errRegistrationAlreaqdyExists)
		} else {
			return nil, false, err
		}
	}

	whitelistPhone, err := api.whitelistPhoneRepo.GetByPhone(ctx, crypt.EncryptedString(registration.Phone.String()))
	if err != nil {
		whitelistPhone = model.NoneWhitelistPhone
		logrus.Errorf("Cannot send get driver whitelist. err=%s", err)
	}

	var isApproved bool
	switch {
	case province.IsWhitelistAutoApprove && whitelistPhone != model.NoneWhitelistPhone && !whitelistPhone.IsUsed:
		lineUID := registration.LineUID.String()
		if api.FeatureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEUserIDUsed.Name) {
			lineUID = registration.LINEUserID
		}
		otpSession, err := api.otpRepo.Load(ctx, lineUID)
		if err != nil {
			logrus.Errorf("couldn't load otpSession; err=%v", err)
			break
		}
		if !otpSession.VerifyCompletion(whitelistPhone.PhoneNumber.String()) {
			logrus.Error("failed otp completion verification")
			break
		}

		if err = api.autoApprove(ctx, registration, province); err != nil {
			if err == model.ErrDuplicateCitizenID {
				logrus.Warningf("auto approve: duplicate citizen id regisID: %s", registration.ID)
				return registration, false, nil
			}
			return nil, false, err
		}

		whitelistPhone.SetIsUsed(true)
		err = api.whitelistPhoneRepo.Update(ctx, whitelistPhone)
		if err != nil {
			logrus.Errorf("cannot update driver whitelist. err=%s", err)
		}

		isApproved = true
	case province.IsNormalAutoApprove && whitelistPhone == model.NoneWhitelistPhone:
		if err = api.autoApprove(ctx, registration, province); err != nil {
			if err == model.ErrDuplicateCitizenID {
				logrus.Warningf("auto approve: duplicate citizen id regisID: %s", registration.ID)
				return registration, false, nil
			}
			logrus.Errorf("auto approve error: %v", err)
			return nil, false, err
		}
		isApproved = true
	default:
	}

	return registration, isApproved, nil
}

func (api *RegistrationAPI) autoApprove(ctx context.Context, regis *model.DriverRegistration, province model.Province) error {

	if regis.IsDuplicatedCitizenID {
		return model.ErrDuplicateCitizenID
	}

	area, err := api.srvareaRepo.GetByRegion(ctx, province.RegionCode)
	if err != nil {
		return err
	}

	if !regis.CanSetCriminalStatus(model.CriminalStatusWhitelist) {
		return fmt.Errorf("couldn't set criminal status to whitelist")
	} else {
		if err := regis.SetCriminalStatus(model.CriminalStatusWhitelist); err != nil {
			return err
		}
	}

	regis.Region = string(area.Region)
	if err := regis.ReviseApprove(api.GlobalConfig.WhitelistPhotoURL); err != nil {
		return err
	}

	driver, err := api.DriverService.CreateDriver(ctx, regis, area)
	if err != nil {
		return fmt.Errorf("create driver err: %v", err)
	}

	dTxn := model.NewDriverTransaction(driver.DriverID)
	if err := api.driverTxnRepo.Create(ctx, dTxn); err != nil {
		return fmt.Errorf("couldn't create driver's wallet, err:%v; driverID:%s", err, driver.DriverID)
	}

	freeCredit := api.Config.AutoApproveFreeCredit
	if freeCredit != 0 {
		uuid := utils.GenerateUUID()
		tmpTxnInfo := dTxn.NewFreeCreditTransaction(crypt.EncryptedString(uuid), types.Money(freeCredit))
		txnInfos, err := dTxn.AddTransactionWithReference([]model.TransactionInfo{*tmpTxnInfo}, tmpTxnInfo.TransRefID)
		if err != nil {
			return errors.New("couldn't add free credit")
		}
		txns := model.NewTransactionsFromInfos(uuid, model.AdminTransactionChannel, model.PurchaseTransactionAction, model.SuccessTransactionStatus, txnInfos)
		if err := api.txnRepo.CreateAll(ctx, txns); err != nil {
			return model.ErrGroupTransactionInvalidTransitStatus
		} else {
			if err := api.driverTxnRepo.Update(ctx, dTxn); err != nil {
				logrus.Warnf("database desync: wrote transactions but couldn't write driver transaction. err=%v; driverID=%s", err, driver.DriverID)
				return errors.New("credit has been added to the system but not to driver's account")
			}
		}
	}

	if err := api.RegistrationRepo.Update(ctx, regis); err != nil {
		return err
	}

	if err := api.DriverService.AssignUobRefToDriver(ctx, driver); err != nil {
		logrus.Warnf("couldn't assign uob ref id to driver, err:%v; driverID:%s", err, driver.DriverID)
	}

	sendMessageTask := func(regis model.DriverRegistration) executor.Task {
		return func(ctx context.Context) error {
			if err := api.sendMessageContextBy(ctx, regis, DriverRegistrationApprovedMessage, api.FeatureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEUserIDUsed.Name)); err != nil {
				logrus.Warnf("Error while send message to driver after approve. driverLineId=%s", regis.LineUID.String())
			}
			return nil
		}
	}

	if err := api.taskExecutor.Run(sendMessageTask(*regis)); err != nil {
		logrus.Errorf("Error while execute task send message %s", err)
	}

	return nil
}

func (api *RegistrationAPI) sendMessageContextBy(ctx context.Context, regis model.DriverRegistration, message string, isLINEUserIDUsed bool) error {
	if isLINEUserIDUsed {
		return api.MsgSvc.SendMessageContext(ctx, regis.LINEUserID, DriverRegistrationApprovedMessage)
	}
	return api.MsgSvc.SendMessageContext(ctx, regis.LineUID.String(), DriverRegistrationApprovedMessage)
}

// extractLINEUserID - return a LINE UID, LINE MID and error
func (api *RegistrationAPI) extractLINEUserID(ctx context.Context, src string) (string, string, error) {
	if lineinternal.IsLINEMIDFormat(src) {
		// if LINE Channel is not encrypted yet, we still got mid from the respone
		// so we still need to encrypt the mid from a LINE's internal api
		lineEncryptedResp, encErr := api.lineInternalClient.EncryptMIDsInternal(ctx, src)
		var lineUID string
		if lineEncryptedResp != nil {
			lineUID = lineEncryptedResp.GetByMID(src)
		}
		if encErr != nil {
			logx.Error().Context(ctx).Err(encErr).Msg("unable to encrypt mid")
			lineUID = ""
		}
		return lineUID, src, encErr
	}

	if lineinternal.IsLINEUIDFormat(src) {
		// if LINE Channel is encrypted, we will got uid from the respone, instead of mid
		// we still need to store LINE MID to handle if something go wrong when set an encrypted key to a current channel ID
		// if nothing go wrong for a month or week, we will remove all LINE MID trace
		lineDecryptedResp, decErr := api.lineInternalClient.DecryptUIDsInternal(ctx, src)
		lineMID := lineDecryptedResp.GetByUID(src)
		if decErr != nil {
			logx.Error().Context(ctx).Err(decErr).Msg("unable to decrypt mid")
			lineMID = ""
		}
		return src, lineMID, decErr
	}

	safe.SentryErrorMessage("invalid user id format, not either LINE MID or LINE UID")
	return src, src, errors.New("invalid format for a LINE MID or UID")
}

// isDriverExistedCheckByLINEMID - if a driver is not existed after find by a UID
//
// then try to use a LINE MID stored in a Driver Registration instead
func (api *RegistrationAPI) isDriverExistedCheckByLINEMID(ctx context.Context, lineUserID string, driverReg *model.DriverRegistration) bool {
	// if the flag is not enabled then do nothing
	if !api.FeatureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsDriverLoginBackwardCompatibleEnabled.Name) {
		return false
	}

	// if the field `lineUserID` used for finding an existing driver is not in LINE UID format then do nothing
	if !lineinternal.IsLINEUIDFormat(lineUserID) {
		return false
	}

	// if the field `driverReg.LineUID` is not in LINE MID format then do nothing
	if !lineinternal.IsLINEMIDFormat(driverReg.LineUID.String()) {
		return false
	}

	return api.RegistrationRepo.IsExistsByLineUid(ctx, driverReg.LineUID)
}

func ProvideRegistrationAPI(
	cfg RegisterHandlerConfig,
	globalCfg config.GlobalConfig,
	vosSrv service.VOSService,
	driverService service.DriverServiceInterface,
	regisRepo repository.DriverRegistrationRepository,
	msgsvc messages.MessageServiceInterface,
	questionRepo repository.QuestionRepository,
	whitelistDriverRepo repository.WhitelistPhoneRepository,
	srvareaRepo repository.ServiceAreaRepository,
	driverRepo repository.DriverRepository,
	txnRepo repository.TransactionRepository,
	driverTxnRepo repository.DriverTransactionRepository,
	taskExecutor executor.TaskExecutor,
	provinceRepo repository.ProvinceRepository,
	otpRepo repository.OTPSessionRepo,
	repSvc rep.REPService,
	vosCfg file.VOSConfig,
	driverProfileReqCfg config.DriverProfileRequestConfig,
	tokenVerifier auth.TokenVerifier,
	lineInternalClient lineinternal.Client,
	featureFlagService featureflag.Service,
) *RegistrationAPI {
	return &RegistrationAPI{
		Config:              cfg,
		GlobalConfig:        globalCfg,
		VosService:          vosSrv,
		DriverService:       driverService,
		RegistrationRepo:    regisRepo,
		MsgSvc:              msgsvc,
		QuestionRepo:        questionRepo,
		whitelistPhoneRepo:  whitelistDriverRepo,
		srvareaRepo:         srvareaRepo,
		driverRepo:          driverRepo,
		txnRepo:             txnRepo,
		driverTxnRepo:       driverTxnRepo,
		taskExecutor:        taskExecutor,
		provinceRepo:        provinceRepo,
		otpRepo:             otpRepo,
		repSvc:              repSvc,
		vosCfg:              vosCfg,
		tokenVerifier:       tokenVerifier,
		driverProfileReqCfg: driverProfileReqCfg,
		lineInternalClient:  lineInternalClient,
		FeatureFlagService:  featureFlagService,
	}
}
