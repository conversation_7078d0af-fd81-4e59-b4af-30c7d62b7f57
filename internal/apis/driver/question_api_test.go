package driver

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestRegistrationAPI_ValidateQuestions(t *testing.T) {

	req := func(path1, path2 string, body AnswerRequest) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", "/v1/questions/"+path1+path2, testutil.JSON(body))
		gctx.Params = gin.Params{
			gin.Param{Key: "path1", Value: path1},
			gin.Param{Key: "path2", Value: path2},
		}
		return gctx, recorder
	}

	t.Run("should validate questions correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPI(ctrl)
		body := AnswerRequest{}
		gctx, recorder := req("fakeLineUid", "validate", body)

		deps.regisRepo.EXPECT().GetByLineUID(gomock.Any(), gomock.Any()).Return(&model.DriverRegistration{}, nil)
		deps.questionRepo.EXPECT().GetQuestionAnswers(gomock.Any()).Return([]model.QuestionAnswer{}, nil)
		deps.regisRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		api.ValidateQuestions(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should validate questions correctly (using line access token)", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPI(ctrl)
		body := AnswerRequest{}
		gctx, recorder := req("validate", "", body)

		deps.regisRepo.EXPECT().GetByLineUID(gomock.Any(), gomock.Any()).Return(&model.DriverRegistration{}, nil)
		deps.questionRepo.EXPECT().GetQuestionAnswers(gomock.Any()).Return([]model.QuestionAnswer{}, nil)
		deps.regisRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.tokenVerifier.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&line.Profile{
			UserID: "fakeLINEuid",
		}, nil)

		gctx.Request.Header.Add(KeyLineAccessToken, "fakeAccessToken")
		api.ValidateQuestions(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return 404 when validate questions with incorrect path", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, _ := newRegistrationAPI(ctrl)
		body := AnswerRequest{}
		gctx, recorder := req("", "", body)

		gctx.Request.Header.Add(KeyLineAccessToken, "fakeAccessToken")
		api.ValidateQuestions(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})
}

func TestRegistrationAPI_GetQuestions(t *testing.T) {

	t.Run("should get questions correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := testutil.TestRequestContext("GET", "/v1/questions", nil)
		gctx.Request.Header.Add(KeyLineAccessToken, "fakeAccessToken")

		deps.tokenVerifier.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&line.Profile{}, nil)
		deps.regisRepo.EXPECT().IsExistsByLineUid(gomock.Any(), gomock.Any()).Return(true)
		deps.questionRepo.EXPECT().GetQuestionAnswers(gomock.Any()).Return([]model.QuestionAnswer{}, nil)

		api.GetQuestions(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return bad request when get questions without line access token", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, _ := newRegistrationAPI(ctrl)
		gctx, recorder := testutil.TestRequestContext("GET", "/v1/questions", nil)

		api.GetQuestions(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return error when get user profile error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := testutil.TestRequestContext("GET", "/v1/questions", nil)
		gctx.Request.Header.Add(KeyLineAccessToken, "fakeAccessToken")
		deps.tokenVerifier.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))

		api.GetQuestions(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestRegistrationAPI_GetQuestionsByLineUid(t *testing.T) {

	t.Run("should get questions correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := testutil.TestRequestContext("GET", "/v1/questions/fakeLineUid", nil)
		deps.regisRepo.EXPECT().IsExistsByLineUid(gomock.Any(), gomock.Any()).Return(true)
		deps.questionRepo.EXPECT().GetQuestionAnswers(gomock.Any()).Return([]model.QuestionAnswer{}, nil)

		api.GetQuestionsByLineUid(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}
