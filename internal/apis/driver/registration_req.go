package driver

import (
	"errors"
	"mime/multipart"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gopkg.in/go-playground/validator.v9"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	apiValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

const (
	DriverImageName              = "avatar"
	DriverLicenseImageName       = "driver_license"
	CitizenIDImageName           = "citizen_id"
	VehicleImageName             = "vehicle"
	VehicleRegistrationImageName = "vehicle_registration"
	BookBankImageName            = "book_bank"
	LegislationImageName         = "legislation"
	LendingVehicleImageName      = "lending_vehicle"
	RegistrationFlowRevise       = "REVISE"

	LimitFileSize = 1 * 1024 * 1024 // MB
)

var referrerCodeRegEx *regexp.Regexp

func init() {
	var err error
	referrerCodeRegEx, err = regexp.Compile("^[a-zA-z0-9]{4,10}$")
	if err != nil {
		panic(err)
	}
}

// PreRegisterReq request struct for register driver
type PreRegisterReq struct {
	// Firstname of driver.
	Firstname string `json:"firstname" form:"firstname" binding:"required"`

	// Lastname of driver.
	Lastname string `json:"lastname" form:"lastname" binding:"required"`

	// Phone is telephone number of driver.
	Phone string `json:"phone" form:"phone" binding:"required"`

	// LineUID is LINE user id of driver.
	LineUID string `json:"lineUid" form:"lineUid" binding:"required"`

	// InterestingProvince is province that driver interesting.
	InterestingProvince string `json:"interestingProvince"`

	// AcceptedConsentVersion is consent version that driver accepted.
	AcceptedConsentVersion int64 `json:"acceptedConsentVersion"`
}

func (r PreRegisterReq) DriverRegistration() model.DriverRegistration {
	regis := model.NewDriverRegistration(crypt.NewLazyEncryptedString(r.LineUID))
	regis.Firstname = crypt.NewLazyEncryptedString(r.Firstname)
	regis.Lastname = crypt.NewLazyEncryptedString(r.Lastname)
	regis.Phone = crypt.NewLazyEncryptedString(r.Phone)
	regis.InterestingProvince = r.InterestingProvince
	regis.AcceptedConsentVersion = r.AcceptedConsentVersion
	return *regis
}

type RegistrationFile struct {
	File        *multipart.FileHeader
	ContentType string
	Name        string
}

type RegistrationReq interface {
	DriverRegistration() (*model.DriverRegistration, []RegistrationFile, error)
}

type NormalRegistrationReq struct {
	Register                 RegistrationPayload   `form:"registration" binding:"required"`
	DriverImage              *multipart.FileHeader `form:"driverImage" binding:"required"`
	DriverLicenseImage       *multipart.FileHeader `form:"driverLicenseImage" binding:"omitempty"`
	CitizenIDImage           *multipart.FileHeader `form:"citizenIdImage" binding:"omitempty"`
	VehicleImage             *multipart.FileHeader `form:"vehicleImage" binding:"omitempty"`
	VehicleRegistrationImage *multipart.FileHeader `form:"vehicleRegistrationImage" binding:"omitempty"`
	BookBankImage            *multipart.FileHeader `form:"bookBankImage" binding:"omitempty"`
	LegislationImage         *multipart.FileHeader `form:"legislationImage" binding:"omitempty"`
	LendingVehicleImage      *multipart.FileHeader `form:"lendingVehicleImage" binding:"omitempty"`
}

type ReviseRegistrationReq struct {
	Register            RegistrationRevisePayload `form:"registration" binding:"required"`
	DriverImage         *multipart.FileHeader     `form:"driverImage" binding:"required"`
	DriverLicenseImage  *multipart.FileHeader     `form:"driverLicenseImage" binding:"omitempty"`
	CitizenIDImage      *multipart.FileHeader     `form:"citizenIdImage" binding:"omitempty"`
	LegislationImage    *multipart.FileHeader     `form:"legislationImage" binding:"omitempty"`
	LendingVehicleImage *multipart.FileHeader     `form:"lendingVehicleImage" binding:"omitempty"`
}

func NewRegistrationReq(gctx *gin.Context) (RegistrationReq, *api.Error) {
	q := gctx.Request.URL.Query()
	flow := strings.ToUpper(q.Get("flow"))

	switch flow {
	case RegistrationFlowRevise:
		return NewReviseRegistrationReq(gctx)
	default:
		return NewNormalRegistrationReq(gctx)
	}
}

func translateError(gctx *gin.Context, err error) *api.Error {
	if validErrors, ok := err.(validator.ValidationErrors); ok {
		errs := apiErrors.NewMultipleError()
		for _, verr := range validErrors {
			errs.AddError(apiErrors.NewValidatorError(verr, apiValidator.GetTranslator(gctx.GetHeader("Accept-Language"))))
		}

		return errs.NewAPIError()
	}

	return apiErrors.ErrInvalidRequest(err)
}

func NewNormalRegistrationReq(gctx *gin.Context) (*NormalRegistrationReq, *api.Error) {
	var req NormalRegistrationReq
	if err := gctx.ShouldBind(&req); err != nil {
		return nil, translateError(gctx, err)
	}

	if err := gctx.ShouldBindHeader(&req); err != nil {
		return nil, translateError(gctx, err)
	}

	if gctx.GetHeader(KeyLineAccessToken) == "" && req.Register.LineUID == "" {
		var reqLineUID RegistrationLineUIDPayload
		if err := gctx.ShouldBind(&reqLineUID); err != nil {
			return nil, translateError(gctx, err)
		}
	}

	if req.Register.Phone == req.Register.EmergencyPhone {
		return nil, translateError(gctx, apiErrors.ErrInvalidEmergencyPhone())
	}

	return &req, nil
}

func NewReviseRegistrationReq(gctx *gin.Context) (*ReviseRegistrationReq, *api.Error) {
	var req ReviseRegistrationReq
	if err := gctx.ShouldBind(&req); err != nil {
		return nil, translateError(gctx, err)
	}

	if err := gctx.ShouldBindHeader(&req); err != nil {
		return nil, translateError(gctx, err)
	}

	if gctx.GetHeader(KeyLineAccessToken) == "" && req.Register.LineUID == "" {
		var reqLineUID RegistrationLineUIDPayload
		if err := gctx.ShouldBind(&reqLineUID); err != nil {
			return nil, translateError(gctx, err)
		}
	}

	return &req, nil
}

func (rr *NormalRegistrationReq) files() ([]RegistrationFile, apiErrors.MultipleError) {
	errs := apiErrors.NewMultipleError()
	if rr.DriverImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("driverImage", "file over size limit"))
	}
	if rr.DriverLicenseImage != nil && rr.DriverLicenseImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("driverLicenseImage", "file over size limit"))
	}
	if rr.CitizenIDImage != nil && rr.CitizenIDImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("citizenIdImage", "file over size limit"))
	}
	if rr.VehicleImage != nil && rr.VehicleImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("vehicleImage", "file over size limit"))
	}
	if rr.BookBankImage != nil && rr.BookBankImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("bookBankImage", "file over size limit"))
	}
	if rr.VehicleRegistrationImage != nil && rr.VehicleRegistrationImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("vehicleRegistrationImage", "file over size limit"))
	}

	if rr.LegislationImage != nil && rr.LegislationImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("legislationImage", "file over size limit"))
	}
	if rr.LendingVehicleImage != nil && rr.LendingVehicleImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("lendingVehicleImage", "file over size limit"))
	}

	if errs.HasError() {
		return nil, errs
	}

	var registrationFile []RegistrationFile

	registrationFile = append(registrationFile, RegistrationFile{
		File:        rr.DriverImage,
		Name:        DriverImageName,
		ContentType: file.GetContentType(rr.DriverImage.Header),
	})

	if rr.DriverLicenseImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.DriverLicenseImage,
			Name:        DriverLicenseImageName,
			ContentType: file.GetContentType(rr.DriverLicenseImage.Header),
		})
	}

	if rr.CitizenIDImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.CitizenIDImage,
			Name:        CitizenIDImageName,
			ContentType: file.GetContentType(rr.CitizenIDImage.Header),
		})
	}

	if rr.VehicleImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.VehicleImage,
			Name:        VehicleImageName,
			ContentType: file.GetContentType(rr.VehicleImage.Header),
		})
	}

	if rr.VehicleRegistrationImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.VehicleRegistrationImage,
			Name:        VehicleRegistrationImageName,
			ContentType: file.GetContentType(rr.VehicleRegistrationImage.Header),
		})
	}

	if rr.BookBankImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.BookBankImage,
			Name:        BookBankImageName,
			ContentType: file.GetContentType(rr.BookBankImage.Header),
		})
	}

	if rr.LegislationImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.LegislationImage,
			Name:        LegislationImageName,
			ContentType: file.GetContentType(rr.LegislationImage.Header),
		})
	}
	if rr.LendingVehicleImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.LendingVehicleImage,
			Name:        LendingVehicleImageName,
			ContentType: file.GetContentType(rr.LendingVehicleImage.Header),
		})
	}

	return registrationFile, nil
}

func (rr *ReviseRegistrationReq) files() ([]RegistrationFile, apiErrors.MultipleError) {
	errs := apiErrors.NewMultipleError()
	if rr.DriverImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("driverImage", "file over size limit"))
	}
	if rr.DriverLicenseImage != nil && rr.DriverLicenseImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("driverLicenseImage", "file over size limit"))
	}
	if rr.CitizenIDImage != nil && rr.CitizenIDImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("citizenIdImage", "file over size limit"))
	}
	if rr.LegislationImage != nil && rr.LegislationImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("legislationImage", "file over size limit"))
	}
	if rr.LendingVehicleImage != nil && rr.LendingVehicleImage.Size > LimitFileSize {
		errs.AddError(apiErrors.NewFieldError("lendingVehicleImage", "file over size limit"))
	}

	if errs.HasError() {
		return nil, errs
	}

	var registrationFile []RegistrationFile

	registrationFile = append(registrationFile, RegistrationFile{
		File:        rr.DriverImage,
		Name:        DriverImageName,
		ContentType: file.GetContentType(rr.DriverImage.Header),
	})

	if rr.DriverLicenseImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.DriverLicenseImage,
			Name:        DriverLicenseImageName,
			ContentType: file.GetContentType(rr.DriverLicenseImage.Header),
		})
	}

	if rr.CitizenIDImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.CitizenIDImage,
			Name:        CitizenIDImageName,
			ContentType: file.GetContentType(rr.CitizenIDImage.Header),
		})
	}

	if rr.LegislationImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.LegislationImage,
			Name:        LegislationImageName,
			ContentType: file.GetContentType(rr.LegislationImage.Header),
		})
	}

	if rr.LendingVehicleImage != nil {
		registrationFile = append(registrationFile, RegistrationFile{
			File:        rr.LendingVehicleImage,
			Name:        LendingVehicleImageName,
			ContentType: file.GetContentType(rr.LendingVehicleImage.Header),
		})
	}

	return registrationFile, nil
}

func (rr *NormalRegistrationReq) DriverRegistration() (*model.DriverRegistration, []RegistrationFile, error) {
	errs := apiErrors.NewMultipleError()
	model, rErrs := rr.Register.registration()
	if rErrs != nil {
		errs.Merge(rErrs)
	}

	files, fErrs := rr.files()
	if fErrs != nil {
		errs.Merge(fErrs)
	}

	if errs.HasError() {
		return nil, nil, errs
	} else {
		return model, files, nil
	}
}

func (rr *ReviseRegistrationReq) DriverRegistration() (*model.DriverRegistration, []RegistrationFile, error) {
	errs := apiErrors.NewMultipleError()
	model, rErrs := rr.Register.registration()
	if rErrs != nil {
		errs.Merge(rErrs)
	}

	files, fErrs := rr.files()
	if fErrs != nil {
		errs.Merge(fErrs)
	}

	if errs.HasError() {
		return nil, nil, errs
	} else {
		return model, files, nil
	}
}

func (rr *ReviseRegistrationReq) DriverExperienced() *DriverExperiencedInformation {
	return rr.Register.DriverExperiencedInformation
}

type UpdateRegistrationHeader struct {
	DeviceID string `header:"x-device-id"`
}

type UpdateRegistrationReq struct {
	Register                 UpdateRegistrationPayload `form:"registration" binding:"required"`
	DriverImage              *multipart.FileHeader     `form:"driverImage"`
	DriverLicenseImage       *multipart.FileHeader     `form:"driverLicenseImage"`
	CitizenIDImage           *multipart.FileHeader     `form:"citizenIdImage"`
	VehicleImage             *multipart.FileHeader     `form:"vehicleImage"`
	VehicleRegistrationImage *multipart.FileHeader     `form:"vehicleRegistrationImage"`
	BookBankImage            *multipart.FileHeader     `form:"bookBankImage"`
	LegislationImage         *multipart.FileHeader     `form:"legislationImage"`
	LendingVehicleImage      *multipart.FileHeader     `form:"lendingVehicleImage"`
	UpdateRegistrationHeader UpdateRegistrationHeader
}

func NewUpdateRegistrationReq(gctx *gin.Context) (*UpdateRegistrationReq, *api.Error) {
	var req UpdateRegistrationReq
	if err := gctx.ShouldBind(&req); err != nil {
		if validErrors, ok := err.(validator.ValidationErrors); ok {
			errs := apiErrors.NewMultipleError()

			for _, verr := range validErrors {
				errs.AddError(apiErrors.NewValidatorError(verr, apiValidator.GetTranslator(gctx.GetHeader("Accept-Language"))))
			}

			return nil, errs.NewAPIError()
		}
		return nil, apiErrors.ErrInvalidRequest(err)
	}

	if err := gctx.ShouldBindHeader(&req); err != nil {
		return nil, translateError(gctx, err)
	}

	if req.Register.Phone == req.Register.EmergencyPhone {
		return nil, apiErrors.ErrInvalidRequest(apiErrors.ErrInvalidEmergencyPhone())
	}

	return &req, nil
}

func (ur *UpdateRegistrationReq) files() ([]RegistrationFile, apiErrors.MultipleError) {
	multipleError := apiErrors.NewMultipleError()
	registrationFile := []RegistrationFile{}

	if ur.DriverImage != nil {
		if ur.DriverImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.DriverImage, Name: DriverImageName})
	}

	if ur.DriverLicenseImage != nil {
		if ur.DriverLicenseImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverLicenseImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.DriverLicenseImage, Name: DriverLicenseImageName})
	}

	if ur.CitizenIDImage != nil {
		if ur.CitizenIDImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("citizenIdImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.CitizenIDImage, Name: CitizenIDImageName})
	}

	if ur.VehicleImage != nil {
		if ur.VehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.VehicleImage, Name: VehicleImageName})
	}

	if ur.BookBankImage != nil {
		if ur.BookBankImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("bookBankImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.BookBankImage, Name: BookBankImageName})
	}

	if ur.VehicleRegistrationImage != nil {
		if ur.VehicleRegistrationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleRegistrationImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.VehicleRegistrationImage, Name: VehicleRegistrationImageName})
	}

	if ur.LegislationImage != nil {
		if ur.LegislationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("legislationImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.LegislationImage, Name: LegislationImageName})
	}

	if ur.LendingVehicleImage != nil {
		if ur.LendingVehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("lendingVehicleImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.LendingVehicleImage, Name: LendingVehicleImageName})
	}

	if multipleError.HasError() {
		return nil, multipleError
	}

	return registrationFile, nil
}

func (ur *UpdateRegistrationReq) Update(regis *model.DriverRegistration) ([]RegistrationFile, error) {
	if err := regis.SetStatus(model.RegistrationPending); err != nil {
		return nil, errors.New("Not allow to update")
	}
	errs := apiErrors.NewMultipleError()
	rErrs := ur.updateRegistration(regis)
	if rErrs != nil {
		errs.Merge(rErrs)
	}

	files, fErrs := ur.files()
	if fErrs != nil {
		errs.Merge(fErrs)
	}

	if errs.HasError() {
		return nil, errs
	} else {
		return files, nil
	}
}

/*
 Update driver registration flow is use when driver status is request update
*/

type UpdateRegistrationReviseReq struct {
	Register            UpdateRegistrationRevisePayload `form:"registration" binding:"required"`
	DriverImage         *multipart.FileHeader           `form:"driverImage"`
	DriverLicenseImage  *multipart.FileHeader           `form:"driverLicenseImage"`
	CitizenIDImage      *multipart.FileHeader           `form:"citizenIdImage"`
	LegislationImage    *multipart.FileHeader           `form:"legislationImage"`
	LendingVehicleImage *multipart.FileHeader           `form:"lendingVehicleImage"`
}

type UpdateRegistrationRevisePayload struct {
	Firstname                    string                        `json:"firstname" binding:"required,alphabet-thai"`
	Lastname                     string                        `json:"lastname" binding:"required,alphabet-thai"`
	Phone                        string                        `json:"phone" binding:"required,phone"`
	EmergencyPhone               string                        `json:"emergencyPhone" binding:"omitempty,phone"`
	CitizenID                    string                        `json:"citizenId" binding:"required,citizen-id"`
	CitizenIDExpiredDate         *time.Time                    `json:"citizenIdExpiredDate" binding:"omitempty"`
	DriverExperiencedInformation *DriverExperiencedInformation `json:"driverExperiencedInformation"`
	Vehicle                      VehiclePayload                `json:"vehicle" binding:"omitempty"`
}

func NewUpdateRegistrationReviseReq(gctx *gin.Context) (*UpdateRegistrationReviseReq, *api.Error) {
	var req UpdateRegistrationReviseReq
	if err := gctx.ShouldBind(&req); err != nil {
		if validErrors, ok := err.(validator.ValidationErrors); ok {
			errs := apiErrors.NewMultipleError()
			for _, verr := range validErrors {
				errs.AddError(apiErrors.NewValidatorError(verr, apiValidator.GetTranslator(gctx.GetHeader("Accept-Language"))))
			}

			return nil, errs.NewAPIError()
		}
		return nil, apiErrors.ErrInvalidRequest(err)
	}
	if req.Register.Phone == req.Register.EmergencyPhone {
		return nil, apiErrors.ErrInvalidRequest(apiErrors.ErrInvalidEmergencyPhone())
	}
	return &req, nil
}

func (ur *UpdateRegistrationReviseReq) files() ([]RegistrationFile, apiErrors.MultipleError) {
	multipleError := apiErrors.NewMultipleError()
	var registrationFile []RegistrationFile

	if ur.DriverImage != nil {
		if ur.DriverImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.DriverImage, Name: DriverImageName})
	}

	if ur.DriverLicenseImage != nil {
		if ur.DriverLicenseImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverLicenseImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.DriverLicenseImage, Name: DriverLicenseImageName})
	}

	if ur.CitizenIDImage != nil {
		if ur.CitizenIDImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("citizenIdImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.CitizenIDImage, Name: CitizenIDImageName})
	}

	if ur.LegislationImage != nil {
		if ur.LegislationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("legislationImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.LegislationImage, Name: LegislationImageName})
	}

	if ur.LendingVehicleImage != nil {
		if ur.LendingVehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("lendingVehicleImage", "file over size limit"))
		}
		registrationFile = append(registrationFile, RegistrationFile{File: ur.LendingVehicleImage, Name: LendingVehicleImageName})
	}

	if multipleError.HasError() {
		return nil, multipleError
	}

	return registrationFile, nil
}

func (ur *UpdateRegistrationReviseReq) Update(regis *model.DriverRegistration) ([]RegistrationFile, error) {
	if err := regis.SetStatus(model.RegistrationPending); err != nil {
		return nil, errors.New("Not allow to update")
	}
	errs := apiErrors.NewMultipleError()
	rErrs := ur.Register.updateRegistration(regis)
	if rErrs != nil {
		errs.Merge(rErrs)
	}

	files, fErrs := ur.files()
	if fErrs != nil {
		errs.Merge(fErrs)
	}

	if errs.HasError() {
		return nil, errs
	} else {
		return files, nil
	}
}

func (r *UpdateRegistrationRevisePayload) updateRegistration(regis *model.DriverRegistration) apiErrors.MultipleError {
	errs := apiErrors.NewMultipleError()

	regis.Firstname = crypt.NewLazyEncryptedString(r.Firstname)
	regis.Lastname = crypt.NewLazyEncryptedString(strings.TrimSpace(r.Lastname))
	regis.Phone = crypt.NewLazyEncryptedString(r.Phone)
	regis.EmergencyPhone = crypt.NewLazyEncryptedString(r.EmergencyPhone)
	regis.StrongEmergencyPhone = crypt.NewStrongEncryptedString(r.EmergencyPhone)
	regis.CitizenID = crypt.NewLazyEncryptedString(r.CitizenID)

	if r.CitizenIDExpiredDate != nil {
		regis.CitizenIDExpiredDate = r.CitizenIDExpiredDate
	}

	createErrorAppender := func(errs apiErrors.MultipleError) func(f func() apiErrors.MultipleError) {
		return func(f func() apiErrors.MultipleError) {
			if e := f(); e != nil {
				errs.Merge(e)
			}
		}
	}

	addErrIfExists := createErrorAppender(errs)

	addErrIfExists(func() apiErrors.MultipleError {
		if r.Vehicle.IsZero() {
			return nil
		}
		v, err := r.Vehicle.vehicle()
		v.PhotoURL = regis.Vehicle.PhotoURL
		v.RegistrationPhotoURL = regis.Vehicle.RegistrationPhotoURL
		regis.Vehicle = *v
		return err
	})

	if errs.HasError() {
		return errs
	} else {
		return nil
	}
}

/* end update registration revise flow */

type RegistrationHeader struct {
	Province    types.URLEncodedString `header:"x-district-province"`
	District    types.URLEncodedString `header:"x-district-area"`
	FullAddress types.URLEncodedString `header:"x-district-full-address"`
	LatLng      string                 `header:"x-district-lat-lng"`
	DeviceID    string                 `header:"x-device-id"`
}

func (r RegistrationHeader) RegistrationLocation() model.RegistrationLocation {
	//expected to fail silent
	location, _ := model.ParseLocation(r.LatLng)

	return model.RegistrationLocation{
		FullAddress: r.FullAddress.String(),
		District:    r.District.String(),
		Province:    r.Province.String(),
		Location:    location,
	}
}

type RegistrationPayload struct {
	Title                  string               `json:"title" binding:"required,driver-title"`
	Firstname              string               `json:"firstname" binding:"required,alphabet-thai"`
	Lastname               string               `json:"lastname" binding:"required,alphabet-thai"`
	Phone                  string               `json:"phone" binding:"required,phone"`
	EmergencyPhone         string               `json:"emergencyPhone" binding:"omitempty,phone"`
	CitizenID              string               `json:"citizenId" binding:"required,citizen-id"`
	CitizenIDExpiredDate   *time.Time           `json:"citizenIdExpiredDate" binding:"omitempty"`
	Birthday               time.Time            `json:"birthday" binding:"required,birthday"`
	LineUID                string               `json:"lineUid" binding:"omitempty"`
	InterestingProvince    string               `json:"interestingProvince" binding:"required"`
	ReferrerCode           string               `json:"referrerCode" binding:"omitempty"`
	Address                AddressPayload       `json:"address" binding:"omitempty"`
	Bank                   BankPayload          `json:"banking" binding:"omitempty"`
	DriverLicense          DriverLicensePayload `json:"driverLicense" binding:"omitempty"`
	Vehicle                VehiclePayload       `json:"vehicle" binding:"omitempty"`
	AcceptedConsentVersion int64                `json:"acceptedConsentVersion" binding:"required"`
	RegistrationHeader     RegistrationHeader
}

type RegistrationRevisePayload struct {
	Firstname                    string                        `json:"firstname" binding:"required,alphabet-thai"`
	Lastname                     string                        `json:"lastname" binding:"required,alphabet-thai"`
	Phone                        string                        `json:"phone" binding:"required,phone"`
	EmergencyPhone               string                        `json:"emergencyPhone" binding:"omitempty,phone"`
	CitizenID                    string                        `json:"citizenId" binding:"required,citizen-id"`
	CitizenIDExpiredDate         *time.Time                    `json:"citizenIdExpiredDate" binding:"omitempty"`
	Birthday                     time.Time                     `json:"birthday" binding:"required,birthday"`
	LineUID                      string                        `json:"lineUid" binding:"omitempty"`
	DriverExperiencedInformation *DriverExperiencedInformation `json:"driverExperiencedInformation"`
	InterestingProvince          string                        `json:"interestingProvince" binding:"required"`
	Vehicle                      VehiclePayload                `json:"vehicle" binding:"omitempty"`
	ReferrerCode                 string                        `json:"referrerCode" binding:"omitempty"`
	AcceptedConsentVersion       int64                         `json:"acceptedConsentVersion"`
	RegistrationHeader           RegistrationHeader
}

type RegistrationLineUIDPayload struct {
	LineUID string `json:"lineUid" binding:"required"`
}

type DriverExperiencedInformation struct {
	AppName     string `json:"appName"`
	Experienced string `json:"experienced"`
}

func (r *RegistrationRevisePayload) registration() (*model.DriverRegistration, apiErrors.MultipleError) {
	errs := apiErrors.NewMultipleError()

	model := model.NewDriverRegistration(crypt.NewLazyEncryptedString(r.LineUID))
	model.Firstname = crypt.NewLazyEncryptedString(r.Firstname)
	model.Lastname = crypt.NewLazyEncryptedString(strings.TrimSpace(r.Lastname))
	model.Phone = crypt.NewLazyEncryptedString(r.Phone)
	model.EmergencyPhone = crypt.NewLazyEncryptedString(r.EmergencyPhone)
	model.StrongEmergencyPhone = crypt.NewStrongEncryptedString(r.EmergencyPhone)
	model.CitizenID = crypt.NewLazyEncryptedString(r.CitizenID)
	model.AcceptedConsentVersion = r.AcceptedConsentVersion
	model.Birthday = &r.Birthday
	setReferrerCodeIfValid(model, r.ReferrerCode)
	model.InterestingProvince = r.InterestingProvince
	model.AcceptedConsentVersion = r.AcceptedConsentVersion
	model.RegistrationLocation = r.RegistrationHeader.RegistrationLocation()
	model.DeviceID = r.RegistrationHeader.DeviceID

	if r.CitizenIDExpiredDate != nil {
		model.CitizenIDExpiredDate = r.CitizenIDExpiredDate
	}

	v, err := r.Vehicle.reviseVehicle()
	if err != nil {
		errs.Merge(err)
	}

	if errs.HasError() {
		return nil, errs
	}

	model.Vehicle = *v
	return model, nil

}

func setReferrerCodeIfValid(reg *model.DriverRegistration, referrerCode string) {
	if referrerCodeRegEx.MatchString(referrerCode) {
		reg.ReferrerCode = referrerCode
	} else {
		logrus.Warnf("referrerCode=%s referrer code is invalid", referrerCode)
	}
}

func (r *RegistrationPayload) registration() (*model.DriverRegistration, apiErrors.MultipleError) {
	errs := apiErrors.NewMultipleError()

	model := model.NewDriverRegistration(crypt.NewLazyEncryptedString(r.LineUID))
	model.Firstname = crypt.NewLazyEncryptedString(r.Firstname)
	model.Lastname = crypt.NewLazyEncryptedString(strings.TrimSpace(r.Lastname))
	model.Title = crypt.NewLazyEncryptedString(r.Title)
	model.Phone = crypt.NewLazyEncryptedString(r.Phone)
	model.EmergencyPhone = crypt.NewLazyEncryptedString(r.EmergencyPhone)
	model.StrongEmergencyPhone = crypt.NewStrongEncryptedString(r.EmergencyPhone)
	model.CitizenID = crypt.NewLazyEncryptedString(r.CitizenID)
	model.Birthday = &r.Birthday
	if r.CitizenIDExpiredDate != nil {
		model.CitizenIDExpiredDate = r.CitizenIDExpiredDate
	}
	model.InterestingProvince = r.InterestingProvince
	model.AcceptedConsentVersion = r.AcceptedConsentVersion
	setReferrerCodeIfValid(model, r.ReferrerCode)
	model.RegistrationLocation = r.RegistrationHeader.RegistrationLocation()
	model.DeviceID = r.RegistrationHeader.DeviceID

	createErrorAppender := func(errs apiErrors.MultipleError) func(f func() apiErrors.MultipleError) {
		return func(f func() apiErrors.MultipleError) {
			if e := f(); e != nil {
				errs.Merge(e)
			}
		}
	}

	addErrIfExists := createErrorAppender(errs)

	addErrIfExists(func() apiErrors.MultipleError {
		address, err := r.Address.address()
		model.Address = *address
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		bank, err := r.Bank.banking()
		model.Banking = *bank
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		dl, err := r.DriverLicense.driverLicense()
		model.DriverLicense = *dl
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		v, err := r.Vehicle.vehicle()
		model.Vehicle = *v
		return err
	})

	if errs.HasError() {
		return nil, errs
	} else {
		return model, nil
	}
}

type UpdateRegistrationPayload struct {
	Title                  string               `json:"title" binding:"required,driver-title"`
	Firstname              string               `json:"firstname" binding:"required,alphabet-thai"`
	Lastname               string               `json:"lastname" binding:"required,alphabet-thai"`
	Phone                  string               `json:"phone" binding:"required,phone"`
	EmergencyPhone         string               `json:"emergencyPhone" binding:"omitempty,phone"`
	CitizenID              string               `json:"citizenId" binding:"required,citizen-id"`
	CitizenIDExpiredDate   *time.Time           `json:"citizenIdExpiredDate" binding:"omitempty"`
	Birthday               time.Time            `json:"birthday" binding:"required,birthday"`
	InterestingProvince    string               `json:"interestingProvince" binding:"required"`
	Address                AddressPayload       `json:"address" binding:"required"`
	Bank                   BankPayload          `json:"banking" binding:"required"`
	DriverLicense          DriverLicensePayload `json:"driverLicense" binding:"required"`
	Vehicle                VehiclePayload       `json:"vehicle" binding:"required"`
	AcceptedConsentVersion int64                `json:"acceptedConsentVersion" binding:"required"`
}

func (r *UpdateRegistrationReq) updateRegistration(regis *model.DriverRegistration) apiErrors.MultipleError {
	errs := apiErrors.NewMultipleError()

	regis.Title = crypt.NewLazyEncryptedString(r.Register.Title)
	regis.Firstname = crypt.NewLazyEncryptedString(r.Register.Firstname)
	regis.Lastname = crypt.NewLazyEncryptedString(strings.TrimSpace(r.Register.Lastname))
	regis.Phone = crypt.NewLazyEncryptedString(r.Register.Phone)
	regis.EmergencyPhone = crypt.NewLazyEncryptedString(r.Register.EmergencyPhone)
	regis.StrongEmergencyPhone = crypt.NewStrongEncryptedString(r.Register.EmergencyPhone)
	regis.CitizenID = crypt.NewLazyEncryptedString(r.Register.CitizenID)
	regis.Birthday = &r.Register.Birthday
	regis.InterestingProvince = r.Register.InterestingProvince
	regis.AcceptedConsentVersion = r.Register.AcceptedConsentVersion
	regis.DeviceID = r.UpdateRegistrationHeader.DeviceID

	if r.Register.CitizenIDExpiredDate != nil {
		regis.CitizenIDExpiredDate = r.Register.CitizenIDExpiredDate
	}

	createErrorAppender := func(errs apiErrors.MultipleError) func(f func() apiErrors.MultipleError) {
		return func(f func() apiErrors.MultipleError) {
			if e := f(); e != nil {
				errs.Merge(e)
			}
		}
	}

	addErrIfExists := createErrorAppender(errs)
	addErrIfExists(func() apiErrors.MultipleError {
		address, err := r.Register.Address.address()
		regis.Address = *address
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		bank, err := r.Register.Bank.banking()
		bank.PhotoURL = regis.Banking.PhotoURL
		regis.Banking = *bank
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		dl, err := r.Register.DriverLicense.driverLicense()
		dl.PhotoURL = regis.DriverLicense.PhotoURL
		regis.DriverLicense = *dl
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		v, err := r.Register.Vehicle.vehicle()
		v.PhotoURL = regis.Vehicle.PhotoURL
		v.RegistrationPhotoURL = regis.Vehicle.RegistrationPhotoURL
		regis.Vehicle = *v
		return err
	})

	if errs.HasError() {
		return errs
	} else {
		return nil
	}
}

type AddressPayload struct {
	HouseNumber string `json:"houseNumber" binding:"omitempty"`
	Moo         string `json:"moo" binding:"omitempty"`
	Subdistrict string `json:"subdistrict" binding:"omitempty"`
	District    string `json:"district" binding:"omitempty"`
	Province    string `json:"province" binding:"omitempty"`
	Zipcode     string `json:"zipcode" binding:"omitempty,len=5,integer"`
}

func (a *AddressPayload) address() (*model.Address, apiErrors.MultipleError) {
	return &model.Address{
		HouseNumber: crypt.NewLazyEncryptedString(a.HouseNumber),
		Moo:         crypt.NewLazyEncryptedString(a.Moo),
		Subdistrict: crypt.NewLazyEncryptedString(a.Subdistrict),
		District:    crypt.NewLazyEncryptedString(a.District),
		Province:    crypt.NewLazyEncryptedString(a.Province),
		Zipcode:     crypt.NewLazyEncryptedString(a.Zipcode),
	}, nil
}

type DriverLicensePayload struct {
	ID             string    `json:"id" binding:"omitempty"`
	ExpirationDate time.Time `json:"expirationDate" binding:"omitempty,gt"`
}

func (v *DriverLicensePayload) driverLicense() (*model.DriverLicenseInfo, apiErrors.MultipleError) {
	return &model.DriverLicenseInfo{
		ID:             crypt.NewLazyEncryptedString(v.ID),
		ExpirationDate: &v.ExpirationDate,
	}, nil
}

type VehiclePayload struct {
	RegistrationDate       time.Time `json:"registrationDate" binding:"omitempty,lt"`
	PlateNumber            string    `json:"plateNumber" binding:"omitempty"`
	LegislationExpiredDate time.Time `json:"legislationExpiredDate" binding:"omitempty"`
}

func (v VehiclePayload) IsZero() bool {
	return v == VehiclePayload{}
}

func (v *VehiclePayload) vehicle() (*model.VehicleInfo, apiErrors.MultipleError) {
	return &model.VehicleInfo{
		RegistrationDate:       &v.RegistrationDate,
		PlateNumber:            crypt.NewLazyEncryptedString(v.PlateNumber),
		LegislationExpiredDate: &v.LegislationExpiredDate,
	}, nil
}

func (v *VehiclePayload) reviseVehicle() (*model.VehicleInfo, apiErrors.MultipleError) {
	if v.PlateNumber == "" {
		errs := apiErrors.NewMultipleError()

		errs.AddError(apiErrors.NewFieldError("Plate Number", "is required"))
		return nil, errs
	}

	return &model.VehicleInfo{
		PlateNumber:            crypt.NewLazyEncryptedString(v.PlateNumber),
		LegislationExpiredDate: &v.LegislationExpiredDate,
	}, nil
}

type BankPayload struct {
	Account       string `json:"account" binding:"omitempty,integer"`
	BankName      string `json:"bankName" binding:"omitempty,bank"`
	AccountHolder string `json:"accountHolder" binding:"omitempty"`
}

func (b *BankPayload) banking() (*model.BankingInfo, apiErrors.MultipleError) {
	return &model.BankingInfo{
		Account:       crypt.NewLazyEncryptedString(b.Account),
		BankName:      crypt.NewLazyEncryptedString(b.BankName),
		AccountHolder: crypt.NewLazyEncryptedString(b.AccountHolder),
	}, nil
}
