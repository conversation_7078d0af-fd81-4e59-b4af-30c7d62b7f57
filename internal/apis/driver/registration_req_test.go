package driver

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http/httptest"
	"net/textproto"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/icrowley/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	apiValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

var validReq = func() NormalRegistrationReq {
	return NormalRegistrationReq{
		Register: RegistrationPayload{
			Title:               "นาย",
			Firstname:           "ทดลอง",
			Lastname:            "ภาษาไทย",
			Phone:               "**********",
			EmergencyPhone:      "**********",
			CitizenID:           "*************",
			Birthday:            time.Now().UTC().AddDate(-20, 0, 0),
			LineUID:             fake.DigitsN(10),
			ReferrerCode:        "1q2w3e4r5t",
			InterestingProvince: "region",
			Address: AddressPayload{
				HouseNumber: fake.Digits(),
				Moo:         fake.Digits(),
				Subdistrict: fake.CharactersN(10),
				District:    fake.City(),
				Province:    fake.State(),
				Zipcode:     fake.DigitsN(5),
			},
			Bank: BankPayload{
				Account:       "***********",
				BankName:      "KBANK",
				AccountHolder: fake.FullName(),
			},
			DriverLicense: DriverLicensePayload{
				ID:             "*********กขคABC",
				ExpirationDate: time.Now().UTC().Add(time.Hour * 24 * 30),
			},
			Vehicle: VehiclePayload{
				RegistrationDate: time.Now().UTC().Add(-1 * time.Hour * 24 * 30),
				PlateNumber:      "xxx-111111",
			},
			AcceptedConsentVersion: 1,
		},
		DriverImage: &multipart.FileHeader{
			Filename: "avatar",
			Size:     1000,
		},
		DriverLicenseImage: &multipart.FileHeader{
			Filename: "avatar",
			Size:     1000,
		},
		CitizenIDImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		VehicleImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		VehicleRegistrationImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		BookBankImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		LegislationImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		LendingVehicleImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
	}
}

var validUpdateReq = func() UpdateRegistrationReq {
	return UpdateRegistrationReq{
		Register: UpdateRegistrationPayload{
			Title:               "นาย",
			Firstname:           "ทดลอง",
			Lastname:            "ภาษาไทย",
			Phone:               "**********",
			EmergencyPhone:      "**********",
			CitizenID:           "*************",
			Birthday:            time.Now().UTC().AddDate(-23, 0, 0),
			InterestingProvince: "region",
			Address: AddressPayload{
				HouseNumber: fake.Digits(),
				Moo:         fake.Digits(),
				Subdistrict: fake.CharactersN(10),
				District:    fake.City(),
				Province:    fake.State(),
				Zipcode:     fake.DigitsN(5),
			},
			Bank: BankPayload{
				Account:       "***********",
				BankName:      "KBANK",
				AccountHolder: fake.FullName(),
			},
			DriverLicense: DriverLicensePayload{
				ID:             "*********",
				ExpirationDate: time.Now().UTC().Add(time.Hour * 24 * 30),
			},
			Vehicle: VehiclePayload{
				RegistrationDate: time.Now().UTC().Add(-1 * time.Hour * 24 * 30),
				PlateNumber:      "xxx-111111",
			},
			AcceptedConsentVersion: 1,
		},
		DriverLicenseImage: &multipart.FileHeader{
			Filename: "avatar",
			Size:     1000,
		},
		CitizenIDImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		VehicleImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		VehicleRegistrationImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		BookBankImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		LegislationImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
		LendingVehicleImage: &multipart.FileHeader{
			Filename: "avatar",
			Header:   nil,
			Size:     1000,
		},
	}
}

func TestRegistrationReq_DriverRegistration(t *testing.T) {
	testcases := []struct {
		name      string
		req       func() NormalRegistrationReq
		expectErr func() errors.MultipleError
	}{
		{
			name: "driver image image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.DriverImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("driverImage", "file over size limit"))
				return errs
			},
		},
		{
			name: "driver license image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.DriverLicenseImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("driverLicenseImage", "file over size limit"))
				return errs
			},
		},
		{
			name: "citizen image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.CitizenIDImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("citizenIdImage", "file over size limit"))
				return errs
			},
		},
		{
			name: "vehicle image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.VehicleImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("vehicleImage", "file over size limit"))
				return errs
			},
		},
		{
			name: "bookbank image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.BookBankImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("bookBankImage", "file over size limit"))
				return errs
			},
		},
		{
			name: "vehicle registration image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.VehicleRegistrationImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("vehicleRegistrationImage", "file over size limit"))
				return errs
			},
		},
		{
			name: "legislation image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.LegislationImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("legislationImage", "file over size limit"))
				return errs
			},
		},
		{
			name: "lending vehicle image size over 1MB",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.LendingVehicleImage.Size = LimitFileSize + 1
				return r
			},
			expectErr: func() errors.MultipleError {
				errs := errors.NewMultipleError()
				errs.AddError(errors.NewFieldError("lendingVehicleImage", "file over size limit"))
				return errs
			},
		},
	}

	for _, tc := range testcases {
		req := tc.req()
		_, _, errs := req.DriverRegistration()
		t.Log(errs)
		require.Equal(t, tc.expectErr(), errs, tc.name)
	}
}

func TestRegistrationReq_DriverRegistration_Not_requiredfield(t *testing.T) {
	testcases := []struct {
		name      string
		req       func() NormalRegistrationReq
		expectErr func() error
	}{
		{
			name: "not required field DriverLicenseImage",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.DriverLicenseImage = nil
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field CitizenIDImage",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.CitizenIDImage = nil
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field VehicleImage",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.VehicleImage = nil
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field BookBankImage",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.BookBankImage = nil
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field VehicleRegistrationImage",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.VehicleRegistrationImage = nil
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field Address",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.Register.Address = AddressPayload{}
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field DriverLicense",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.Register.DriverLicense = DriverLicensePayload{}
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field Bank",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.Register.Bank = BankPayload{}
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field Vehicle",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.Register.Vehicle = VehiclePayload{}
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field LegislationImage",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.LegislationImage = nil
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
		{
			name: "not required field LendingVehicleImage",
			req: func() NormalRegistrationReq {
				r := validReq()
				r.LendingVehicleImage = nil
				return r
			},
			expectErr: func() error {
				return nil
			},
		},
	}

	for _, tc := range testcases {
		req := tc.req()
		_, _, errs := req.DriverRegistration()
		require.Equal(t, tc.expectErr(), errs, tc.name)
	}

}

func TestRegistrationHeader(t *testing.T) {
	reg := RegistrationHeader{
		Province:    "Bangkok",
		District:    "Sathon",
		FullAddress: "1 Lumpini, Sathon, Bangkok",
		LatLng:      "13.1234,100.2345",
	}

	loc := reg.RegistrationLocation()
	assert.Equal(t, "Bangkok", loc.Province)
	assert.Equal(t, "Sathon", loc.District)
	assert.Equal(t, "1 Lumpini, Sathon, Bangkok", loc.FullAddress)
	assert.Equal(t, model.Location{Lat: 13.1234, Lng: 100.2345}, loc.Location)
}

func TestNewRegistrationReq(t *testing.T) {
	req := func(r RegistrationPayload) (*gin.Context, *httptest.ResponseRecorder) {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		defer w.Close()

		h := make(textproto.MIMEHeader)
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, "registration"))
		h.Set("Content-Type", "application/json")
		f, _ := w.CreatePart(h)
		io.Copy(f, testutil.JSON(r))

		f, _ = w.CreateFormFile("driverImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte("")))

		f, _ = w.CreateFormFile("driverLicenseImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte("")))

		f, _ = w.CreateFormFile("citizenIdImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte("")))

		f, _ = w.CreateFormFile("vehicleImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte("")))

		f, _ = w.CreateFormFile("vehicleRegistrationImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte("")))

		f, _ = w.CreateFormFile("bookBankImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte("")))

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/driver/registration", &b)
		ctx.Request.Header.Set("Content-Type", w.FormDataContentType())

		return ctx, recorder
	}

	setRegistrationHeader := func(gctx *gin.Context) {
		gctx.Request.Header.Set("X-District-Province", "Bangkok")
		gctx.Request.Header.Set("X-District-Area", "Sathon")
		gctx.Request.Header.Set("X-District-Full-Address", "1 Lumpini Soi 1, Sathon, Bangkok, Thailand, 10000")
		gctx.Request.Header.Set("X-District-Lat-Lng", "13.1234567, 100.2345678")
	}

	t.Run("valid request should have no error", func(tt *testing.T) {
		payload := validReq().Register
		gctx, _ := req(payload)

		_, errs := NewRegistrationReq(gctx)
		require.Nil(tt, errs)
	})

	t.Run("missing header for normal registration should has no effect", func(t *testing.T) {
		payload := validReq().Register
		gctx, _ := req(payload)

		reg, errs := NewRegistrationReq(gctx)
		require.Nil(t, errs)
		regis := reg.(*NormalRegistrationReq).Register
		assert.Equal(t, RegistrationHeader{}, regis.RegistrationHeader)
	})

	t.Run("bind header for normal registration", func(t *testing.T) {
		payload := validReq().Register
		gctx, _ := req(payload)
		setRegistrationHeader(gctx)

		reg, errs := NewRegistrationReq(gctx)
		require.Nil(t, errs)
		regis := reg.(*NormalRegistrationReq).Register
		assert.Equal(t, "Bangkok", regis.RegistrationHeader.Province.String())
		assert.Equal(t, "Sathon", regis.RegistrationHeader.District.String())
		assert.Equal(t, "1 Lumpini Soi 1, Sathon, Bangkok, Thailand, 10000", regis.RegistrationHeader.FullAddress.String())
		assert.Equal(t, "13.1234567, 100.2345678", regis.RegistrationHeader.LatLng)
		//sampling some normal field
		assert.Equal(t, "ทดลอง", regis.Firstname)
	})

	t.Run("bind header for revise registration", func(t *testing.T) {
		payload := validReq().Register
		gctx, _ := req(payload)
		setRegistrationHeader(gctx)
		gctx.Request.URL.RawQuery = "flow=" + RegistrationFlowRevise

		reg, errs := NewRegistrationReq(gctx)
		require.Nil(t, errs)
		regis := reg.(*ReviseRegistrationReq).Register
		assert.Equal(t, "Bangkok", regis.RegistrationHeader.Province.String())
		assert.Equal(t, "Sathon", regis.RegistrationHeader.District.String())
		assert.Equal(t, "1 Lumpini Soi 1, Sathon, Bangkok, Thailand, 10000", regis.RegistrationHeader.FullAddress.String())
		assert.Equal(t, "13.1234567, 100.2345678", regis.RegistrationHeader.LatLng)
		//sampling some normal field
		assert.Equal(t, "ทดลอง", regis.Firstname)
	})

	t.Run("bind header and decode the url encoded string", func(t *testing.T) {
		payload := validReq().Register
		gctx, _ := req(payload)
		gctx.Request.Header.Set("X-District-Province", "Krung+Thep+%E0%B8%81%E0%B8%A3%E0%B8%B8%E0%B8%87%E0%B9%80%E0%B8%97%E0%B8%9E")

		reg, errs := NewRegistrationReq(gctx)
		require.Nil(t, errs)
		regis := reg.(*NormalRegistrationReq).Register
		assert.Equal(t, "Krung Thep กรุงเทพ", regis.RegistrationHeader.Province.String())
	})

	t.Run("invalid request should error", func(tt *testing.T) {
		type expect struct {
			field string
			tag   string
		}
		testcases := []struct {
			name      string
			req       func(NormalRegistrationReq) NormalRegistrationReq
			expectErr expect
		}{
			{
				name: "lineuid is required",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.LineUID = ""
					return r
				},
				expectErr: expect{
					field: "lineUid",
					tag:   "required",
				},
			},
			{
				name: "title should be one of นาย นาง นางสาว",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.Title = "xxx"
					return r
				},
				expectErr: expect{
					field: "registration.title",
					tag:   "driver-title",
				},
			},
			{
				name: "firstname should be thai alphabet",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.Firstname = "ทดลองTest"
					return r
				},
				expectErr: expect{
					field: "registration.firstname",
					tag:   "alphabet-thai",
				},
			},
			{
				name: "lastname should be thai alphabet",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.Lastname = "ทดลองTest"
					return r
				},
				expectErr: expect{
					field: "registration.lastname",
					tag:   "alphabet-thai",
				},
			},
			{
				name: "phone should be (08|09|06)xxxxxxxx",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.Phone = "**********"
					return r
				},
				expectErr: expect{
					field: "registration.phone",
					tag:   "phone",
				},
			},
			{
				name: "bank should valid",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.Bank.BankName = "XXX"
					return r
				},
				expectErr: expect{
					field: "registration.banking.bankName",
					tag:   "bank",
				},
			},
			{
				name: "citizen id should valid",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.CitizenID = "*************"
					return r
				},
				expectErr: expect{
					field: "registration.citizenId",
					tag:   "citizen-id",
				},
			},
			{
				name: "account shoud contain number only",
				req: func(r NormalRegistrationReq) NormalRegistrationReq {
					r.Register.Bank.Account = "12345 ********"
					return r
				},
				expectErr: expect{
					field: "registration.banking.account",
					tag:   "integer",
				},
			},
		}

		for _, tc := range testcases {
			payload := tc.req(validReq()).Register
			gctx, _ := req(payload)
			_, errs := NewRegistrationReq(gctx)
			require.Error(tt, errs, tc.name)
			aa := errs.Info["errors"].([]map[string]interface{})
			require.Equal(tt, tc.expectErr.field, aa[0]["field"], tc.name)
			require.Equal(tt, tc.expectErr.tag, aa[0]["tag"], tc.name)
		}

	})
}

func TestUpdateRegistrationReq_Update(t *testing.T) {

	t.Run("cannot update if status is not request update", func(tt *testing.T) {
		req := validUpdateReq()
		regis := model.NewDriverRegistration(crypt.NewLazyEncryptedString("test-lineuid"))
		regis.Status = model.RegistrationApproved
		_, err := req.Update(regis)

		require.NotNil(tt, err)
	})

	t.Run("cannot update if file limit exceed", func(tt *testing.T) {
		req := validUpdateReq()
		req.DriverImage = &multipart.FileHeader{
			Filename: "avatar",
			Size:     LimitFileSize + 2000,
		}
		regis := model.NewDriverRegistration(crypt.NewLazyEncryptedString("test-lineuid"))
		regis.Status = model.RegistrationRequestedUpdate
		_, err := req.Update(regis)
		expectedError := errors.MultipleError{}
		expectedError.AddError(errors.NewFieldError("driverImage", "file over size limit"))
		require.Equal(tt, expectedError, err)
	})

	t.Run("update success", func(tt *testing.T) {
		req := validUpdateReq()
		regis := model.NewDriverRegistration(crypt.NewLazyEncryptedString("test-lineuid"))
		regis.AvatarURL = "content1"
		regis.Status = model.RegistrationRequestedUpdate
		regisFile, err := req.Update(regis)
		require.Nil(tt, err)
		require.Equal(tt, crypt.NewLazyEncryptedString("ทดลอง"), regis.Firstname)
		require.Equal(tt, crypt.NewLazyEncryptedString("**********"), regis.EmergencyPhone)
		require.Equal(tt, crypt.NewStrongEncryptedString("**********"), regis.StrongEmergencyPhone)
		require.Equal(tt, "content1", regis.AvatarURL)
		require.NotEmpty(tt, regisFile)
	})
}

func init() {
	binding.Validator = apiValidator.NewDefaultValidator()
}

func TestRegistrationWithReferrer(t *testing.T) {
	type fields struct {
		ReferrerCode string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "valid referrer code",
			fields: fields{
				ReferrerCode: "abdc12345",
			},
			want: "abdc12345",
		},
		{
			name: "too long referrer code",
			fields: fields{
				ReferrerCode: "abdc123309040945",
			},
			want: "",
		},
		{
			name: "too short referrer code",
			fields: fields{
				ReferrerCode: "ab3",
			},
			want: "",
		},
		{
			name: "empty referrer code",
			fields: fields{
				ReferrerCode: "",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RegistrationRevisePayload{
				ReferrerCode: tt.fields.ReferrerCode,
				Birthday:     time.Now().UTC().AddDate(-18, 0, 0),
				Vehicle: VehiclePayload{
					PlateNumber: "xx-1234",
				},
			}
			got, err := r.registration()
			require.False(t, err.HasError())
			require.Equal(t, tt.want, got.ReferrerCode)
		})

		t.Run(tt.name, func(t *testing.T) {
			r := &RegistrationPayload{
				Birthday:     time.Now().UTC().AddDate(-30, 0, 0),
				ReferrerCode: tt.fields.ReferrerCode,
				Vehicle: VehiclePayload{
					PlateNumber: "xx-1234",
				},
			}
			got, err := r.registration()
			require.False(t, err.HasError())
			require.Equal(t, tt.want, got.ReferrerCode)
		})
	}
}
