package driver

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/textproto"
	"net/url"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/golang/mock/gomock"
	"github.com/icrowley/fake"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	unleashtest "git.wndv.co/go/unleash/test"
	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	apiValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/auth/mock_auth"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal/mock_lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_unleash"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/executor"
	"git.wndv.co/lineman/fleet-distribution/internal/messages/mock_messages"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestRegistrationAPI_PreRegister(t *testing.T) {
	req := func(r PreRegisterReq) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/driver/register", testutil.JSON(r))
	}

	t.Run("driver got registered and send message to OA", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPIWithCfg(ctrl, RegisterHandlerConfig{
			ContactDays: 3,
		}, file.VOSConfig{
			CDNEndpoint: "https://cdn-dev.net",
		})

		request := PreRegisterReq{
			Firstname:           "สม",
			Lastname:            "ส่ง",
			LineUID:             "<line_uid>",
			InterestingProvince: "กรุงเทพมหานคร",
			Phone:               "0812223333",
		}

		gctx, recorder := req(request)

		ctx := gctx.Request.Context()
		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(request.LineUID)).
			Return(false)
		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, entity *model.DriverRegistration) error {
				require.Equal(tt, request.Firstname, entity.Firstname.String())
				require.Equal(tt, request.Lastname, entity.Lastname.String())
				require.Equal(tt, request.LineUID, entity.LineUID.String())
				require.Equal(tt, request.InterestingProvince, entity.InterestingProvince)
				require.Equal(tt, request.Phone, entity.Phone.String())
				require.Equal(tt, model.RegistrationRequestedUpdate, entity.Status)
				return nil
			})
		deps.messages.EXPECT().SendMessageContext(gomock.Any(), "<line_uid>", DriverRegisteredWelcomeMessage).Return(nil)
		api.PreRegister(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})

	t.Run("driver already registered", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPIWithCfg(ctrl, RegisterHandlerConfig{
			ContactDays: 3,
		}, file.VOSConfig{
			CDNEndpoint: "https://cdn-dev.net",
		})
		// deps.regisSvc.EXPECT().Register(gomock.Any(), gomock.Any()).Return(ErrDriverAlreadyRegistered)
		gctx, recorder := req(PreRegisterReq{
			Firstname: "สม",
			Lastname:  "ส่ง",
			LineUID:   "<line_uid>",
			Phone:     "0812223333",
		})
		ctx := gctx.Request.Context()
		deps.regisRepo.
			EXPECT().
			IsExistsByLineUid(ctx, gomock.Any()).
			Return(true)
		// deps.messages.EXPECT().SendMessageContext(gomock.Any(), gomock.Any(), gomock.Any()).Times(0)

		api.PreRegister(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)

		var err absapi.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, DriverAlreadyRegisteredMessage, err.Message)
	})

	t.Run("driver got archived status and send message to OA when interesting province is in llm", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPIWithCfg(ctrl, RegisterHandlerConfig{
			ContactDays:      3,
			LalamoveProvince: "กรุงเทพมหานคร,ชลบุรี,สมุทรปราการ",
		}, file.VOSConfig{
			CDNEndpoint: "https://cdn-dev.net",
		})

		request := PreRegisterReq{
			Firstname:           "สม",
			Lastname:            "ส่ง",
			LineUID:             "<line_uid>",
			InterestingProvince: "กรุงเทพมหานคร",
			Phone:               "0812223333",
		}

		gctx, recorder := req(request)

		ctx := gctx.Request.Context()
		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(request.LineUID)).
			Return(false)
		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, entity *model.DriverRegistration) error {
				require.Equal(tt, request.Firstname, entity.Firstname.String())
				require.Equal(tt, request.Lastname, entity.Lastname.String())
				require.Equal(tt, request.LineUID, entity.LineUID.String())
				require.Equal(tt, request.InterestingProvince, entity.InterestingProvince)
				require.Equal(tt, request.Phone, entity.Phone.String())
				require.Equal(tt, model.RegistrationArchived, entity.Status)
				return nil
			})
		deps.messages.EXPECT().SendMessageContext(gomock.Any(), "<line_uid>", DriverRegisteredLalamoveProvinceMessage).Return(nil)
		api.PreRegister(gctx)

		require.Equal(t, http.StatusOK, recorder.Code)
	})
}

func TestRegistrationAPI_Register(t *testing.T) {
	validRegistration := func() RegistrationPayload {
		return RegistrationPayload{
			Title:               "นาย",
			Firstname:           "ทดลอง",
			Lastname:            "ภาษาไทย",
			Phone:               "**********",
			EmergencyPhone:      "**********",
			CitizenID:           "*************",
			Birthday:            time.Now().UTC().AddDate(-25, 0, 0),
			LineUID:             fake.DigitsN(10),
			InterestingProvince: "region",
			Address: AddressPayload{
				HouseNumber: fake.Digits(),
				Moo:         fake.Digits(),
				Subdistrict: fake.CharactersN(10),
				District:    fake.City(),
				Province:    fake.State(),
				Zipcode:     fake.DigitsN(5),
			},
			Bank: BankPayload{
				Account:       "***********",
				BankName:      "KBANK",
				AccountHolder: fake.FullName(),
			},
			DriverLicense: DriverLicensePayload{
				ID:             "*********",
				ExpirationDate: time.Now().UTC().Add(time.Hour * 24 * 30),
			},
			Vehicle: VehiclePayload{
				RegistrationDate:       time.Now().UTC().Add(-1 * time.Hour * 24 * 30),
				PlateNumber:            "xxx-111111",
				LegislationExpiredDate: time.Now().UTC().AddDate(30, 0, 0),
			},
			AcceptedConsentVersion: 1,
		}
	}

	req := func(r RegistrationPayload, driverImgContent, driverLicenseImgContent, cititzenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent string) (*gin.Context, *httptest.ResponseRecorder) {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		defer w.Close()

		h := make(textproto.MIMEHeader)
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, "registration"))
		h.Set("Content-Type", "application/json")
		f, _ := w.CreatePart(h)
		io.Copy(f, testutil.JSON(r))

		f, _ = w.CreateFormFile("driverImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(driverImgContent)))

		f, _ = w.CreateFormFile("driverLicenseImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(driverLicenseImgContent)))

		f, _ = w.CreateFormFile("citizenIdImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(cititzenIDImgContent)))

		f, _ = w.CreateFormFile("vehicleImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(vehicleImgContent)))

		f, _ = w.CreateFormFile("vehicleRegistrationImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(vehicleRegistrationImgContent)))

		f, _ = w.CreateFormFile("bookBankImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(bankImgContent)))

		f, _ = w.CreateFormFile("legislationImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(legislationImgContent)))

		f, _ = w.CreateFormFile("lendingVehicleImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(lendingVehicleImgContent)))

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/driver/registrations", &b)
		ctx.Request.Header.Set("Content-Type", w.FormDataContentType())

		return ctx, recorder
	}

	t.Run("success register should return 201", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)

		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(VehicleRegistrationImageName, vehicleRegistrationImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)
		expectCallFile.Store(LegislationImageName, legislationImgContent)
		expectCallFile.Store(LendingVehicleImageName, lendingVehicleImgContent)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(regis.LineUID)).
			Return(false)

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(strs[1], prefix), nil
			}).
			Times(7)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				avatarKey = prefix
				return file.NewFile(prefix, prefix), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			Return(nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{RegionCode: "region_code"}, nil)

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LegislationPhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LendingVehiclePhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
	})

	t.Run("success register should return 201 (using line access token)", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		gctx.Request.Header.Set("x-line-access-token", "line access token")
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(VehicleRegistrationImageName, vehicleRegistrationImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)
		expectCallFile.Store(LegislationImageName, legislationImgContent)
		expectCallFile.Store(LendingVehicleImageName, lendingVehicleImgContent)

		lineUID := regis.LineUID
		regis.LineUID = ""
		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(lineUID)).
			Return(false)

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(strs[1], prefix), nil
			}).
			Times(7)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				avatarKey = prefix
				return file.NewFile(prefix, prefix), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			Return(nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{RegionCode: "region_code"}, nil)
		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(&line.Profile{UserID: lineUID}, nil)

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LegislationPhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LendingVehiclePhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
	})

	t.Run("register should return 500 when get line user id error (using line access token)", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		gctx.Request.Header.Set("x-line-access-token", "line access token")

		api, deps := newRegistrationAPI(ctrl)

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(nil, errors.New("error"))

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("register should return 400 when lookup province error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(VehicleRegistrationImageName, vehicleRegistrationImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{}, repository.ErrNotFound)

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("duplicate registration should return 400", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, _ := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(regis.LineUID)).
			Return(true)
		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{}, nil)
		api.Register(gctx)

		require.NotEmpty(tt, gctx.Errors.Errors())
	})

	t.Run("request invalid should return 400", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		regis := validRegistration()
		regis.LineUID = ""

		var (
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, _ := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)

		api, _ := newRegistrationAPI(ctrl)
		api.Register(gctx)

		require.NotEmpty(tt, gctx.Errors.Errors())
	})

	t.Run("duplicate citizen use set IsDuplicatedCitizenID true", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(regis.LineUID)).
			Return(false)

		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(file.NewFile("name", "location/file"), nil).
			Times(7)

		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(file.NewFile("name", "location/file"), nil)

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(true)

		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			Return(nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{RegionCode: "region_code"}, nil)

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual model.DriverRegistration
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, true, actual.IsDuplicatedCitizenID)
		require.NotEmpty(tt, actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LegislationPhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LendingVehiclePhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
	})

	t.Run("request with same phone and emergencyPhone should be return 400", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		regis := validRegistration()
		regis.Phone = "**********"
		regis.EmergencyPhone = "**********"

		var (
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, _ := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)

		api, _ := newRegistrationAPI(ctrl)
		api.Register(gctx)

		require.NotEmpty(tt, gctx.Errors.Errors())
	})

	t.Run("success register should return 201 via line access token with encryption", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		gctx.Request.Header.Set("x-line-access-token", "line access token")
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(VehicleRegistrationImageName, vehicleRegistrationImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)
		expectCallFile.Store(LegislationImageName, legislationImgContent)
		expectCallFile.Store(LendingVehicleImageName, lendingVehicleImgContent)

		lineUID := "uaaaaaaaaaabbbbbbbbbbcccccccccc12"

		deps.lineInternalClient.EXPECT().EncryptMIDsInternal(ctx, lineUID).
			Return(&lineinternal.EncryptMIDsResponse{
				UserIDs: map[string]string{
					"uaaaaaaaaaabbbbbbbbbbcccccccccc12": "Ulineuid",
				},
			}, nil)

		regis.LineUID = ""
		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(lineUID)).
			Return(false)

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(strs[1], prefix), nil
			}).
			Times(7)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				avatarKey = prefix
				return file.NewFile(prefix, prefix), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, entity *model.DriverRegistration) error {
				require.Equal(tt, "uaaaaaaaaaabbbbbbbbbbcccccccccc12", entity.LineUID.String())
				require.Equal(tt, "Ulineuid", entity.LINEUserID)
				return nil
			})

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{RegionCode: "region_code"}, nil)
		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(&line.Profile{UserID: lineUID}, nil)

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LegislationPhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LendingVehiclePhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
	})

	t.Run("success register should return 201 via line access token with decryption", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		gctx.Request.Header.Set("x-line-access-token", "line access token")
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(VehicleRegistrationImageName, vehicleRegistrationImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)
		expectCallFile.Store(LegislationImageName, legislationImgContent)
		expectCallFile.Store(LendingVehicleImageName, lendingVehicleImgContent)

		lineUID := "Uaaaaaaaaaabbbbbbbbbbcccccccccc12"

		deps.lineInternalClient.EXPECT().DecryptUIDsInternal(ctx, lineUID).
			Return(lineinternal.DecryptUIDsResponse{
				MIDs: map[string]string{
					"Uaaaaaaaaaabbbbbbbbbbcccccccccc12": "ulinemid",
				},
			}, nil)

		regis.LineUID = ""
		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(lineUID)).
			Return(false)

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(strs[1], prefix), nil
			}).
			Times(7)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				avatarKey = prefix
				return file.NewFile(prefix, prefix), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, entity *model.DriverRegistration) error {
				require.Equal(tt, "ulinemid", entity.LineUID.String())
				require.Equal(tt, "Uaaaaaaaaaabbbbbbbbbbcccccccccc12", entity.LINEUserID)
				return nil
			})

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{RegionCode: "region_code"}, nil)
		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(&line.Profile{UserID: lineUID}, nil)

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LegislationPhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LendingVehiclePhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
	})

	t.Run("success register should return 201 via line access token with decryption and retry checking", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		gctx.Request.Header.Set("x-line-access-token", "line access token")
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(VehicleRegistrationImageName, vehicleRegistrationImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)
		expectCallFile.Store(LegislationImageName, legislationImgContent)
		expectCallFile.Store(LendingVehicleImageName, lendingVehicleImgContent)

		deps.lineInternalClient.EXPECT().DecryptUIDsInternal(ctx, "Uaaaaaaaaaabbbbbbbbbbcccccccccc12").
			Return(lineinternal.DecryptUIDsResponse{
				MIDs: map[string]string{
					"Uaaaaaaaaaabbbbbbbbbbcccccccccc12": "uaaaaaaaaaabbbbbbbbbbcccccccccc12",
				},
			}, nil)

		regis.LineUID = ""
		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString("Uaaaaaaaaaabbbbbbbbbbcccccccccc12")).
			Return(false) // 1st finding

		deps.simpleUnleasher.SetEnabled(featureflag.IsDriverLoginBackwardCompatibleEnabled.Name, true)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString("uaaaaaaaaaabbbbbbbbbbcccccccccc12")).
			Return(false) // 2nd finding

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(strs[1], prefix), nil
			}).
			Times(7)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				avatarKey = prefix
				return file.NewFile(prefix, prefix), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, entity *model.DriverRegistration) error {
				require.Equal(tt, "uaaaaaaaaaabbbbbbbbbbcccccccccc12", entity.LineUID.String())
				require.Equal(tt, "Uaaaaaaaaaabbbbbbbbbbcccccccccc12", entity.LINEUserID)
				return nil
			})

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{RegionCode: "region_code"}, nil)
		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(&line.Profile{UserID: "Uaaaaaaaaaabbbbbbbbbbcccccccccc12"}, nil)

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LegislationPhotoURL)
		require.NotEmpty(tt, actual.Vehicle.LendingVehiclePhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
	})

	t.Run("duplicate registration should return 400 after retry checking", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)

		gctx, _ := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		gctx.Request.Header.Set("x-line-access-token", "line access token")
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(&line.Profile{UserID: "Uaaaaaaaaaabbbbbbbbbbcccccccccc12"}, nil)

		regis.LineUID = ""
		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString("Uaaaaaaaaaabbbbbbbbbbcccccccccc12")).
			Return(false) // 1st finding

		deps.lineInternalClient.EXPECT().DecryptUIDsInternal(ctx, "Uaaaaaaaaaabbbbbbbbbbcccccccccc12").
			Return(lineinternal.DecryptUIDsResponse{
				MIDs: map[string]string{
					"Uaaaaaaaaaabbbbbbbbbbcccccccccc12": "uaaaaaaaaaabbbbbbbbbbcccccccccc12",
				},
			}, nil)

		deps.simpleUnleasher.SetEnabled(featureflag.IsDriverLoginBackwardCompatibleEnabled.Name, true)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString("uaaaaaaaaaabbbbbbbbbbcccccccccc12")).
			Return(true) // 2nd finding

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{}, nil)
		api.Register(gctx)

		require.NotEmpty(tt, gctx.Errors.Errors())
	})

}

func TestRegistrationAPI_Get(t *testing.T) {
	req := func(uid string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", "/v1/registrations/"+uid, nil)
		gctx.Params = gin.Params{
			gin.Param{Key: KeyLineUid, Value: uid},
		}
		return gctx, recorder
	}

	reqWithToken := func(token string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", "/v1/registrations", nil)
		gctx.Request.Header.Set("x-line-access-token", token)
		return gctx, recorder
	}

	t.Run("success should return 200", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := req("id-1")
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, "id-1").
			Return(model.NewDriverRegistration(crypt.NewLazyEncryptedString("id-1")), nil)

		api.GetRegistrationByLineUid(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("success should return 200 (using line access token)", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := reqWithToken("id-1")

		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, "id-1").
			Return(model.NewDriverRegistration(crypt.NewLazyEncryptedString("id-1")), nil)

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(&line.Profile{UserID: "id-1"}, nil)

		api.GetRegistration(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return 500 when get line user id error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := reqWithToken("id-1")

		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, "id-1").Times(0)

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(nil, errors.New("error"))

		api.GetRegistration(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return 400 when get without line access token", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := reqWithToken("")

		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, "id-1").Times(0)

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Times(0)

		api.GetRegistration(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("not found should return 404", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := req("not-found")
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)
		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, "not-found").
			Return(nil, repository.ErrNotFound)

		api.GetRegistrationByLineUid(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})
}

func TestRegistrationAPI_UpdateRegistration(t *testing.T) {
	const FAKE_DEVICE_ID = "FAKE_DEVICE_ID"

	validRegistration := func() UpdateRegistrationPayload {
		return UpdateRegistrationPayload{
			Title:               "นาย",
			Firstname:           "ทดลอง",
			Lastname:            "ภาษาไทย",
			Phone:               "**********",
			EmergencyPhone:      "**********",
			CitizenID:           "*************",
			Birthday:            time.Now().UTC().AddDate(-25, 0, 0),
			InterestingProvince: "region",
			Address: AddressPayload{
				HouseNumber: fake.Digits(),
				Moo:         fake.Digits(),
				Subdistrict: fake.CharactersN(10),
				District:    fake.City(),
				Province:    fake.State(),
				Zipcode:     fake.DigitsN(5),
			},
			Bank: BankPayload{
				Account:       "***********",
				BankName:      "KBANK",
				AccountHolder: fake.FullName(),
			},
			DriverLicense: DriverLicensePayload{
				ID:             "*********",
				ExpirationDate: time.Now().UTC().Add(time.Hour * 24 * 30),
			},
			Vehicle: VehiclePayload{
				RegistrationDate: time.Now().UTC().Add(-1 * time.Hour * 24 * 30),
				PlateNumber:      "xxx-111111",
			},
			AcceptedConsentVersion: 1,
		}
	}

	req := func(id string, r UpdateRegistrationPayload, driverImgContent, driverLicenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent string) (*gin.Context, *httptest.ResponseRecorder) {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		defer w.Close()

		h := make(textproto.MIMEHeader)
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, "registration"))
		h.Set("Content-Type", "application/json")
		f, _ := w.CreatePart(h)
		io.Copy(f, testutil.JSON(r))

		if driverImgContent != "" {
			f, _ = w.CreateFormFile("driverImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(driverImgContent)))
		}

		if driverLicenseImgContent != "" {
			f, _ = w.CreateFormFile("driverLicenseImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(driverLicenseImgContent)))
		}

		if citizenIDImgContent != "" {
			f, _ = w.CreateFormFile("citizenIdImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(citizenIDImgContent)))
		}

		if vehicleImgContent != "" {
			f, _ = w.CreateFormFile("vehicleImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(vehicleImgContent)))
		}

		if vehicleRegistrationImgContent != "" {
			f, _ = w.CreateFormFile("vehicleRegistrationImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(vehicleRegistrationImgContent)))
		}

		if bankImgContent != "" {
			f, _ = w.CreateFormFile("bookBankImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(bankImgContent)))
		}

		if legislationImgContent != "" {
			f, _ = w.CreateFormFile("legislationImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(legislationImgContent)))
		}

		if lendingVehicleImgContent != "" {
			f, _ = w.CreateFormFile("lendingVehicleImage", fake.CharactersN(10))
			io.Copy(f, bytes.NewReader([]byte(lendingVehicleImgContent)))
		}

		testurl := ""
		if id == "" {
			testurl = fmt.Sprintf("/v1/driver/registrations")
		} else {
			testurl = fmt.Sprintf("/v1/driver/registrations/%s", id)
		}
		ctx, recorder := testutil.TestRequestContext("POST", testurl, &b)
		ctx.Params = gin.Params{
			{Key: KeyLineUid, Value: id},
		}
		ctx.Request.Header.Set("Content-Type", w.FormDataContentType())
		ctx.Request.Header.Set("x-device-id", FAKE_DEVICE_ID)

		return ctx, recorder
	}

	t.Run("should return 404 if registration not found", func(tt *testing.T) {
		var (
			regisId                       = "regis-1"
			regis                         = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
		)
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := req(regisId, regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, "", "")
		ctx := gctx.Request.Context()

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, regisId).
			Return(nil, repository.ErrNotFound)

		api.UpdateRegistrationByLineUid(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("should return 400 if update is invalid", func(tt *testing.T) {
		var (
			regisId                       = "regis-1"
			regisPayload                  = validRegistration()
			regisModel                    = model.NewDriverRegistration(crypt.NewLazyEncryptedString(regisId))
			driverImgContent              = "content-1"
			licenseImgContent             = "content-2"
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = "content-4"
			bankImgContent                = "content-5"
		)
		regisModel.Status = model.RegistrationApproved
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := req(regisId, regisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, "", "")
		ctx := gctx.Request.Context()

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, regisId).
			Return(regisModel, nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regisModel.InterestingProvince).
			Return(ProvinceWithNormalApproval, nil)

		api.UpdateRegistrationByLineUid(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("success update", func(tt *testing.T) {
		var (
			regisId                       = "regis-1"
			regisPayload                  = validRegistration()
			regisModel                    = model.NewDriverRegistration(crypt.NewLazyEncryptedString(regisId))
			driverImgContent              = "content-1"
			licenseImgContent             = ""
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = ""
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := req(regisId, regisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		ctx := gctx.Request.Context()

		regisModel.Status = model.RegistrationRequestedUpdate
		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)
		expectCallFile.Store(LegislationImageName, legislationImgContent)
		expectCallFile.Store(LendingVehicleImageName, lendingVehicleImgContent)

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, regisId).
			Return(regisModel, nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regisModel.InterestingProvince).
			Return(ProvinceWithNormalApproval, nil)

		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(prefix, prefix), nil
			}).
			Times(5)
		id := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, ops ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(prefix, prefix), nil
			})
		deps.regisRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		api.UpdateRegistrationByLineUid(gctx)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, id, actual.ID)
	})

	t.Run("success update (using line access token)", func(tt *testing.T) {
		var (
			regisId                       = "regis-1"
			regisPayload                  = validRegistration()
			regisModel                    = model.NewDriverRegistration(crypt.NewLazyEncryptedString(regisId))
			driverImgContent              = "content-1"
			licenseImgContent             = ""
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = ""
			bankImgContent                = "content-5"
			legislationImgContent         = "content-6"
			lendingVehicleImgContent      = "content-7"
		)
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := req("", regisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, legislationImgContent, lendingVehicleImgContent)
		gctx.Request.Header.Set("x-line-access-token", regisId)
		ctx := gctx.Request.Context()

		regisModel.Status = model.RegistrationRequestedUpdate
		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)
		expectCallFile.Store(VehicleImageName, vehicleImgContent)
		expectCallFile.Store(BookBankImageName, bankImgContent)
		expectCallFile.Store(LegislationImageName, legislationImgContent)
		expectCallFile.Store(LendingVehicleImageName, lendingVehicleImgContent)

		deps.regisRepo.EXPECT().
			GetByLineUID(ctx, regisId).
			Return(regisModel, nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regisModel.InterestingProvince).
			Return(ProvinceWithNormalApproval, nil)

		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(prefix, prefix), nil
			}).
			Times(5)
		id := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, ops ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(prefix, prefix), nil
			})
		deps.regisRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(&line.Profile{UserID: regisId}, nil)

		api.UpdateRegistration(gctx)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, id, actual.ID)
	})

	t.Run("should return 500 when update get line user id by access token error", func(tt *testing.T) {
		var (
			regisId                       = "regis-1"
			regisPayload                  = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = ""
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = ""
			bankImgContent                = "content-5"
		)
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := req("", regisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, "", "")
		gctx.Request.Header.Set("x-line-access-token", regisId)

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Return(nil, errors.New("error"))

		api.UpdateRegistration(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return 400 when update without line access token", func(tt *testing.T) {
		var (
			regisPayload                  = validRegistration()
			driverImgContent              = "content-1"
			licenseImgContent             = ""
			citizenIDImgContent           = "content-3"
			vehicleImgContent             = "content-4"
			vehicleRegistrationImgContent = ""
			bankImgContent                = "content-5"
		)
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newRegistrationAPI(ctrl)
		gctx, recorder := req("", regisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent, "", "")

		deps.tokenVerifier.EXPECT().GetUserProfile(gctx, gomock.Any()).Times(0)

		api.UpdateRegistration(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})
}

func TestRegistrationAPI_Register_Revise(t *testing.T) {
	t.Parallel()

	validRegistration := func() RegistrationRevisePayload {
		expDate := time.Now().UTC().AddDate(18, 0, 0)
		return RegistrationRevisePayload{
			Firstname:    "ทูริน",
			Lastname:     "ทูรัมบา",
			Phone:        "**********",
			CitizenID:    "*************",
			Birthday:     time.Now().UTC().AddDate(-18, 0, 0),
			ReferrerCode: "LMDAKQWBA",
			LineUID:      fake.DigitsN(10),
			DriverExperiencedInformation: &DriverExperiencedInformation{
				AppName:     "LALAMOVE",
				Experienced: "200 ปี",
			},
			InterestingProvince: "กรุงเทพ",
			Vehicle: VehiclePayload{
				PlateNumber: "2ธค-2020",
			},
			AcceptedConsentVersion: 1,
			CitizenIDExpiredDate:   &expDate,
		}
	}

	req := func(r RegistrationRevisePayload, driverImgContent, driverLicenseImgContent, cititzenIDImgContent string) (*gin.Context, *httptest.ResponseRecorder) {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		defer w.Close()

		h := make(textproto.MIMEHeader)
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, "registration"))
		h.Set("Content-Type", "application/json")
		f, _ := w.CreatePart(h)
		io.Copy(f, testutil.JSON(r))

		f, _ = w.CreateFormFile("driverImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(driverImgContent)))

		f, _ = w.CreateFormFile("driverLicenseImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(driverLicenseImgContent)))

		f, _ = w.CreateFormFile("citizenIdImage", fake.CharactersN(10))
		io.Copy(f, bytes.NewReader([]byte(cititzenIDImgContent)))

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/driver/registration", &b)
		ctx.Request.URL = &url.URL{
			RawQuery: "flow=revise",
		}
		ctx.Request.Header.Set("Content-Type", w.FormDataContentType())

		return ctx, recorder
	}

	t.Run("success register should return 201 with phone is in whitelist and auto approve", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPIWithCfg(ctrl, RegisterHandlerConfig{
			ContactDays:           3,
			AutoApproveFreeCredit: 100,
		}, file.VOSConfig{
			CDNEndpoint: "https://cdn-dev.net",
		})
		defer func() {
			deps.taskExector.Stop()
			ctrl.Finish()
		}()

		var (
			regis               = validRegistration()
			driverImgContent    = "content-1"
			licenseImgContent   = "content-2"
			citizenIDImgContent = "content-3"
		)
		registration, _ := regis.registration()

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent)
		ctx := gctx.Request.Context()

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(regis.LineUID)).
			Return(false)

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				pf := fmt.Sprintf("fleet_private/%s", prefix)
				return file.NewFile(pf, pf), nil
			}).
			Times(2)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, ops ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				pf := fmt.Sprintf("%s", prefix)
				avatarKey = prefix
				return file.NewFile(prefix, pf), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)
		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		phoneWhitelist := &model.WhitelistPhone{
			PhoneNumber: crypt.EncryptedString(regis.Phone),
			IsUsed:      false,
		}
		deps.whitelistPhoneRepo.EXPECT().
			GetByPhone(ctx, crypt.EncryptedString(regis.Phone)).
			Return(phoneWhitelist, nil)

		deps.otpRepo.EXPECT().
			Load(ctx, regis.LineUID).
			Return(model.OTPSession{
				PhoneNumber: regis.Phone,
				IsValidated: true,
			}, nil)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			Return(nil)

		region := model.MapProvincetoRegion(regis.InterestingProvince)
		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, region).
			Return(model.NewServiceArea(model.GenerateServiceAreaID(), "BKK"), nil)

		deps.driverSvc.EXPECT().
			CreateDriver(ctx, gomock.Any(), gomock.Any()).
			Return(model.NewDriver(*registration, model.RegionCode(region), crypt.NewLazyEncryptedString("REF_ID")), nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{IsWhitelistAutoApprove: true, RegionCode: "BKK", Name: regis.InterestingProvince}, nil)

		deps.driverTxnRepo.EXPECT().
			Create(ctx, gomock.Any()).
			Return(nil)

		deps.txnRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			Return(nil)

		deps.driverTxnRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.regisRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.whitelistPhoneRepo.EXPECT().
			Update(ctx, gomock.Any()).
			DoAndReturn(func(_ interface{}, phone *model.WhitelistPhone) error {
				require.True(tt, phoneWhitelist.IsUsed)
				return nil
			})

		deps.driverSvc.EXPECT().
			AssignUobRefToDriver(ctx, gomock.Any()).
			Return(nil)

		deps.messages.EXPECT().
			SendMessageContext(ctx, regis.LineUID, gomock.Any()).
			Return(nil)

		deps.repSvc.EXPECT().
			Publish(gomock.Any(), gomock.Any()).
			Return(nil).AnyTimes()

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
	})

	t.Run("success register should return 201 with phone is in whitelist and auto approve normal driver", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPIWithCfg(ctrl, RegisterHandlerConfig{
			ContactDays:           3,
			AutoApproveFreeCredit: 100,
		}, file.VOSConfig{
			CDNEndpoint: "https://cdn-dev.net",
		})
		defer func() {
			deps.taskExector.Stop()
			ctrl.Finish()
		}()

		var (
			regis               = validRegistration()
			driverImgContent    = "content-1"
			licenseImgContent   = "content-2"
			citizenIDImgContent = "content-3"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent)
		ctx := gctx.Request.Context()

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(regis.LineUID)).
			Return(false)

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(prefix, prefix), nil
			}).
			Times(2)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, ops ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				avatarKey = prefix
				return file.NewFile(prefix, prefix), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)
		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.whitelistPhoneRepo.EXPECT().
			GetByPhone(ctx, crypt.EncryptedString(regis.Phone)).
			Return(&model.WhitelistPhone{
				PhoneNumber: crypt.EncryptedString(regis.Phone),
				IsUsed:      false,
			}, nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{IsNormalAutoApprove: true, RegionCode: "BKK", Name: regis.InterestingProvince}, nil)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			Return(nil)

		deps.repSvc.EXPECT().
			Publish(gomock.Any(), gomock.Any()).
			Return(nil).AnyTimes()

		api.Register(gctx)

		require.Empty(tt, gctx.Errors.Errors())
		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
	})

	t.Run("success register should return 201 with phone is in whitelist but is not in revise-onboard province", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		api, deps := newRegistrationAPIWithCfg(ctrl, RegisterHandlerConfig{
			ContactDays:           3,
			AutoApproveFreeCredit: 100,
		}, file.VOSConfig{
			CDNEndpoint: "https://cdn-dev.net",
		})
		defer func() {
			deps.taskExector.Stop()
			ctrl.Finish()
		}()

		var (
			regis               = validRegistration()
			driverImgContent    = "content-1"
			licenseImgContent   = "content-2"
			citizenIDImgContent = "content-3"
		)

		gctx, recorder := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent)
		ctx := gctx.Request.Context()

		expectCallFile := sync.Map{}
		expectCallFile.Store(DriverImageName, driverImgContent)
		expectCallFile.Store(DriverLicenseImageName, licenseImgContent)
		expectCallFile.Store(CitizenIDImageName, citizenIDImgContent)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(regis.LineUID)).
			Return(false)

		id := ""
		deps.vosSvc.EXPECT().
			UploadTOVOSForInternal(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				return file.NewFile(prefix, prefix), nil
			}).
			Times(2)

		avatarKey := ""
		deps.vosSvc.EXPECT().
			UploadToVOSCDN(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, prefix string, contentType string, content io.Reader, path string, ops ...file.SaveOption) (*file.File, error) {
				strs := strings.Split(prefix, "/")
				id = strs[0]

				expectContent, ok := expectCallFile.Load(strs[1])

				require.True(tt, ok)
				b, _ := io.ReadAll(content)
				require.Equal(tt, expectContent, string(b))

				expectCallFile.Delete(prefix)
				avatarKey = prefix
				return file.NewFile(prefix, prefix), nil
			})

		deps.regisRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)
		deps.driverRepo.EXPECT().
			IsExistsByCitizenID(ctx, crypt.NewLazyEncryptedString(regis.CitizenID)).
			Return(false)

		deps.whitelistPhoneRepo.EXPECT().
			GetByPhone(ctx, crypt.EncryptedString(regis.Phone)).
			Return(&model.WhitelistPhone{
				PhoneNumber: crypt.EncryptedString(regis.Phone),
				IsUsed:      false,
			}, nil)

		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{IsWhitelistAutoApprove: false, IsNormalAutoApprove: false, RegionCode: "BKK", Name: regis.InterestingProvince}, nil)

		deps.regisRepo.EXPECT().
			Insert(ctx, gomock.Any()).
			Return(nil)

		deps.repSvc.EXPECT().
			Publish(gomock.Any(), gomock.Any()).AnyTimes()

		api.Register(gctx)

		require.Equal(tt, http.StatusCreated, recorder.Code)

		var actual RegistrationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.Equal(tt, id, actual.ID)
		require.Equal(tt, fmt.Sprintf("%s/%s", api.vosCfg.CDNEndpoint, avatarKey), actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
	})

	t.Run("duplicate registrantion should return 400", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			regis               = validRegistration()
			driverImgContent    = "content-1"
			licenseImgContent   = "content-2"
			citizenIDImgContent = "content-3"
		)

		gctx, _ := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent)
		ctx := gctx.Request.Context()

		api, deps := newRegistrationAPI(ctrl)

		deps.regisRepo.EXPECT().
			IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(regis.LineUID)).
			Return(true)
		deps.provinceRepo.EXPECT().
			GetByName(ctx, regis.InterestingProvince).
			Return(model.Province{}, nil)

		api.Register(gctx)

		require.NotEmpty(tt, gctx.Errors.Errors())
	})

	t.Run("request invalid should return 400", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		regis := validRegistration()
		regis.LineUID = ""

		var (
			driverImgContent    = "content-1"
			licenseImgContent   = "content-2"
			citizenIDImgContent = "content-3"
		)

		gctx, _ := req(regis, driverImgContent, licenseImgContent, citizenIDImgContent)

		api, _ := newRegistrationAPI(ctrl)
		api.Register(gctx)

		require.NotEmpty(tt, gctx.Errors.Errors())
	})
}

type registrationAPIDeps struct {
	vosSvc    *mock_service.MockVOSService
	driverSvc *mock_service.MockDriverServiceInterface
	regisRepo *mock_repository.MockDriverRegistrationRepository
	messages  *mock_messages.MockMessageServiceInterface

	whitelistPhoneRepo *mock_repository.MockWhitelistPhoneRepository
	srvareaRepo        *mock_repository.MockServiceAreaRepository
	driverRepo         *mock_repository.MockDriverRepository
	txnRepo            *mock_repository.MockTransactionRepository
	driverTxnRepo      *mock_repository.MockDriverTransactionRepository
	taskExector        executor.TaskExecutor
	provinceRepo       *mock_repository.MockProvinceRepository
	otpRepo            *mock_repository.MockOTPSessionRepo
	repSvc             *mock_rep.MockREPService
	tokenVerifier      *mock_auth.MockTokenVerifier
	questionRepo       *mock_repository.MockQuestionRepository
	lineInternalClient *mock_lineinternal.MockClient
	simpleUnleasher    *unleashtest.SimpleUnleasher
	unleashAdmin       *mock_unleash.MockAdmin
}

func newRegistrationAPI(ctrl *gomock.Controller) (*RegistrationAPI, *registrationAPIDeps) {
	return newRegistrationAPIWithCfg(ctrl, RegisterHandlerConfig{
		ContactDays: 3,
	}, file.VOSConfig{
		CDNEndpoint: "https://cdn-dev.net",
	})
}

func newRegistrationAPIWithCfg(ctrl *gomock.Controller, cfg RegisterHandlerConfig, vosCfg file.VOSConfig) (*RegistrationAPI, *registrationAPIDeps) {
	vosSvc := mock_service.NewMockVOSService(ctrl)
	regisRepo := mock_repository.NewMockDriverRegistrationRepository(ctrl)
	driverSvc := mock_service.NewMockDriverServiceInterface(ctrl)
	msgSvc := mock_messages.NewMockMessageServiceInterface(ctrl)
	questionRepo := mock_repository.NewMockQuestionRepository(ctrl)

	whitelistPhoneRepo := mock_repository.NewMockWhitelistPhoneRepository(ctrl)
	srvareaRepo := mock_repository.NewMockServiceAreaRepository(ctrl)
	driverRepo := mock_repository.NewMockDriverRepository(ctrl)
	txnRepo := mock_repository.NewMockTransactionRepository(ctrl)
	driverTxnRepo := mock_repository.NewMockDriverTransactionRepository(ctrl)
	taskExec := executor.NewLocalTaskExecutor()
	provinceRepo := mock_repository.NewMockProvinceRepository(ctrl)
	otpRepo := mock_repository.NewMockOTPSessionRepo(ctrl)
	repSvc := mock_rep.NewMockREPService(ctrl)
	tokenVerifier := mock_auth.NewMockTokenVerifier(ctrl)
	globalConfig := config.GlobalConfig{WhitelistPhotoURL: []string{""}}
	driverProfReqCfg := config.DriverProfileRequestConfig{
		EnableRequireDriverLegislationImageExpireDateFlag: true,
		EnableRequireDriverIdCardImageExpireDateFlag:      true,
	}
	lineInternalClient := mock_lineinternal.NewMockClient(ctrl)
	simpleUnleasher := unleashtest.ProvideSimpleUnleasher()
	unleashAdmin := mock_unleash.NewMockAdmin(ctrl)

	deps := &registrationAPIDeps{
		vosSvc:             vosSvc,
		driverSvc:          driverSvc,
		regisRepo:          regisRepo,
		messages:           msgSvc,
		whitelistPhoneRepo: whitelistPhoneRepo,
		srvareaRepo:        srvareaRepo,
		driverRepo:         driverRepo,
		txnRepo:            txnRepo,
		driverTxnRepo:      driverTxnRepo,
		taskExector:        taskExec,
		provinceRepo:       provinceRepo,
		otpRepo:            otpRepo,
		repSvc:             repSvc,
		tokenVerifier:      tokenVerifier,
		questionRepo:       questionRepo,
		lineInternalClient: lineInternalClient,
		simpleUnleasher:    simpleUnleasher,
		unleashAdmin:       unleashAdmin,
	}

	return ProvideRegistrationAPI(cfg, globalConfig, vosSvc, driverSvc, regisRepo, msgSvc, questionRepo, whitelistPhoneRepo, srvareaRepo, driverRepo, txnRepo, driverTxnRepo, taskExec, provinceRepo, otpRepo, repSvc, vosCfg, driverProfReqCfg, tokenVerifier, lineInternalClient, featureflag.NewFeatureFlagService(deps.simpleUnleasher, deps.unleashAdmin)), deps
}

func init() {
	binding.Validator = apiValidator.NewDefaultValidator()
}
