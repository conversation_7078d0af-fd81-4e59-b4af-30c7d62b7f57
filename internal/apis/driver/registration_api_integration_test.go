//go:build integration_test
// +build integration_test

package driver_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"

	"github.com/icrowley/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

var (
	lineUID                       = "line-uid"
	regis                         = createRegistrationPayload(lineUID)
	updateRegisPayload            = createUpdateRegistrationPayload()
	driverImgContent              = "content-1"
	licenseImgContent             = "content-2"
	citizenIDImgContent           = "content-3"
	vehicleImgContent             = "content-4"
	vehicleRegistrationImgContent = "content-5"
	bankImgContent                = "content-6"
	ctx                           = context.Background()
	fakeDeviceId                  = "FAKE_DEVICE_ID"
)

func TestIntegrationRegistrationAPI_PreRegister(t *testing.T) {
	t.Parallel()
	t.Run("driver got registered and send message to OA", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		gctx := createPreRegisterReq(driver.PreRegisterReq{
			Firstname:           "สม",
			Lastname:            "ส่ง",
			LineUID:             lineUID,
			InterestingProvince: "อยุธยา",
			Phone:               "**********",
		})

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusOK)
		container.StubMessageService.AssertMessageSent(tt, lineUID, driver.DriverRegisteredWelcomeMessage)
		regisRepo := container.DriverRegistrationRepository
		assert.Equal(tt, true, regisRepo.IsExistsByLineUid(ctx, crypt.NewLazyEncryptedString(lineUID)))
		regist, err := regisRepo.GetByLineUID(ctx, lineUID)
		assert.NoError(tt, err)
		assert.Equal(tt, "สม", regist.Firstname.String())
		assert.Equal(tt, "ส่ง", regist.Lastname.String())
		assert.Equal(tt, "**********", regist.Phone.String())
	})

	t.Run("driver already registered", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		gctx := createPreRegisterReq(driver.PreRegisterReq{
			Firstname: "สม",
			Lastname:  "ส่ง",
			LineUID:   "uid_incomplete_preregistration",
			Phone:     "**********",
		})

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
		var apiErr absapi.Error
		gctx.DecodeJSONResponse(&apiErr)
		require.Equal(tt, driver.DriverAlreadyRegisteredMessage, apiErr.Message)
	})

	t.Run("driver got archived status and send message to OA when interesting Province is in llm", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		gctx := createPreRegisterReq(driver.PreRegisterReq{
			Firstname:           "สม",
			Lastname:            "ส่ง",
			LineUID:             lineUID,
			Phone:               "**********",
			InterestingProvince: "กรุงเทพมหานคร",
		})

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusOK)
		container.StubMessageService.AssertMessageSent(tt, lineUID, driver.DriverRegisteredLalamoveProvinceMessage)
	})
}

func TestIntegrationRegistrationAPI_Register(t *testing.T) {
	t.Parallel()
	t.Run("success register should return 201", func(tt *testing.T) {
		container := ittest.NewContainer(tt)
		gctx := createRegistrationRequest(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusCreated)
		var actual driver.RegistrationRes
		gctx.DecodeJSONResponse(&actual)
		d, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(tt, err)
		require.Equal(tt, false, actual.IsDuplicatedCitizenID)
		require.NotContains(tt, gctx.ResponseRecorder.Body.String(), "device_id")
		require.NotEmpty(tt, actual.ID)
		require.NotEmpty(tt, actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
		require.Equal(tt, "BKK", d.Region)
		require.Equal(tt, fakeDeviceId, d.DeviceID)
		assertDriverRegistrationLocation(t, d)
	})

	t.Run("success register should return 201 without x-device-id header", func(tt *testing.T) {
		container := ittest.NewContainer(tt)
		gctx := createRegistrationRequest(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)
		gctx.GinCtx().Request.Header.Del("x-device-id")

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusCreated)

		var actual driver.RegistrationRes
		gctx.DecodeJSONResponse(&actual)
		d, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(tt, err)
		require.Equal(tt, "", d.DeviceID)
		assertDriverRegistrationLocation(t, d)
	})

	t.Run("invalid interesting province should error", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		regis.InterestingProvince = "not existing"
		gctx := createRegistrationRequest(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
		var actual map[string]interface{}
		gctx.DecodeJSONResponse(&actual)
		require.Equal(tt, "interesting province is not found", actual["message"])
	})

	t.Run("duplicate registration should return 400", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		payload := createRegistrationPayload("uid_incomplete_preregistration")
		fmt.Printf("%s\n", testutil.JSON(payload))
		gctx := createRegistrationRequest(payload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
	})

	t.Run("request invalid should return 400", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		regis := createRegistrationPayload("")
		gctx := createRegistrationRequest(regis, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
	})

	t.Run("register with duplicate citizen id must set IsDuplicatedCitizenID to be true", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		req1 := createRegistrationPayload("line-uid-1")
		req1.CitizenID = "*************"
		req2 := createRegistrationPayload("line-uid-2")
		req2.CitizenID = "*************"
		registerDriver(tt, container, req1)
		gctx := createRegistrationRequest(req2, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusCreated)
		var actual model.DriverRegistration
		gctx.DecodeJSONResponse(&actual)
		require.Equal(tt, true, actual.IsDuplicatedCitizenID)
		require.NotEmpty(tt, actual.AvatarURL)
		require.NotEmpty(tt, actual.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, actual.DriverLicense.PhotoURL)
		require.NotEmpty(tt, actual.Vehicle.PhotoURL)
		require.NotEmpty(tt, actual.Banking.PhotoURL)
	})
}

func TestRegistrationAPI_Get(t *testing.T) {
	t.Parallel()
	t.Run("success should return 200", func(tt *testing.T) {
		container := ittest.NewContainer(tt)
		tt.Parallel()
		gctx := createGetRegistrationRequest("uid_incomplete_preregistration")

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusOK)
		require.NotContains(tt, gctx.ResponseRecorder.Body.String(), "device_id")
	})

	t.Run("not found should return 404", func(tt *testing.T) {
		container := ittest.NewContainer(tt)
		tt.Parallel()
		gctx := createGetRegistrationRequest("not-found")

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusNotFound)
	})
}

func TestIntegrationRegistrationAPI_UpdateRegistration(t *testing.T) {
	t.Parallel()
	t.Run("should return 404 if registration not found", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		gctx := createUpdateRegistrationReq("not-existing-line-uid", updateRegisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusNotFound)
	})

	t.Run("should return 400 if register status is pending", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		gctx := createUpdateRegistrationReq("uid_pending_info_complete", updateRegisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
	})

	t.Run("should return 400 if register status is approved", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		gctx := createUpdateRegistrationReq("uid_approved", updateRegisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
	})

	t.Run("should return 200 if register status is pending but info incomplete", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		lineUID := "uid_incomplete_preregistration"
		driverRegisModel, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(tt, err)
		gctx := createUpdateRegistrationReq(lineUID, updateRegisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusOK)
		var actual driver.RegistrationRes
		gctx.DecodeJSONResponse(&actual)
		assert.Equal(tt, driverRegisModel.ID.Hex(), actual.ID)
		assert.Equal(tt, updateRegisPayload.Firstname, actual.Firstname)
		assert.Equal(tt, updateRegisPayload.Lastname, actual.Lastname)
		assert.Equal(tt, updateRegisPayload.CitizenID, actual.CitizenID)
		assert.Equal(tt, updateRegisPayload.AcceptedConsentVersion, actual.AcceptedConsentVersion)
		driverRegisModel, err = container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(tt, err)
		assert.Equal(tt, driverRegisModel.Firstname.String(), actual.Firstname)
		assert.Equal(tt, driverRegisModel.Lastname.String(), actual.Lastname)
		assert.Equal(tt, driverRegisModel.CitizenID.String(), actual.CitizenID)
	})

	t.Run("success update", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		preRegisterDriver(tt, container, lineUID)
		driverRegisModel, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(tt, err)
		gctx := createUpdateRegistrationReq(lineUID, updateRegisPayload, driverImgContent, licenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent)

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusOK)
		var actual driver.RegistrationRes
		gctx.DecodeJSONResponse(&actual)
		assert.Equal(tt, driverRegisModel.ID.Hex(), actual.ID)
		assert.Equal(tt, updateRegisPayload.Firstname, actual.Firstname)
		assert.Equal(tt, updateRegisPayload.Lastname, actual.Lastname)
		assert.Equal(tt, updateRegisPayload.CitizenID, actual.CitizenID)
		assert.Equal(tt, updateRegisPayload.AcceptedConsentVersion, actual.AcceptedConsentVersion)
		driverRegisModel, err = container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(tt, err)
		assert.Equal(tt, driverRegisModel.Firstname.String(), actual.Firstname)
		assert.Equal(tt, driverRegisModel.Lastname.String(), actual.Lastname)
		assert.Equal(tt, driverRegisModel.CitizenID.String(), actual.CitizenID)
		assert.Equal(tt, fakeDeviceId, driverRegisModel.DeviceID)
	})

}

func TestRegistrationAPI_Revise(t *testing.T) {
	t.Parallel()
	t.Run("Register success", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		id := "lineuid-1"
		payload := registerRevisePayload(id)
		payload.InterestingProvince = "กรุงเทพมหานคร"
		ctx := registerReviseDriver(payload)

		container.GinEngineRouter.HandleContext(ctx.GinCtx())

		d, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), id)
		assert.NoError(tt, err)
		assertDriverReviseRegistrationPayload(tt, d, payload)
		require.Equal(tt, "BKK", d.Region)
		require.Equal(tt, fakeDeviceId, d.DeviceID)
		require.NotContains(tt, ctx.ResponseRecorder.Body.String(), "device_id")
		assertDriverRegistrationLocation(t, d)
	})

	t.Run("Register return error when lookup province not found", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		id := "lineuid-1"
		payload := registerRevisePayload(id)
		payload.InterestingProvince = "not existing province"
		ctx := registerReviseDriver(payload)

		container.GinEngineRouter.HandleContext(ctx.GinCtx())

		ctx.AssertResponseCode(t, http.StatusBadRequest)
		var actual map[string]interface{}
		ctx.DecodeJSONResponse(&actual)
		require.Equal(tt, "interesting province is not found", actual["message"])
	})

	t.Run("Register success without experience", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		id := "lineuid-1"
		payload := registerRevisePayloadWithExp(id, nil)
		ctx := registerReviseDriver(payload)
		container.WhitelistPhonesTestData.SetPhoneWhitelist(payload.Phone)

		container.GinEngineRouter.HandleContext(ctx.GinCtx())

		d, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), id)
		assert.NoError(tt, err)
		assertDriverReviseRegistrationPayload(tt, d, payload)
		assertDriverRegistrationLocation(t, d)
	})

	t.Run("Update success only required field", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		lineUID := "uid_requested_update_revise"
		drvRegistration := container.DriverRegistrationTestData.FindOneByLineID(lineUID)
		updatePayload := updateRevisePayload()
		gctx := updateReviseDriver(updatePayload, lineUID)

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusOK)
		d := container.DriverRegistrationTestData.FindOneByLineID(lineUID)
		require.Equal(tt, updatePayload.Firstname, d.Firstname.String())
		require.Equal(tt, updatePayload.Lastname, d.Lastname.String())
		require.Equal(tt, updatePayload.CitizenID, d.CitizenID.String())
		require.Equal(tt, updatePayload.Phone, d.Phone.String())
		require.Equal(tt, updatePayload.Phone, d.Phone.String())
		require.NotEmpty(tt, d.AvatarURL)
		require.NotEmpty(tt, d.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, d.DriverLicense.PhotoURL)
		//sample assert do not update
		require.Equal(tt, drvRegistration.Vehicle, d.Vehicle)
		require.Equal(tt, drvRegistration.Address, d.Address)
	})

	t.Run("Update success revise", func(tt *testing.T) {
		tt.Parallel()
		container := ittest.NewContainer(tt)
		lineUID := "uid_requested_update_revise"
		drvRegistration := container.DriverRegistrationTestData.FindOneByLineID(lineUID)
		updatePayload := updateRevisePayload()
		exp := time.Now().UTC().AddDate(1, 0, 0)
		updatePayload.CitizenIDExpiredDate = &exp
		updatePayload.Vehicle = driver.VehiclePayload{
			RegistrationDate: time.Now().UTC().Add(-1 * time.Hour * 24 * 30).Truncate(time.Second),
			PlateNumber:      "xxx-222222",
		}
		gctx := updateReviseDriverWithImg(updatePayload, lineUID)

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(tt, http.StatusOK)
		d, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(tt, err)
		require.Equal(tt, updatePayload.Firstname, d.Firstname.String())
		require.Equal(tt, updatePayload.Lastname, d.Lastname.String())
		require.Equal(tt, updatePayload.CitizenID, d.CitizenID.String())
		require.Equal(tt, updatePayload.Phone, d.Phone.String())
		require.Equal(tt, updatePayload.Vehicle.PlateNumber, d.Vehicle.PlateNumber.String())
		require.Equal(tt, updatePayload.Vehicle.RegistrationDate, *d.Vehicle.RegistrationDate)
		require.NotEmpty(tt, d.AvatarURL)
		require.NotEmpty(tt, d.CitizenIDCardPhotoURL)
		require.NotEmpty(tt, d.DriverLicense.PhotoURL)
		//assert do not update
		require.Equal(tt, drvRegistration.Vehicle.PhotoURL, d.Vehicle.PhotoURL)
		require.Equal(tt, drvRegistration.Vehicle.RegistrationPhotoURL, d.Vehicle.RegistrationPhotoURL)
	})

	t.Run("regis error when vehicle is missing", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		err := container.ProvinceRepository.Create(context.Background(), &model.Province{
			Name:                   "กำแพงเพชร",
			RegistrationFlow:       "REVISE",
			RegionCode:             "KPP",
			Label:                  "กำแพงเพชร",
			CreatedAt:              timeutil.BangkokNow(),
			UpdatedAt:              timeutil.BangkokNow(),
			RequestedBy:            "<EMAIL>",
			IsWhitelistAutoApprove: false,
			IsNormalAutoApprove:    false,
			Priority:               1,
		})

		id := "lineuid-1"
		payload := registerRevisePayload(id)
		payload.InterestingProvince = "กำแพงเพชร"
		payload.Vehicle = driver.VehiclePayload{}
		ctx := registerReviseDriver(payload)

		require.NoError(t, err)
		container.GinEngineRouter.HandleContext(ctx.GinCtx())
		ctx.AssertResponseCode(t, http.StatusBadRequest)
	})

	t.Run("should not return date value if value is date zero value", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		id := "lineuid-1"
		payload := registerRevisePayload(id)
		payload.Vehicle = driver.VehiclePayload{
			PlateNumber: "xxx-111111",
		}
		gctx := registerReviseDriver(payload)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(t, http.StatusCreated)
		var actual driver.RegistrationRes
		gctx.DecodeJSONResponse(&actual)
		require.Empty(t, actual.Vehicle.RegistrationDate)
	})

	t.Run("should not return field if field is empty", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		lineUID := "uid_requested_update_revise"
		d, err := container.DriverRegistrationRepository.GetByLineUID(context.Background(), lineUID)
		require.NoError(t, err)
		d.Vehicle = model.VehicleInfo{}
		d.DriverLicense = model.DriverLicenseInfo{}
		d.Banking = model.BankingInfo{}
		err = container.DriverRegistrationRepository.Update(context.Background(), d)
		require.NoError(t, err)

		gctx := createGetRegistrationRequest(lineUID)

		gctx.Send(container.GinEngineRouter)

		gctx.AssertResponseCode(t, http.StatusOK)

		var actual driver.RegistrationRes
		gctx.DecodeJSONResponse(&actual)

		require.Nil(t, actual.Vehicle)
		require.Nil(t, actual.DriverLicense)
		require.Nil(t, actual.Banking)
	})
}

func assertDriverReviseRegistrationPayload(t *testing.T, d *model.DriverRegistration, payload driver.RegistrationRevisePayload) {
	require.Equal(t, d.Firstname.String(), payload.Firstname)
	require.Equal(t, d.Lastname.String(), payload.Lastname)
	require.Equal(t, d.CitizenID.String(), payload.CitizenID)
	require.Equal(t, d.Phone.String(), payload.Phone)
	require.Equal(t, d.InterestingProvince, payload.InterestingProvince)
	require.NotEmpty(t, d.AvatarURL)
	require.NotEmpty(t, d.CitizenIDCardPhotoURL)
	require.NotEmpty(t, d.DriverLicense.PhotoURL)
}

func TestRegistrationAPI_AutoApprove(t *testing.T) {
	const existingCitizenId = "*************"
	const provinceName = "กรุงเทพมหานคร"

	t.Parallel()
	ctn := ittest.NewContainer(t)
	require.NoError(t, datastore.EnsureIndexForTest(ctn.DBConnectionForTest))

	ctn.ProvincesTestData.SetWhitelistAutoApprove(provinceName)
	ctn.ProvincesTestData.SetNormalAutoApprove(provinceName)
	require.True(t, ctn.DriverRegistrationRepository.IsExistsByCitizenID(context.Background(), crypt.NewLazyEncryptedString(existingCitizenId)))

	doReviseRegisterWithDuplicateCitizenId := func(lineuid string) (driver.RegistrationRevisePayload, *testutil.GinContextWithRecorder) {
		id := lineuid
		payload := registerRevisePayload(id)
		ctn.WhitelistPhonesTestData.SetPhoneWhitelist(payload.Phone)
		ctn.OTPSessionTestData.OtpVerified(id, payload.Phone)
		payload.InterestingProvince = provinceName
		payload.CitizenID = existingCitizenId
		gctx := registerReviseDriver(payload)

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		return payload, gctx
	}

	t.Run("regis status should be pending when duplicate citizen id with auto approve flow", func(t *testing.T) {
		lineuid := "auto-lineuid-1"
		payload, gctx := doReviseRegisterWithDuplicateCitizenId(lineuid)

		gctx.AssertResponseCode(t, http.StatusCreated)
		var dr driver.RegistrationRes
		gctx.DecodeJSONResponse(&dr)
		d, err := ctn.DriverRegistrationRepository.GetByLineUID(context.Background(), lineuid)
		require.NoError(t, err)
		require.Equal(t, d.Firstname.String(), payload.Firstname)
		require.Equal(t, d.CitizenID.String(), payload.CitizenID)
		require.Equal(t, d.Phone.String(), payload.Phone)
		require.Equal(t, d.Status, model.RegistrationPending)
	})

	t.Run("redirect client app to exam page if registered with duplicate citizen id", func(t *testing.T) {
		_, gctx := doReviseRegisterWithDuplicateCitizenId("auto-lineuid-2")

		gctx.AssertResponseCode(t, http.StatusCreated)
		var dr driver.RegistrationRes
		gctx.DecodeJSONResponse(&dr)
		require.Equal(t, false, dr.IsApproved) //expect false to redirect to exam page
	})

	t.Run("redirect client app to ready to work page when normal driver registered in auto approve province (normalAutoApprove is true)", func(tt *testing.T) {
		id := "auto-lineuid-3"
		payload := registerRevisePayload(id)
		payload.InterestingProvince = provinceName
		ctn.WhitelistPhonesTestData.RemovePhoneWhitelist(payload.Phone)
		payload.CitizenID = testdata.ThaiNationID()
		gctx := registerReviseDriver(payload)

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, http.StatusCreated)
		var dr driver.RegistrationRes
		gctx.DecodeJSONResponse(&dr)
		d, err := ctn.DriverRegistrationRepository.GetByLineUID(context.Background(), id)
		require.NoError(tt, err)
		require.Equal(tt, d.Firstname.String(), payload.Firstname)
		require.Equal(tt, d.CitizenID.String(), payload.CitizenID)
		require.Equal(tt, d.Phone.String(), payload.Phone)
		require.Equal(tt, true, dr.IsApproved) //expect true to redirect to ready to work page

		actualUobRef, err := ctn.MongoUobRefRepository.FindOne(context.Background(), persistence.NewUobRefQuery().WithUobRefID("ABCD000001"))
		require.NoError(tt, err)
		require.NotEmpty(tt, actualUobRef.DriverID)

		actualDriver, err := ctn.DriverRepository.FindDriverID(context.Background(), actualUobRef.DriverID)
		require.NoError(tt, err)
		require.Equal(tt, actualUobRef.UobRefID, actualDriver.DriverUOBRefID())
		require.Equal(tt, d.ID, actualDriver.RegistrationIDs[0])
	})
}

func registerRevisePayload(lID string) driver.RegistrationRevisePayload {
	return registerRevisePayloadWithExp(lID, &driver.DriverExperiencedInformation{
		AppName:     "GRAB",
		Experienced: "3 years",
	})
}

func registerRevisePayloadWithExp(lID string, exp *driver.DriverExperiencedInformation) driver.RegistrationRevisePayload {
	ex := time.Now().UTC().AddDate(1, 0, 0)
	return driver.RegistrationRevisePayload{
		Firstname:                    "รัก",
		Lastname:                     "นะ",
		Phone:                        "**********",
		CitizenID:                    "*************",
		CitizenIDExpiredDate:         &ex,
		Birthday:                     time.Now().UTC().AddDate(-18, 0, 0),
		LineUID:                      lID,
		DriverExperiencedInformation: exp,
		InterestingProvince:          "กรุงเทพมหานคร",
		Vehicle: driver.VehiclePayload{
			RegistrationDate: time.Now().UTC().Add(-1 * time.Hour * 24 * 30),
			PlateNumber:      "xxx-111111",
		},
		ReferrerCode:           "0987654321",
		AcceptedConsentVersion: 1,
	}
}

func updateRevisePayload() driver.UpdateRegistrationRevisePayload {
	return driver.UpdateRegistrationRevisePayload{
		Firstname:      "ไม่รัก",
		Lastname:       "ไม่นะ",
		Phone:          "**********",
		EmergencyPhone: "**********",
		CitizenID:      "*************",
	}
}

func registerReviseDriver(d driver.RegistrationRevisePayload) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPOST("/v1/registrations?flow=REVISE")
	setRegistrationHeader(ctx)
	ctx.Body().MultipartForm().
		JSON("registration", d).
		ImageData("driverImage", "driverImgContent").
		ImageData("driverLicenseImage", "driverLicenseImgContent").
		ImageData("citizenIdImage", "citizenIDImgContent").
		ImageData("legislationImage", "legislationImgContent").
		ImageData("lendingVehicleImage", "lendingVehicleImgContent").
		Build()
	return ctx
}

func updateReviseDriverWithImg(d driver.UpdateRegistrationRevisePayload, lID string) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("/v1/registrations/%s", lID)
	ctx.Body().MultipartForm().
		JSON("registration", d).
		ImageData("driverImage", "driverImgContent2").
		ImageData("driverLicenseImage", "driverLicenseImgContent2").
		ImageData("citizenIdImage", "citizenIDImgContent2").
		ImageData("legislationImage", "legislationImgContent").
		ImageData("lendingVehicleImage", "lendingVehicleImgContent").
		Build()
	return ctx
}

func updateReviseDriver(d driver.UpdateRegistrationRevisePayload, lID string) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("/v1/registrations/%s", lID)
	ctx.Body().MultipartForm().
		JSON("registration", d).
		Build()
	return ctx
}

func createPreRegisterReq(r driver.PreRegisterReq) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPOST("/v1/driver/register")
	ctx.Body().JSON(r).Build()
	return ctx
}

func preRegisterDriver(t *testing.T, container *ittest.IntegrationTestContainer, LineUID string) {
	gctx := createPreRegisterReq(driver.PreRegisterReq{
		Firstname: fake.FirstName(),
		Lastname:  fake.LastName(),
		LineUID:   LineUID,
		Phone:     fake.Phone(),
	})

	container.GinEngineRouter.HandleContext(gctx.GinCtx())

	gctx.AssertResponseCode(t, http.StatusOK)
}

func createRegistrationRequest(r driver.RegistrationPayload, driverImgContent, driverLicenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent string) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPOST("/v1/registrations")
	setRegistrationHeader(ctx)
	ctx.Body().MultipartForm().
		JSON("registration", r).
		ImageData("driverImage", driverImgContent).
		ImageData("driverLicenseImage", driverLicenseImgContent).
		ImageData("citizenIdImage", citizenIDImgContent).
		ImageData("vehicleImage", vehicleImgContent).
		ImageData("vehicleRegistrationImage", vehicleRegistrationImgContent).
		ImageData("bookBankImage", bankImgContent).
		Build()

	return ctx
}

func setRegistrationHeader(ctx *testutil.GinContextWithRecorder) {
	ctx.SetHeader("x-district-province", "Krung+Thep")
	ctx.SetHeader("x-district-area", "Sathon")
	ctx.SetHeader("x-district-full-address", "10%20%E0%B8%97%E0%B8%B5%E0%B8%A7%E0%B8%B1%E0%B8%99")
	ctx.SetHeader("x-district-lat-lng", "1,2")
	ctx.SetHeader("x-device-id", fakeDeviceId)
}

func assertDriverRegistrationLocation(t *testing.T, d *model.DriverRegistration) {
	assert.Equal(t, "Krung Thep", d.RegistrationLocation.Province)
	assert.Equal(t, "Sathon", d.RegistrationLocation.District)
	assert.Equal(t, "10 ทีวัน", d.RegistrationLocation.FullAddress)
	assert.Equal(t, model.Location{Lat: 1, Lng: 2}, d.RegistrationLocation.Location)
}

func registerDriver(t *testing.T, container *ittest.IntegrationTestContainer, req driver.RegistrationPayload) {
	gctx := createRegistrationRequest(req, "c1", "c2", "c3", "c4", "c5", "c6")

	container.GinEngineRouter.HandleContext(gctx.GinCtx())

	gctx.AssertResponseCode(t, http.StatusCreated)
}

func createRegistrationPayload(lineUID string) driver.RegistrationPayload {
	return driver.RegistrationPayload{
		Title:               "นาย",
		Firstname:           "ทดลอง",
		Lastname:            "ภาษาไทย",
		Phone:               "**********",
		EmergencyPhone:      "**********",
		CitizenID:           "*************",
		Birthday:            time.Now().UTC().AddDate(-25, 0, 0),
		LineUID:             lineUID,
		InterestingProvince: "กรุงเทพมหานคร",
		Address: driver.AddressPayload{
			HouseNumber: fake.Digits(),
			Moo:         fake.Digits(),
			Subdistrict: fake.CharactersN(10),
			District:    fake.City(),
			Province:    fake.State(),
			Zipcode:     fake.DigitsN(5),
		},
		Bank: driver.BankPayload{
			Account:       "***********",
			BankName:      "KBANK",
			AccountHolder: fake.FullName(),
		},
		DriverLicense: driver.DriverLicensePayload{
			ID:             "*********",
			ExpirationDate: time.Now().UTC().Add(time.Hour * 24 * 30),
		},
		Vehicle: driver.VehiclePayload{
			RegistrationDate: time.Now().UTC().Add(-1 * time.Hour * 24 * 30),
			PlateNumber:      "xxx-111111",
		},
		AcceptedConsentVersion: 1,
	}
}

func createUpdateRegistrationPayload() driver.UpdateRegistrationPayload {
	exp := time.Now().UTC().AddDate(1, 0, 0)
	return driver.UpdateRegistrationPayload{
		Title:                "นาย",
		Firstname:            "ทดลอง",
		Lastname:             "ภาษาไทย",
		Phone:                "**********",
		EmergencyPhone:       "**********",
		CitizenID:            "*************",
		CitizenIDExpiredDate: &exp,
		Birthday:             time.Now().UTC().AddDate(-25, 0, 0),
		InterestingProvince:  "region",
		Address: driver.AddressPayload{
			HouseNumber: fake.Digits(),
			Moo:         fake.Digits(),
			Subdistrict: fake.CharactersN(10),
			District:    fake.City(),
			Province:    fake.State(),
			Zipcode:     fake.DigitsN(5),
		},
		Bank: driver.BankPayload{
			Account:       "***********",
			BankName:      "SCB",
			AccountHolder: fake.FullName(),
		},
		DriverLicense: driver.DriverLicensePayload{
			ID:             "*********",
			ExpirationDate: time.Now().UTC().Add(time.Hour * 24 * 30),
		},
		Vehicle: driver.VehiclePayload{
			RegistrationDate: time.Now().UTC().Add(-1 * time.Hour * 24 * 30),
			PlateNumber:      "xxx-211111",
		},
		AcceptedConsentVersion: 2,
	}
}

func createUpdateRegistrationReq(id string, r driver.UpdateRegistrationPayload, driverImgContent, driverLicenseImgContent, citizenIDImgContent, vehicleImgContent, vehicleRegistrationImgContent, bankImgContent string) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("/v1/registrations/%s", id)
	ctx.Body().MultipartForm().
		JSON("registration", r).
		ImageDataIfNotZero("driverImage", driverImgContent).
		ImageDataIfNotZero("driverLicenseImage", driverLicenseImgContent).
		ImageDataIfNotZero("citizenIdImage", citizenIDImgContent).
		ImageDataIfNotZero("vehicleImage", vehicleImgContent).
		ImageDataIfNotZero("vehicleRegistrationImage", vehicleRegistrationImgContent).
		ImageDataIfNotZero("bookBankImage", bankImgContent).
		Build()

	ctx.SetHeader("x-device-id", fakeDeviceId)

	return ctx
}

func createGetRegistrationRequest(uid string) *testutil.GinContextWithRecorder {
	gctx := testutil.NewContextWithRecorder()
	gctx.SetGET("/v1/registrations/%s", uid)
	return gctx
}
