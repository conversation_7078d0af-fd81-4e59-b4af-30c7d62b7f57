//go:build integration_test
// +build integration_test

package driver_test

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestRegistrationAPI_Questions(t *testing.T) {

	t.Run("list question with `questions.tmpl`", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)
		gctx := callQuestionAPI(t, container)
		gctx.Send(container.GinEngineRouter)
		gctx.AssertResponseCode(t, http.StatusOK)
		var qRes driver.QuestionResp
		gctx.DecodeJSONResponse(&qRes)

		require.Equal(t, qRes.Data[0].Question, "เมื่อต้องการพัก หรือไม่สะดวกรับงานต่อไรเดอร์ต้องทำอย่างไร")
		require.Equal(t, qRes.Data[1].Question, "ข้อใดไม่ถูกต้องเกี่ยวกับหน้าวอลเล็ต หรือกระเป๋าเงินบนแอปฯ LINE MAN RIDER")
		require.Equal(t, qRes.Data[2].Question, "เมื่อกดรับงานสำเร็จ และร้านค้ารับออเดอร์แล้ว ขั้นตอนแรกไรเดอร์ต้องปฏิบัติตามข้อใด")
		require.Equal(t, qRes.Data[3].Question, "ไรเดอร์สามารถกดปุ่ม ส่งมอบอาหารแล้ว ได้ในสถานการณ์ใด")
		require.Equal(t, qRes.Data[4].Question, "เมื่อไปถึงร้านค้าไรเดอร์ต้องทำอย่างไรในขั้นตอนแรก")
		require.Equal(t, qRes.Data[5].Question, "การแต่งกายที่ถูกกฎระเบียบของไรเดอร์คือข้อใด")
		require.Equal(t, qRes.Data[6].Question, "สถานการณ์ใด ที่ไรเดอร์สามารถขอยกเลิกงานผ่านฝ่ายดูแลคนขับ (CS)ได้")
		require.Equal(t, qRes.Data[7].Question, "หากร้านค้าต้องเตรียมอาหารนาน ไรเดอร์ควรปฏิบัติอย่างไร")
		require.Equal(t, qRes.Data[8].Question, "ข้อปฏิบัติใดถูกต้อง หากลูกค้าขอยกเลิกออเดอร์ ขณะที่ไรเดอร์รับอาหารมาแล้วและกำลังจะนำไปส่งให้ลูกค้า")
		require.Equal(t, qRes.Data[9].Question, "หากร้านค้าปิด ข้อปฏิบัติใดถูกต้อง")

	})
}

func callQuestionAPI(t *testing.T, container *ittest.IntegrationTestContainer) *testutil.GinContextWithRecorder {
	regis, err := container.DriverRegistrationRepository.FindOneByID(context.Background(), "000000000000000000000002")
	require.NoError(t, err)
	gctx := testutil.NewContextWithRecorder()
	gctx.SetGET("/v1/questions/%s", regis.LineUID.String())
	return gctx
}
