package persistence

import (
	"context"
	"fmt"
	"time"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

var _ repository.OTPSessionRepo = &OTPSessionCacheRepo{}

type OTPSessionCacheRepo struct {
	cache         cache.Cache
	newOTPSession model.NewOTPSession
}

func (cr OTPSessionCacheRepo) Remove(ctx context.Context, lineUID string) error {
	return cr.cache.Remove(ctx, otpCacheKey(lineUID))
}

func (cr OTPSessionCacheRepo) Save(ctx context.Context, ses model.OTPSession) error {
	row, err := ses.Serialize()
	if err != nil {
		return err
	}
	if err := cr.cache.Set(ctx, otpCacheKey(ses.LineUID), row, cache.Option{
		ExpiredTime: time.Now().Add(24 * time.Hour),
	}); err != nil {
		return err
	}
	return nil
}

func (cr OTPSessionCacheRepo) Load(ctx context.Context, lineUID string) (model.OTPSession, error) {
	ses := cr.newOTPSession(lineUID)
	row, err := cr.cache.Get(ctx, otpCacheKey(lineUID))
	if err != nil {
		if err == cache.NotFound {
			err = repository.ErrNotFound
		}
		return ses, err
	}
	err = ses.Unserialize(row)
	return ses, err
}

func (cr OTPSessionCacheRepo) LoadOrNew(ctx context.Context, lineUID string) (model.OTPSession, error) {
	ses, err := cr.Load(ctx, lineUID)
	if err == repository.ErrNotFound {
		return cr.newOTPSession(lineUID), nil
	}
	return ses, err
}

func otpCacheKey(lineUID string) string {
	prefix := "OTP"
	return fmt.Sprintf("%s:%s", prefix, crypt.EncryptedString(lineUID).Encrypt())
}

func ProvideOTPSessionRepo(cache cache.Cache, newOTPSession model.NewOTPSession, meter metric.Meter) *repository.ProxyOTPSessionRepo {
	return repository.NewLatencyProxyOTPSessionRepo(&OTPSessionCacheRepo{
		cache:         cache,
		newOTPSession: newOTPSession,
	}, meter)
}
