package persistence

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type DataStoreHeatMapRepository struct {
	datastore HeatMapDataStore
}

func (repo *DataStoreHeatMapRepository) Find(query repository.HeatMapQuery, opts ...repository.Option) ([]model.HeatMap, error) {

	var heatmaps []model.HeatMap
	nearSphere := bson.D{
		{Key: "$near", Value: bson.D{
			{Key: "$geometry", Value: model.NewHeatMapPoint(query.Lng, query.Lat)},
			{Key: "$maxDistance", Value: query.DistanceInMeters},
		}},
	}

	filter := bson.M{"location": nearSphere}
	t := query.Time.Format("15:04")
	filter["end"] = bson.M{"$gte": t}
	filter["region"] = query.Region

	if err := repo.datastore.Find(context.Background(), filter, 0, 0, &heatmaps, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return heatmaps, nil
}

func (repo *DataStoreHeatMapRepository) FindByH3IDIn(h3ids []string, opts ...repository.Option) ([]model.HeatMap, error) {

	var heatmaps []model.HeatMap

	filter := bson.M{
		"h3id": bson.M{
			"$in": h3ids,
		},
	}

	if err := repo.datastore.Find(context.Background(), filter, 0, 0, &heatmaps, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return heatmaps, nil
}

func (repo *DataStoreHeatMapRepository) UpsertAll(listHeatMap []model.HeatMap) error {

	for _, heatMap := range listHeatMap {
		_, err := repo.datastore.Upsert(context.Background(), bson.M{"region": heatMap.Region, "h3id": heatMap.H3ID, "start": heatMap.Start, "end": heatMap.End}, heatMap)
		if err != nil {
			logrus.Error("upsert heatmap error: ", err)
		}
	}

	return nil
}

func (repo *DataStoreHeatMapRepository) DeleteByHourOfWeekday(weekday int, hour int) error {
	query := bson.M{}
	query["slot.weekday"] = weekday
	query["slot.hour"] = hour

	if _, err := repo.datastore.RemoveAll(context.Background(), query); err != nil {
		return err
	}

	return nil
}

func (repo *DataStoreHeatMapRepository) DeleteByCreatedAtLte(time time.Time) error {
	query := bson.M{}
	query["created_at"] = bson.M{"$lte": time}

	if _, err := repo.datastore.RemoveAll(context.Background(), query); err != nil {
		return err
	}

	return nil
}

type HeatMapDataStore mongodb.DataStoreInterface

func ProvideHeatMapDataStore(conn *mongodb.Conn) HeatMapDataStore {
	return mongodb.NewDataStoreWithConn(conn, "heatmap")
}

func ProvideHeatMapRepository(ds HeatMapDataStore, meter metric.Meter) *repository.ProxyHeatMapRepository {
	return repository.NewLatencyProxyHeatMapRepository(&DataStoreHeatMapRepository{
		datastore: ds,
	}, meter)
}
