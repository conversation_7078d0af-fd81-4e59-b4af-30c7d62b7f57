package persistence

import (
	"context"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

var _ repository.RatingOptionRepository = &RatingOptionRepo{}

type RatingOptionRepo struct {
	ds RatingOptionDataStore
}

func (repo *RatingOptionRepo) Get(ctx context.Context, id string) (*model.RatingOption, error) {
	var ro *model.RatingOption
	oid, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
	if err != nil {
		return nil, err
	}
	err = repo.ds.FindOne(ctx, bson.M{"_id": oid}, &ro)
	if err != nil {
		return nil, err
	}
	return ro, nil
}
func (repo *RatingOptionRepo) GetAll(ctx context.Context, query repository.RatingOptionQuery) ([]model.RatingOption, error) {
	roc := make([]model.RatingOption, 0)

	condition := bson.M{}

	if query.RatingType != "" {
		condition["rating_type"] = query.RatingType
	}

	if query.Active != nil {
		condition["active"] = query.Active
	}

	err := repo.ds.Find(ctx, condition, 0, 0, &roc)

	if err != nil {
		return []model.RatingOption{}, err
	}
	return roc, nil
}

func (repo *RatingOptionRepo) Create(ctx context.Context, ro *model.RatingOption) error {
	now := time.Now().UTC()
	ro.CreatedAt = now
	ro.UpdatedAt = now
	return repo.ds.Insert(ctx, ro)
}
func (repo *RatingOptionRepo) Update(ctx context.Context, ro *model.RatingOption) error {
	ro.UpdatedAt = time.Now().UTC()
	err := repo.ds.Replace(ctx, bson.M{"_id": ro.ID}, ro)
	if err != nil {
		return err
	}

	return nil
}
func (repo *RatingOptionRepo) Delete(ctx context.Context, ro *model.RatingOption) error {
	err := repo.ds.RemoveID(ctx, ro.ID)
	if err != nil {
		return err
	}
	return nil
}

type RatingOptionDataStore mongodb.DataStoreInterface

func ProvideRatingOptionRepository(datastore RatingOptionDataStore, meter metric.Meter) *repository.ProxyRatingOptionRepository {
	return repository.NewLatencyProxyRatingOptionRepository(&RatingOptionRepo{ds: datastore}, meter)
}

func ProvideRatingOptionDataStore(conn *mongodb.Conn) RatingOptionDataStore {
	return mongodb.NewDataStoreWithConn(conn, "rating_options")
}
