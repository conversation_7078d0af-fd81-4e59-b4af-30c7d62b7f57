package testdata

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

func ProvideOTPSessionTestData(repo repository.OTPSessionRepo) *OTPSessionTestData {
	return &OTPSessionTestData{
		otpRepo: repo,
	}
}

type OTPSessionTestData struct {
	otpRepo repository.OTPSessionRepo
}

func (w *OTPSessionTestData) OtpVerified(lineUID string, phone string) {
	sess, err := w.otpRepo.LoadOrNew(context.Background(), lineUID)
	if err != nil {
		panic(err)
	}
	sess.PhoneNumber = phone
	sess.IsValidated = true
	if err := w.otpRepo.Save(context.Background(), sess); err != nil {
		panic(err)
	}
}

func (w *OTPSessionTestData) ClearOtp(lineUID string) {
	_ = w.otpRepo.Remove(context.Background(), lineUID)
}
