//go:build integration_test
// +build integration_test

package geo_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/geo"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func Test_FromContext_WithTxn(tt *testing.T) {
	ctn := ittest.NewContainer(tt)
	lat, lng, currentTime := 1.0, -1.0, timeutils.Now()

	tt.Run("go context", func(t *testing.T) {
		ctx := context.Background()
		ctx = geo.WrapContext(ctx, lat, lng, currentTime)
		ctn.DriverOperationAdminAPI.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
			checkContext(t, sessCtx, lat, lng, currentTime)
			return nil, nil
		})
	})

	tt.Run("gin context", func(t *testing.T) {
		ginCtx, _ := testutil.TestRequestContext("POST", "/v1/account/next-status", nil)
		ctx := geo.WrapContext(ginCtx, lat, lng, currentTime)
		ctn.DriverOperationAdminAPI.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
			checkContext(t, sessCtx, lat, lng, currentTime)
			return nil, nil
		})
	})
}

func checkContext(t *testing.T, ctx context.Context, expectedLat float64, expectedLng float64, expectedTime time.Time) {
	lat, lng, updatedAt := geo.FromContext(ctx)
	require.Equal(t, expectedLat, lat)
	require.Equal(t, expectedLng, lng)
	require.Equal(t, expectedTime, updatedAt)
}
