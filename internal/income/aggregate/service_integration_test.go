//go:build integration_test
// +build integration_test

package aggregate_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestIncomeAggregateService_Inc(t *testing.T) {
	t.<PERSON>llel()

	t.Run("able to inc correctly", func(t *testing.T) {
		t.<PERSON>()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/aggregate/inc")
		require.NoError(t, err, "unable to setup fixture")

		var tmp []struct {
			TransactionID string `bson:"transaction_id"`
		}
		err = container.TransactionDataStore.FindWithSelector(context.Background(), bson.M{"info.driver_id": "LMDSAC9I3"}, bson.M{"transaction_id": 1}, &tmp)
		require.NoError(t, err, "unable to setup fixture")

		var transactionIDs []string
		for _, v := range tmp {
			transactionIDs = append(transactionIDs, v.TransactionID)
		}

		// When
		req := aggregate.IncomeIncRequest{
			Date:           time.Date(2024, 8, 1, 0, 0, 0, 0, timeutil.BangkokLocation()),
			DriverId:       "LMDSAC9I3",
			TransactionIDs: transactionIDs,
		}

		ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2024, 8, 2, 10, 0, 0, 0, timeutil.BangkokLocation()))
		err = container.IncomeAggregateService.Inc(ctx, req)

		require.NoError(t, err, "unable to inc")

		// Then
		ids, err := container.IncomeDailySummaryRepository.Query(context.Background(), &persistence.MongoIncomeDailySummaryQuery{
			DriverID:      "LMDSAC9I3",
			FromInclusive: time.Date(2024, 8, 1, 0, 0, 0, 0, timeutil.BangkokLocation()),
			ToExclusive:   time.Date(2024, 8, 2, 0, 0, 0, 0, timeutil.BangkokLocation()),
		}, 0, 0, []string{})

		require.NoError(t, err, "unable to query daily income summary")
		require.Len(t, ids, 1, "should have 1 daily income summary")
		id := ids[0]
		require.Equal(t, "LMDSAC9I3", id.DriverID, "driver id should be LMDSAC9I3")
		require.Equal(t, 1, id.SchemaVersion)                                                                                         // existing data is schema version 1
		require.Equal(t, time.Date(2024, 8, 1, 0, 30, 0, 0, timeutil.BangkokLocation()), id.CreatedAt.In(timeutil.BangkokLocation())) // should not change
		require.WithinDuration(t, time.Now(), id.UpdatedAt, 10*time.Second)
		require.Equal(t, types.Money(146.14), id.TotalIncome)
		require.Equal(t, 3, id.TotalOrder)
		require.Equal(t, 3, id.TotalTrip)
		require.True(t, types.Money(125.5).Equal(id.IncomeDetail.TotalWage))
		require.True(t, types.Money(10).Equal(id.IncomeDetail.TotalIncentive))
		require.True(t, types.Money(4.36).Equal(id.IncomeDetail.TotalWithholdingTax))
		require.True(t, types.Money(5).Equal(id.IncomeDetail.TotalTip))
		require.True(t, types.Money(10).Equal(id.IncomeDetail.TotalGuaranteeIncentive))
	})

	t.Run("able to upsert inc if aggregate date data not found", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/aggregate/inc")
		require.NoError(t, err, "unable to setup fixture")

		var tmp []struct {
			TransactionID string `bson:"transaction_id"`
		}
		err = container.TransactionDataStore.FindWithSelector(context.Background(), bson.M{"info.driver_id": "LMDSAC9I3"}, bson.M{"transaction_id": 1}, &tmp)
		require.NoError(t, err, "unable to setup fixture")

		var transactionIDs []string
		for _, v := range tmp {
			transactionIDs = append(transactionIDs, v.TransactionID)
		}

		// remove daily income summary which expect to be upserted
		err = container.IncomeDailySummaryStore.Remove(context.Background(), bson.M{"driver_id": "LMDSAC9I3", "date": time.Date(2024, 8, 1, 0, 0, 0, 0, timeutil.BangkokLocation())})
		require.NoError(t, err, "unable to remove daily income summary")

		// When
		req := aggregate.IncomeIncRequest{
			Date:           time.Date(2024, 8, 1, 0, 0, 0, 0, timeutil.BangkokLocation()),
			DriverId:       "LMDSAC9I3",
			TransactionIDs: transactionIDs,
		}

		ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2024, 8, 2, 10, 0, 0, 0, timeutil.BangkokLocation()))
		err = container.IncomeAggregateService.Inc(ctx, req)

		require.NoError(t, err, "unable to inc")

		// Then
		ids, err := container.IncomeDailySummaryRepository.Query(context.Background(), &persistence.MongoIncomeDailySummaryQuery{
			DriverID:      "LMDSAC9I3",
			FromInclusive: time.Date(2024, 8, 1, 0, 0, 0, 0, timeutil.BangkokLocation()),
			ToExclusive:   time.Date(2024, 8, 2, 0, 0, 0, 0, timeutil.BangkokLocation()),
		}, 0, 0, []string{})

		require.NoError(t, err, "unable to query daily income summary")
		require.Len(t, ids, 1, "should have 1 daily income summary")
		id := ids[0]
		require.Equal(t, "LMDSAC9I3", id.DriverID, "driver id should be LMDSAC9I3")
		require.Equal(t, 2, id.SchemaVersion)
		require.WithinDuration(t, time.Now(), id.CreatedAt, 10*time.Second)
		require.WithinDuration(t, time.Now(), id.UpdatedAt, 10*time.Second)
		require.Equal(t, types.Money(65.62), id.TotalIncome)
		require.Equal(t, 1, id.TotalOrder)
		require.Equal(t, 1, id.TotalTrip)
		require.True(t, types.Money(42.5).Equal(id.IncomeDetail.TotalWage))
		require.True(t, types.Money(10).Equal(id.IncomeDetail.TotalIncentive))
		require.True(t, types.Money(1.88).Equal(id.IncomeDetail.TotalWithholdingTax))
		require.True(t, types.Money(5).Equal(id.IncomeDetail.TotalTip))
		require.True(t, types.Money(10).Equal(id.IncomeDetail.TotalGuaranteeIncentive))
	})
}
