package di

import (
	"context"
	"os"
	"reflect"
	"sync"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"
	"github.com/google/tink/go/aead"
	"github.com/google/tink/go/insecurecleartextkeyset"
	"github.com/google/tink/go/keyset"
	"github.com/google/tink/go/tink"
	"github.com/google/wire"
	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/mgocompat"

	"git.wndv.co/go/logx/v2"
	lmwnmongo "git.wndv.co/go/mongo"
	psentry "git.wndv.co/go/pillars/sentry"
	_ "git.wndv.co/go/topkek-lmwn/absinthe"
	"git.wndv.co/go/topkek-lmwn/setup"
	topkekmongo "git.wndv.co/go/topkek/pkg/mongo"
	"git.wndv.co/go/topkek/topkek"
	"git.wndv.co/go/unleash/admin"
	unleashprovider "git.wndv.co/go/unleash/provider"
	"git.wndv.co/go/unleash/strategies"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/customcodec"
	"git.wndv.co/lineman/absinthe/vos"
	"git.wndv.co/lineman/fleet-distribution/cmd/api/http/router"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/auth/token"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongoprofiler"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/fleetbucket"
	"git.wndv.co/lineman/fleet-distribution/internal/logging"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/externaldeps"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	"git.wndv.co/lineman/fleet-distribution/toggle"
)

//go:generate providergen -source_root ../../internal -out ../../internal/di/gen_providers.go -base_repo "git.wndv.co/lineman/fleet-distribution/internal" -out_package "git.wndv.co/lineman/fleet-distribution/internal/di"

type (
	ContainerInitializer string

	InitFirst      string
	InitVOS        string
	InitTestDBConn string
	InitTestData   string
	InitMetric     string
	InitSentry     string
	InitModel      string
	InitToggle     string
	InitTopkek     string
)

var once sync.Once

type DBConfig struct {
	ReadPrimary bool `envconfig:"DB_READ_PRIMARY" default:"false"`
	WriteWait   int  `envconfig:"DB_WRITE_WAIT" default:"0"`
}

// it is guarantee to be first to called when create container, do not pass any dependency here
// @@no-locator-generation@@
func ProvideInit1() (i InitFirst) {
	logging.Setup()
	config.LoadConfig()
	token.Load()
	return
}

// @@no-locator-generation@@
func ProvideInit2(cfg config.GlobalConfig) (i InitVOS) {
	if !cfg.DisabledDownloadServiceAccountFromVOS {
		fleetbucket.DownloadDriverServiceAccountFile(cfg.ApplicationCredentialsFile, vos.NewFromEnvironment())
	}
	return
}

func ProvideDBConfig() (dbCfg DBConfig) {
	envconfig.MustProcess("", &dbCfg)

	return dbCfg
}

// @@no-locator-generation@@
func ProvideDBConnectionV2() (lmwnmongo.Connection, func()) {
	cfg := lmwnmongo.NewConfig()

	con, err := lmwnmongo.NewConnection(cfg)
	if err != nil {
		panic(err)
	}

	return con, func() {
		if con != nil {
			con.Stop()
		}
	}
}

// @@wire-set-name@@ name:"MainConfigSet"
// @@no-locator-generation@@
func ProvideDBConnection() (*mongodb.Conn, func()) {
	cfg := mongodb.LoadConfigFromEnv()
	return createDBConnection(cfg)
}

// @@wire-set-name@@ name:"MainConfigSet"
// @@no-locator-generation@@
func ProvideDBMonitorConnection() (*mongoprofiler.MonitorConn, func()) {
	cfg := mongodb.LoadConfigFromEnv()
	cfg.Name = "admin"
	cfg.Username = os.Getenv("DB_MONITOR_USERNAME")
	cfg.Password = os.Getenv("DB_MONITOR_PASSWORD")
	conn, cleanupFunc := createDBConnection(cfg)
	return &mongoprofiler.MonitorConn{
		Conn: conn,
	}, cleanupFunc
}

func createDBConnection(cfg *mongodb.Config) (*mongodb.Conn, func()) {
	registerStandardMongoCodec()
	stdConn, err := lmwnmongo.NewConnection(newStandardMongoConfigFromAbsintheConfig(cfg))
	if err != nil {
		panic(err)
	}
	connector := mongodb.NewMongoDBConnectorWithConn(stdConn.Client(), cfg)

	return connector, func() {
		connector.Close(context.Background())
	}
}

func newStandardMongoConfigFromAbsintheConfig(cfg *mongodb.Config) *lmwnmongo.Config {
	stdCfg := lmwnmongo.NewConfig()
	stdCfg.Hosts = cfg.Host
	stdCfg.Name = cfg.Name
	stdCfg.Username = cfg.Username
	stdCfg.Password = cfg.Password
	stdCfg.ReplicaSet = cfg.ReplicaSet
	stdCfg.AdminName = cfg.AdminName
	stdCfg.ReadTimeout = cfg.ReadTimeout
	stdCfg.ConnectionTimeout = cfg.ConnectionTimeout
	stdCfg.MaxConnIdleTime = cfg.MaxConnIdleTime
	stdCfg.PoolLimit = cfg.PoolLimit

	return stdCfg
}

func registerStandardMongoCodec() {
	rb := mgocompat.NewRegistryBuilder().
		RegisterCodec(reflect.TypeOf(time.Duration(0)), customcodec.NewTimeDurationCodec(time.Millisecond))
	topkekmongo.RegisterCodec(rb)
	bson.DefaultRegistry = rb.Build()
}

// @@no-locator-generation@@
func ProvideInitModel() (i InitModel) {
	model.InitDefaults()
	return
}

// @@no-locator-generation@@
func ProvideInitTestDBConn() (i InitTestDBConn, c func()) {
	externaldeps.Init()

	mongoProc := externaldeps.NewDefaultMongoDB()
	c = func() {
		_ = mongoProc.Stop(context.Background())
	}

	if err := mongoProc.Start(context.Background()); err != nil {
		panic(err)
	}
	i = InitTestDBConn(*mongoProc.GetDatabaseName())
	return
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideDBConnectionForTest(i InitTestDBConn) (*mongodb.Conn, func()) {
	cfg := mongodb.LoadConfigFromEnv()
	if i != "" {
		cfg.Name = string(i)
	}
	conn, cleanupFunc := createDBConnection(cfg)
	if i != "" {
		if err := datastore.EnsureIndexForTest(conn, "orders", "setting_on_top_fares", "heatmap"); err != nil {
			panic(err)
		}
	}
	return conn, cleanupFunc
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideDBMonitorConnectionForTest(i InitTestDBConn) (*mongoprofiler.MonitorConn, func()) {
	cfg := mongodb.LoadConfigFromEnv()
	if i != "" {
		cfg.Name = string(i)
	}
	cfg.Name = "admin"
	cfg.Username = os.Getenv("DB_MONITOR_USERNAME")
	cfg.Password = os.Getenv("DB_MONITOR_PASSWORD")
	monitorConn, cleanupFunc := createDBConnection(cfg)
	return &mongoprofiler.MonitorConn{Conn: monitorConn}, cleanupFunc
}

// For integration testing
// @@wire-set-name@@ name:"IntegrationTest"
func ProvideGinEngineRouter(apis *router.APISet, builder *middlewares.Builder, apiSpecConf config.APISpecConfig, routerConfig config.RouterConfig, featureFlag featureflag.Service) *gin.Engine {
	rt := gin.Default()
	router.Register(rt, apis, builder, apiSpecConf, routerConfig, featureFlag)
	return rt
}

// @@no-locator-generation@@
func ProvideInitTestData(d *testdata.Fixtures, conn *mongodb.Conn) (i InitTestData) {
	if err := d.InitDefault(conn); err != nil {
		panic(err)
	}
	return
}

// @@no-locator-generation@@
func ProvideInitMetric() (i InitMetric) {
	metric.RegisterCollector(metric.NewPrometheusCollector())
	return
}

// @@no-locator-generation@@
func ProvideInitSentry(cfg psentry.Config) (i InitSentry, cleanup func()) {
	if err := psentry.Init(&cfg); err != nil {
		panic(err)
	}

	cleanup = func() {
		sentry.Flush(time.Second * 5)
	}
	return
}

func (InitToggle) Parse() {
	cfg := toggle.Config{}
	if err := envconfig.Process("", &cfg); err != nil {
		logrus.WithError(err).Errorf("cannot load feature toggle from database config, error[%v]", err)
		return
	}

	toggle.Enable(cfg.Features...)
}

func ProvideInitToggle(configUpdater *config.DBConfigUpdater, meter metric.Meter) (i InitToggle) {
	configUpdater.Register(i)
	toggle.RegisterCollector(meter)
	return
}

func CommonCmdAppInitializer(_ InitFirst, _ InitSentry, _ InitVOS, _ *mongodb.Conn, _ InitMetric, _ InitModel,
	_ InitToggle, _ InitTopkek, _ datastore.SecondaryDBConnection) (ini ContainerInitializer) {
	return
}

func BCPAppInitializer(_ InitFirst) (ini ContainerInitializer) {
	return
}

func ProvideTopkekConfig() setup.Config {
	var config setup.Config
	envconfig.MustProcess("TOPKEK", &config)
	return config
}

// @@no-locator-generation@@
func ProvideTopkek(config setup.Config, conn *mongodb.Conn) (InitTopkek, func()) {
	stop, err := RegisterKeyAead(context.Background(), config)
	if err != nil {
		panic(err)
	}
	err = topkekmongo.RegisterAll(context.Background(), conn.Database(), topkekmongo.WithUpdateInterval(config.CacheTTL))
	if err != nil {
		panic(err)
	}

	return "", stop
}

// RegisterKeyAead interim solution to load topkek key aead from file FIXME lmf-9985 using dev vault to load key instead of file
func RegisterKeyAead(ctx context.Context, config setup.Config) (func(), error) {
	if config.VaultURI == "" && config.KeysetPath != "" {
		path := config.KeysetPath
		stopFn := func() {}
		if _, err := os.Stat(path); os.IsNotExist(err) {
			// This mode is intended for local development - hence the insecure mode
			// (the key is stored unencrypted anyway...)
			f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE, 0644)
			if err != nil {
				return stopFn, err
			}
			defer f.Close()

			// generate new keyset if the file did not exists
			handle, err := keyset.NewHandle(aead.AES128GCMKeyTemplate())
			if err != nil {
				return stopFn, err
			}

			err = insecurecleartextkeyset.Write(handle, keyset.NewJSONWriter(f))
			if err != nil {
				return stopFn, err
			}

			logx.Info().Msgf("Created new KEK keyset at %s", path)

			return stopFn, nil
		}

		f, err := os.Open(path)
		if err != nil {
			return stopFn, err
		}
		defer f.Close()

		handle, err := insecurecleartextkeyset.Read(keyset.NewJSONReader(f))
		if err != nil {
			return stopFn, err
		}
		wrappedPs, err := topkek.WrapTinkHandle[tink.AEAD](handle)
		if err != nil {
			return stopFn, err
		}
		topkek.RegisterKey("keyAEAD", wrappedPs)

		return stopFn, nil
	}

	return setup.RegisterKeyAead(ctx, config)
}

// redefine Unleash wireset, remove unleashprovider.ProvideUnleash to avoid directly using unleasher
var UnleashWireSet = wire.NewSet(
	unleashprovider.ProvideUnleashConfig,
	unleashprovider.ProvideAdminConfig,
	strategies.ProvideStrategies,
	admin.ProvideUnleashAdmin,
)
