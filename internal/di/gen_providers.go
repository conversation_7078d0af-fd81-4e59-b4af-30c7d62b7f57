// Code generated by providergen version v1.4.0. DO NOT EDIT.

package di

import (
	"github.com/google/wire"

	ggggrpclib "git.wndv.co/go/grpclib"
	ggpdfvv2 "git.wndv.co/go/proto/dap/feast/v2"
	ggplavv1 "git.wndv.co/go/proto/lineman/auth/v1"
	ggplcvv1 "git.wndv.co/go/proto/lineman/chat/v1"
	ggplevv1 "git.wndv.co/go/proto/lineman/egs/v1"
	ggplfpvv1 "git.wndv.co/go/proto/lineman/fleet/pool/v1"
	ggplfsvv1 "git.wndv.co/go/proto/lineman/form_service/v1"
	ggplivv1 "git.wndv.co/go/proto/lineman/inventory/v1"
	ggplpvv2 "git.wndv.co/go/proto/lineman/polygon/v2"
	ggpltvv1 "git.wndv.co/go/proto/lineman/translation/v1"
	gladvv2 "git.wndv.co/lineman/absinthe/database/v2"
	gladvttransaction "git.wndv.co/lineman/absinthe/database/v2/transaction"
	glfiaaassignmentbenchmarks "git.wndv.co/lineman/fleet-distribution/internal/apis/assignmentbenchmarks"
	glfiaddbconfig "git.wndv.co/lineman/fleet-distribution/internal/apis/dbconfig"
	glfiadzdedicated_zone "git.wndv.co/lineman/fleet-distribution/internal/apis/dedicated_zone"
	glfiaddeliveryfee "git.wndv.co/lineman/fleet-distribution/internal/apis/deliveryfee"
	glfiaddriver "git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	glfiaddriverinsurance "git.wndv.co/lineman/fleet-distribution/internal/apis/driverinsurance"
	glfiaddriverorderinfo "git.wndv.co/lineman/fleet-distribution/internal/apis/driverorderinfo"
	glfiaeegs "git.wndv.co/lineman/fleet-distribution/internal/apis/egs"
	glfiaeevent "git.wndv.co/lineman/fleet-distribution/internal/apis/event"
	glfiaffeatureflagconfig "git.wndv.co/lineman/fleet-distribution/internal/apis/featureflagconfig"
	glfiaffraud "git.wndv.co/lineman/fleet-distribution/internal/apis/fraud"
	glfiahheatmap "git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	glfiaiincentive "git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	glfiaiincentivesource "git.wndv.co/lineman/fleet-distribution/internal/apis/incentivesource"
	glfiaiinternalapi "git.wndv.co/lineman/fleet-distribution/internal/apis/internalapi"
	glfiammiddlewares "git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	glfiaoorder "git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	glfiaoddispatcherconfig "git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	glfiaoddistribution "git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	glfiaooorderassigner "git.wndv.co/lineman/fleet-distribution/internal/apis/order/orderassigner"
	glfiaootp "git.wndv.co/lineman/fleet-distribution/internal/apis/otp"
	glfiappartners "git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	glfiapaauth "git.wndv.co/lineman/fleet-distribution/internal/apis/partners/auth"
	glfiappayment "git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	glfiapphones "git.wndv.co/lineman/fleet-distribution/internal/apis/phones"
	glfiapproduct "git.wndv.co/lineman/fleet-distribution/internal/apis/product"
	glfiapprovince "git.wndv.co/lineman/fleet-distribution/internal/apis/province"
	glfiarsrain_situation "git.wndv.co/lineman/fleet-distribution/internal/apis/rain_situation"
	glfiarrating "git.wndv.co/lineman/fleet-distribution/internal/apis/rating"
	glfiarregion "git.wndv.co/lineman/fleet-distribution/internal/apis/region"
	glfiarreward "git.wndv.co/lineman/fleet-distribution/internal/apis/reward"
	glfiasshift "git.wndv.co/lineman/fleet-distribution/internal/apis/shift"
	glfiassrvarea "git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	glfiassummaryofchange "git.wndv.co/lineman/fleet-distribution/internal/apis/summaryofchange"
	glfiatothrottled_order "git.wndv.co/lineman/fleet-distribution/internal/apis/throttled_order"
	glfiatthrottleddispatchdetail "git.wndv.co/lineman/fleet-distribution/internal/apis/throttleddispatchdetail"
	glfiattrip "git.wndv.co/lineman/fleet-distribution/internal/apis/trip"
	glfiawwithholdingtaxcertificate "git.wndv.co/lineman/fleet-distribution/internal/apis/withholdingtaxcertificate"
	glfiazzone "git.wndv.co/lineman/fleet-distribution/internal/apis/zone"
	glfiaasynqclient "git.wndv.co/lineman/fleet-distribution/internal/asynqclient"
	glfiaauth "git.wndv.co/lineman/fleet-distribution/internal/auth"
	glfiaaws "git.wndv.co/lineman/fleet-distribution/internal/aws"
	glficconfig "git.wndv.co/lineman/fleet-distribution/internal/config"
	glficconfiglocator "git.wndv.co/lineman/fleet-distribution/internal/configlocator"
	glficbbcp "git.wndv.co/lineman/fleet-distribution/internal/connector/bcp"
	glficccache "git.wndv.co/lineman/fleet-distribution/internal/connector/cache"
	glficddelivery "git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	glficddispatcher "git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	glficeexperimentplatform "git.wndv.co/lineman/fleet-distribution/internal/connector/experimentplatform"
	glficffile "git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	glficffleetarea "git.wndv.co/lineman/fleet-distribution/internal/connector/fleetarea"
	glficffleetorder "git.wndv.co/lineman/fleet-distribution/internal/connector/fleetorder"
	glficffraudadvisor "git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor"
	glfichheatmapdemand "git.wndv.co/lineman/fleet-distribution/internal/connector/heatmapdemand"
	glficicinet_client "git.wndv.co/lineman/fleet-distribution/internal/connector/inet_client"
	glfickkafcclient "git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	glfickkkafcdistribution "git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution"
	glficlline "git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	glficllineinternal "git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	glficllocker "git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	glficmmapservice "git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	glficmmmanmap "git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/manmap"
	glficmmatchrate "git.wndv.co/lineman/fleet-distribution/internal/connector/matchrate"
	glficmmongoprofiler "git.wndv.co/lineman/fleet-distribution/internal/connector/mongoprofiler"
	glficmmongotxn "git.wndv.co/lineman/fleet-distribution/internal/connector/mongotxn"
	glficmmq "git.wndv.co/lineman/fleet-distribution/internal/connector/mq"
	glficppolygon "git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	glficpprediction "git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	glficrriderlevel "git.wndv.co/lineman/fleet-distribution/internal/connector/riderlevel"
	glficsslack "git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	glficssms "git.wndv.co/lineman/fleet-distribution/internal/connector/sms"
	glficuuobclient "git.wndv.co/lineman/fleet-distribution/internal/connector/uobclient"
	glficuuwterror "git.wndv.co/lineman/fleet-distribution/internal/connector/uwterror"
	glfiddatastore "git.wndv.co/lineman/fleet-distribution/internal/datastore"
	glfidmmodel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	glfidrrepository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	glfidsservice "git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	glfidsaaccountinghub "git.wndv.co/lineman/fleet-distribution/internal/domain/service/accountinghub"
	glfidsbbcp "git.wndv.co/lineman/fleet-distribution/internal/domain/service/bcp"
	glfidsddrivertransaction "git.wndv.co/lineman/fleet-distribution/internal/domain/service/drivertransaction"
	glfidsfform "git.wndv.co/lineman/fleet-distribution/internal/domain/service/form"
	glfidsffraud "git.wndv.co/lineman/fleet-distribution/internal/domain/service/fraud"
	glfidsoorder "git.wndv.co/lineman/fleet-distribution/internal/domain/service/order"
	glfidsppendingtransaction "git.wndv.co/lineman/fleet-distribution/internal/domain/service/pendingtransaction"
	glfidsttransaction "git.wndv.co/lineman/fleet-distribution/internal/domain/service/transaction"
	glfieemail "git.wndv.co/lineman/fleet-distribution/internal/email"
	glfieeevent "git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	glfiffeatureflag "git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	glfiggrpc "git.wndv.co/lineman/fleet-distribution/internal/grpc"
	glfigcchat "git.wndv.co/lineman/fleet-distribution/internal/grpc/chat"
	glfigcmcmock_chat "git.wndv.co/lineman/fleet-distribution/internal/grpc/chat/mock_chat"
	glfigccoinplan "git.wndv.co/lineman/fleet-distribution/internal/grpc/coinplan"
	glfigcmcmock_coinplan "git.wndv.co/lineman/fleet-distribution/internal/grpc/coinplan/mock_coinplan"
	glfigddapfeast "git.wndv.co/lineman/fleet-distribution/internal/grpc/dapfeast"
	glfigddriverprovision "git.wndv.co/lineman/fleet-distribution/internal/grpc/driverprovision"
	glfigeegs "git.wndv.co/lineman/fleet-distribution/internal/grpc/egs"
	glfigemgmock_grpc "git.wndv.co/lineman/fleet-distribution/internal/grpc/egs/mock_grpc"
	glfigeegsbranch "git.wndv.co/lineman/fleet-distribution/internal/grpc/egsbranch"
	glfigeegsorder "git.wndv.co/lineman/fleet-distribution/internal/grpc/egsorder"
	glfigffeatureplatform "git.wndv.co/lineman/fleet-distribution/internal/grpc/featureplatform"
	glfigffleetpool "git.wndv.co/lineman/fleet-distribution/internal/grpc/fleetpool"
	glfigfformservice "git.wndv.co/lineman/fleet-distribution/internal/grpc/formservice"
	glfigfmgmock_grpc "git.wndv.co/lineman/fleet-distribution/internal/grpc/formservice/mock_grpc"
	glfigiinventoryservice "git.wndv.co/lineman/fleet-distribution/internal/grpc/inventoryservice"
	glfigmmarketplace "git.wndv.co/lineman/fleet-distribution/internal/grpc/marketplace"
	glfigmmgmock_grpc "git.wndv.co/lineman/fleet-distribution/internal/grpc/marketplace/mock_grpc"
	glfigppolygon "git.wndv.co/lineman/fleet-distribution/internal/grpc/polygon"
	glfigpmgmock_grpc "git.wndv.co/lineman/fleet-distribution/internal/grpc/polygon/mock_grpc"
	glfigppriceintervention "git.wndv.co/lineman/fleet-distribution/internal/grpc/priceintervention"
	glfigrrainservice "git.wndv.co/lineman/fleet-distribution/internal/grpc/rainservice"
	glfigrmrmock_rainservice "git.wndv.co/lineman/fleet-distribution/internal/grpc/rainservice/mock_rainservice"
	glfigttranslationservice "git.wndv.co/lineman/fleet-distribution/internal/grpc/translationservice"
	glfiguuser "git.wndv.co/lineman/fleet-distribution/internal/grpc/user"
	glfigumgmock_grpc "git.wndv.co/lineman/fleet-distribution/internal/grpc/user/mock_grpc"
	glfihhttpclient "git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	glfiiincome "git.wndv.co/lineman/fleet-distribution/internal/income"
	glfiiaaggregate "git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	glfiiinfrastructure "git.wndv.co/lineman/fleet-distribution/internal/infrastructure"
	glfiiccache "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	glfiieexecutor "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/executor"
	glfiippersistence "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	glfilccleanup "git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
	glfilllocalcache "git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
	glfimmessages "git.wndv.co/lineman/fleet-distribution/internal/messages"
	glfinnotification "git.wndv.co/lineman/fleet-distribution/internal/notification"
	glfinffirebase "git.wndv.co/lineman/fleet-distribution/internal/notification/firebase"
	glfinssocketio "git.wndv.co/lineman/fleet-distribution/internal/notification/socketio"
	glfinsstub "git.wndv.co/lineman/fleet-distribution/internal/notification/stub"
	glfippreload "git.wndv.co/lineman/fleet-distribution/internal/preload"
	glfissafe "git.wndv.co/lineman/fleet-distribution/internal/safe"
	glfisserviceprovider "git.wndv.co/lineman/fleet-distribution/internal/serviceprovider"
	glfitmmetric "git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	glfitpproviders "git.wndv.co/lineman/fleet-distribution/internal/testutil/providers"
	glfitttestdata "git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	glfittmpl "git.wndv.co/lineman/fleet-distribution/internal/tmpl"
	ggggin "github.com/gin-gonic/gin"
	ghaasynq "github.com/hibiken/asynq"
)

// nolint
var ConfigSet = wire.NewSet(
	glfiaddriver.ProvideRegisterHandlerConfig,
	glfiaddriver.ProvideOrderConfig,
	glfiaddriver.ProvideFinancialRiskConfig,
	glfiaddriver.ProvideDriverDocumentConfig,
	glfiaddriverinsurance.ProvideInsuranceConfig,
	glfiaeegs.ProvideHookConfig,
	glfiahheatmap.ProvideHeatMapConfig,
	glfiammiddlewares.ProvideMiddlewareConfig,
	glfiaoorder.ProvideOrderAPIConfig,
	glfiaoorder.ProvideDeliveryAPIFeeConfig,
	glfiaoorder.ProvideContingencyConfig,
	glfiaoddispatcherconfig.ProvideAutoAssignDbConfig,
	glfiaoddispatcherconfig.ProvideAutoAcceptConfig,
	glfiaoddispatcherconfig.ProvideDistributionConfig,
	glfiaoddispatcherconfig.ProvideAtomicDistributionConfig,
	glfiaoddistribution.ProvideAutoAssignConfig,
	glfiaoorder.ProvideAtomicOrderDBConfig,
	glfiapaauth.ProvideConfig,
	glfiappartners.ProvideUobApiConfig,
	glfiappartners.ProvideCitiApiConfig,
	glfiapphones.ProvidePhonesConfig,
	glfiapproduct.ProvideProductConfig,
	glfiarsrain_situation.ProvideConfig,
	glfiarregion.ProvideAtomicRegionConfig,
	glfiarreward.ProvideConfig,
	glfiasshift.ProvideShiftConfig,
	glfiasshift.ProvideShiftCancelReasonConfig,
	glfiawwithholdingtaxcertificate.ProvideConfig,
	glfiaaws.ProvideConfig,
	glficconfig.ProvideAdminConfig,
	glficconfig.ProvideBackToBackConfig,
	glficconfig.ProvideCancelReasonConfig,
	glficconfig.ProvideGlobalConfig,
	glficconfig.ProvideAtomicMapOverrideConfig,
	glficconfig.ProvideServiceAreaRepositoryConfig,
	glficconfig.ProvideDeliveryFeeSettingRepositoryConfig,
	glficconfig.ProvideDeliveryFeeSettingPriceSchemesRepositoryConfig,
	glficconfig.ProvideDriverRatingConfig,
	glficconfig.ProvideAttendanceRateConfig,
	glficconfig.ProvideAtomicCancellationRateConfig,
	glficconfig.ProvideAtomicTripConfig,
	glficconfig.ProvideAtomicSupplyPositioningConfig,
	glficconfig.ProvideDriverPeriodCompletedTripsConfig,
	glficconfig.ProvideAPISpecConfig,
	glficconfig.ProvideRouterConfig,
	glficconfig.ProvideSentryConfig,
	glficconfig.ProvideLocationManagerConfig,
	glficconfig.ProvideDriverProfileRequestConfig,
	glficconfig.ProvideAtomicDedicatedPriorityScorerConfig,
	glficconfig.ProvideAtomicNegativeCreditConfig,
	glficconfig.ProvideAtomicMongoTxnConfig,
	glficconfig.ProvideAtomicMongoProfilerConfig,
	glficconfig.ProvideMongoProfilerConfig,
	glficconfig.ProvideThrottledOrderConfig,
	glficconfig.ProvideDriverTransactionConfig,
	glficconfig.ProvideEGSConfig,
	glficconfig.ProvideAtomicEGSDBConfig,
	glficconfig.ProvideFormConfig,
	glficconfig.ProvideIncentiveSourceConfig,
	glficconfig.ProvidePaymentConfig,
	glficconfig.ProvideAtomicPredictionServiceConfig,
	glficconfig.ProvideServiceAreaConfig,
	glficconfig.ProvideGlobalServiceAreaConfig,
	glficddelivery.ProvideDeliveryConfig,
	glficddelivery.ProvideDeliveryClientConfig,
	glficddispatcher.ProvideDispatcherConfig,
	glficeexperimentplatform.ProvideDistributionExperimentPlatformDbConfig,
	glficffile.ProvideVosConfig,
	glficffleetarea.ProvideFleetAreaClientConfig,
	glficffleetorder.ProvideDispatcherConfig,
	glficffraudadvisor.ProvideFraudConfig,
	glficicinet_client.ProvideINetClientConfig,
	glfickkkafcdistribution.ProvideDistributionKafkaConsumerConfig,
	glfickkkafcdistribution.ProvideDistributionKafcConsumerConfig,
	glfickkkafcdistribution.ProvideSecureIMFKafkaSyncProducerConfig,
	glfickkafcclient.ProvideIMFKafkaProducerConfig,
	glfickkafcclient.ProvideSecureIMFKafkaProducerConfig,
	glficlline.ProvideEnvironmentConfig,
	glficlline.ProvideLINEHTTPClientConfig,
	glficllineinternal.ProvideLINEInternalConfig,
	glficmmmanmap.ProvideManMapConfig,
	glficmmatchrate.ProvideConfig,
	glficmmq.ProvideKafkaConsumerConfig,
	glficppolygon.ProvidePolygonConfig,
	glficpprediction.ProvidePredictionConfig,
	glficrriderlevel.ProvideRiderLevelConnectorConfig,
	glficsslack.ProvideSlackConfig,
	glficssms.ProvideSMSConfig,
	glficuuobclient.ProvideUobConfig,
	glficuuwterror.ProvideConfig,
	ProvideDBConfig,
	ProvideTopkekConfig,
	glfidmmodel.ProvideOTPConfig,
	glfidsservice.ProvideAtomicCancelReasonConfig,
	glfidsservice.ProvideDistributionLogEventServiceConfig,
	glfidsservice.ProvideDistributionServiceConfig,
	glfidsservice.ProvideAtomicDriverServiceConfig,
	glfidsservice.ProvideUpdateDriverLocationEventConfig,
	glfidsservice.ProvideAtomicFormServiceConfig,
	glfidsservice.ProvideInstallmentConfig,
	glfidsservice.ProvideAtomicInstallmentDBConfig,
	glfidsservice.ProvideMissionLogEventServiceConfig,
	glfidsservice.ProvideConfig,
	glfidsservice.ProvideOrderDistributionEventServiceConfig,
	glfidsservice.ProvideOrderHeartbeatServiceConfig,
	glfidsservice.ProvideAtomicRainSituationConfig,
	glfidsservice.ProvideServiceOptInReminderServiceConfig,
	glfidsservice.ProvideAtomicServiceOptInReminderServiceConfig,
	glfidsservice.ProvideVosInternalConfig,
	glfidsservice.ProvideVosFleetConfig,
	glfieemail.ProvideEmailConfig,
	glfieeevent.ProvideDriverStatisticConfig,
	glfieeevent.ProvideAtomicDriverStatisticConfig,
	glfiffeatureflag.ProvideUnleashConfig,
	glfigcchat.ProvideConfig,
	glfigccoinplan.ProvideConfig,
	glfigddapfeast.ProvideConfig,
	glfigddriverprovision.ProvideConfig,
	glfigeegs.ProvideConfig,
	glfigeegsbranch.ProvideConfig,
	glfigeegsorder.ProvideConfig,
	glfigffeatureplatform.ProvideConfig,
	glfigffleetpool.ProvideConfig,
	glfigfformservice.ProvideConfig,
	glfigiinventoryservice.ProvideConfig,
	glfigmmarketplace.ProvideConfig,
	glfigppolygon.ProvideConfig,
	glfigppriceintervention.ProvideConfig,
	glfigrrainservice.ProvideConfig,
	glfigttranslationservice.ProvideConfig,
	glfiguuser.ProvideConfig,
	glfihhttpclient.ProvideFraudAdvisorHTTPClientConfig,
	glfiiccache.ProvideCoinCashConversionRateLocalCacheConfig,
	glfiiinfrastructure.ProvideKafkaConfig,
	glfiippersistence.ProvideOrderConfig,
	glfiippersistence.ProvideRevisionConfig,
	glfiiinfrastructure.ProvideRepConfig,
	glfilllocalcache.ProvideLocalCacheConfig,
	glfimmessages.ProvideConfig,
	glfinffirebase.ProvideFirebaseConfig,
	glfinffirebase.ProvideAppOptionConfig,
	glfinssocketio.ProvideSocketIOConfig,
	glfinssocketio.ProvideConfig,
	glfissafe.ProvideRecoveryConfig,
	glfissafe.ProvideWorkerContextConfig,
)

// nolint
var DriverTransactionProvider = wire.NewSet(
	glfidsddrivertransaction.ProvideInstallmentOnTopTransactionProvider,
)

// nolint
var EventTrackingSet = wire.NewSet(
	glfieeevent.ProvideDriverMarker,
)

// nolint
var GRPCClient = wire.NewSet(
	glfigcchat.ProvideChatServiceClient,
)

// nolint
var IntegrationTest = wire.NewSet(
	glficconfiglocator.ProvideAtomicConfigLocator,
	glficbbcp.ProvideBCPTestClientStub,
	glficddelivery.ProvideStubDeliveryFleetApi,
	glfickkkafcdistribution.ProvideSecureIMFKafkaSyncProducerClientForTest,
	glfickkafcclient.ProvideIMFKafkaProducerClientForTest,
	glfickkafcclient.ProvideSecureIMFKafkaProducerClientForTest,
	glficlline.ProvideClientStub,
	glficmmmanmap.ProvideStubMapService,
	glficrriderlevel.ProvideStubRiderLevelConnector,
	glficsslack.ProvideSlackStub,
	glficuuwterror.ProvideStubUWTErrorService,
	ProvideDBConnectionForTest,
	ProvideDBMonitorConnectionForTest,
	ProvideGinEngineRouter,
	glfiffeatureflag.ProvideStubUnleashAdmin,
	glfiffeatureflag.ProvideStubLMWNUnleasher,
	glfiffeatureflag.ProvideFeatureFlagServiceForIntegration,
	glfigcchat.ProvideStubChatGRPCService,
	glfigccoinplan.ProvideStubCoinPlanGRPCClient,
	glfigddapfeast.ProvideStubGRPCFeatureV2ServiceClient,
	glfigddriverprovision.ProvideStubGRPCRecommendationService,
	glfigeegs.ProvideStubGRPCEGSService,
	glfigeegsbranch.ProvideStubGRPCEGSBranchService,
	glfigeegsorder.ProvideStubGRPCEGSOrderService,
	glfigffeatureplatform.ProvideStubGPRCFeaturePlatformClient,
	glfigfformservice.ProvideStubGRPCFormService,
	glfigiinventoryservice.ProvideStubGRPCPriorityGroupService,
	glfigiinventoryservice.ProvideStubGRPCProductService,
	glfigmmarketplace.ProvideStubGRPCMarketplaceService,
	glfigppolygon.ProvideStubGRPCUserPolygonService,
	glfigppriceintervention.ProvidePriceInterventionServiceClientStub,
	glfigrrainservice.ProvideRainServiceClientStub,
	glfiguuser.ProvideStubGRPCUserService,
	glfihhttpclient.ProvideFraudAdvisorHTTPClientStub,
	glfihhttpclient.ProvideLINEInternalHTTPClientStub,
	glfiiinfrastructure.ProvideStubKafkaConnector,
	glfiippersistence.ProvideDriverLocationRepositoryForTest,
	glfitpproviders.ProvideServiceGRPCConnFactory,
	glfitpproviders.ProvideStubPolygonApiForTest,
	glfitpproviders.ProvideTopkekForTest,
	glfitttestdata.ProvideFixtures,
)

// nolint
var Main = wire.NewSet(
	glficddelivery.ProvideclientFleetAPIClient,
	glfickkafcclient.ProvideIMFKafcProducer,
	glfickkafcclient.ProvideSecureIMFKafcProducer,
	glfickkkafcdistribution.ProvideSecureIMFKafkaSyncProducer,
	glficlline.ProvideClient,
	glficmmmanmap.ProvideMapServiceClient,
	glficsslack.ProvideSlack,
	glficuuwterror.ProvideUWTErrorService,
	glfiffeatureflag.ProvideFeatureFlagService,
	glfigccoinplan.ProvideCoinPlanGRPCClient,
	glfigddapfeast.ProvideGRPCFeatureV2ServiceClient,
	glfigddriverprovision.ProvideGRPCDriverProvisionClient,
	glfigeegs.ProvideGRPCEGSServiceClient,
	glfigeegsbranch.ProvideGRPCEGSBranchServiceClient,
	glfigeegsorder.ProvideGRPCEGSOrderServiceClient,
	glfigfformservice.ProvideGRPCFormServiceClient,
	glfiggrpc.ProvideGRPCConnectionFactory,
	glfigiinventoryservice.ProvideGRPCPriorityGroupServiceClient,
	glfigiinventoryservice.ProvideGRPCProductServiceClient,
	glfigmmarketplace.ProvideGRPCMarketplaceServiceClient,
	glfigppolygon.ProvideGRPCUserPolygonServiceClient,
	glfigppriceintervention.ProvidePriceInterventionClient,
	glfigrrainservice.ProvideRainServiceClient,
	glfiguuser.ProvideGRPCUserServiceClient,
	glfihhttpclient.ProvideFraudAdvisorHTTPClient,
	glfihhttpclient.ProvideLINEInternalHTTPClient,
	glfiippersistence.ProvideDriverLocationRepository,
)

// nolint
var MainConfigSet = wire.NewSet(
	glficconfig.ProvideLocationRedisConfig,
	glficconfig.ProvideDriverStatRedisConfig,
	glficbbcp.ProvideConfig,
	glfiddatastore.ProvideRedisConfig,
	ProvideDBConnection,
	ProvideDBMonitorConnection,
	glfiippersistence.ProvideDriverLocationConfig,
)

// nolint
var ProviderSet = wire.NewSet(
	glfiaaassignmentbenchmarks.ProvideAssignmentBenchmarkAPI,
	glfiaddbconfig.ProvideDbConfigAdminAPI,
	glfiadzdedicated_zone.ProvideDedicatedZoneAPI,
	glfiaddeliveryfee.ProvideDeliveryFeeAPI,
	glfiaddriver.ProvideDocument,
	glfiaddriver.ProvideDriverAdminAPI,
	glfiaddriver.ProvideDriverNotifyAPI,
	glfiaddriver.ProvideDriverSalesforceAPI,
	glfiaddriver.ProvideDriverStatusAPI,
	glfiaddriver.ProvidePdpaApi,
	glfiaddriver.ProvideRegistrationAdminAPI,
	glfiaddriver.ProvideRegistrationAPI,
	glfiaddriverinsurance.ProvideInsuranceAPI,
	glfiaddriverorderinfo.ProvideAutoBanCompleteOrderTooOftenCfg,
	glfiaddriverorderinfo.ProvideAutoBanCompleteOrderTooFarCfg,
	glfiaddriverorderinfo.ProvideDriverOrderInfoAPI,
	glfiaeevent.ProvideEventAPI,
	glfiaffeatureflagconfig.ProvideFeatureFlagConfigAPI,
	glfiaffraud.ProvideFraudAPI,
	glfiahheatmap.ProvideHeatMapAPI,
	glfiaiincentive.ProvideIncentiveAPI,
	glfiaiincentive.ProvideIncentiveProgressDataStore,
	glfiaiincentive.ProvideDataStoreIncentiveProgressRepository,
	glfiaiincentive.ProvideIncentiveDataStore,
	glfiaiincentive.ProvideDataStoreIncentiveRepository,
	glfiaiincentivesource.ProvideIncentiveSourceAPI,
	glfiaiinternalapi.ProvideInternalAPI,
	glfiammiddlewares.ProvideMiddlewareBuilder,
	glfiaoorder.ProvideAcceptorDeps,
	glfiaoorder.ProvideAcceptor,
	glfiaoorder.ProvideAdminAPI,
	glfiaoorder.ProvideDeliveryAPIPortal,
	glfiaoorder.ProvideCanceller,
	glfiaoddistribution.ProvideAssigningStateManager,
	glfiaoddistribution.ProvideAutoAssignOrderDistributor,
	glfiaoddistribution.ProvideAutoAssignOrderDistributorDeps,
	glfiaoddistribution.ProvideConfigValidator,
	glfiaoorder.ProvideMemLocker,
	glfiaoorder.ProvideOrderAPI,
	glfiaoorder.ProvideOrderPortalAPI,
	glfiaooorderassigner.ProvideOrderAssignerHandler,
	glfiaoorder.ProvideFoodProvider,
	glfiaoorder.ProvideProviderDeps,
	glfiaootp.ProvideOTPAPI,
	glfiapaauth.ProvideCitiAuthenticationService,
	glfiappartners.ProvideCitiAPI,
	glfiappartners.ProvideUobAPI,
	glfiappayment.ProvideApprovalAPI,
	glfiappayment.ProvideApprovalCreator,
	glfiappayment.ProvideApprovalService,
	glfiappayment.ProvideDriverTransactionAPI,
	glfiappayment.ProvideDriverTransactionService,
	glfiappayment.ProvideGroupTransactionAPI,
	glfiappayment.ProvidePaymentService,
	glfiappayment.ProvideUOBPayoutMetric,
	glfiappayment.ProvideTransactionAPI,
	glfiappayment.ProvideTransactionSchemeAPI,
	glfiappayment.ProvideTransactionService,
	glfiapphones.ProvidePhoneAPI,
	glfiapproduct.ProvideProductAPI,
	glfiapprovince.ProvideProvinceAdminAPI,
	glfiapprovince.ProvideProvinceAPI,
	glfiarsrain_situation.ProvideRainSituationAdminAPI,
	glfiarsrain_situation.ProvideRainSituationAPI,
	glfiarrating.ProvideRatingAdminAPI,
	glfiarrating.ProvideRatingOptionAPI,
	glfiarregion.ProvideRegionAdminAPI,
	glfiarregion.ProvideRegionAPI,
	glfiarreward.ProvideRewardAdminAPI,
	glfiasshift.ProvideShiftAPI,
	glfiassrvarea.ProvideClientAreaAPI,
	glfiassrvarea.ProvideServiceAreaSettingAPI,
	glfiassummaryofchange.ProvideSummaryOfChangeAPI,
	glfiatothrottled_order.ProvideThrottledOrderAPI,
	glfiatthrottleddispatchdetail.ProvideThrottledDispatchDetailAdminAPI,
	glfiattrip.ProvideTripAPI,
	glfiawwithholdingtaxcertificate.ProvideWithholdingTaxCertificateAPI,
	glfiazzone.ProvideZoneAdminAPI,
	glfiaasynqclient.ProvideAsynqClient,
	glfiaauth.ProvideRedisTokenStore,
	glfiaaws.ProvideRekognitionClient,
	glficconfig.ProvideDBConfigDataStore,
	glficconfig.ProvideDBConfigRepository,
	glficbbcp.ProvideBCPClient,
	glficccache.ProvideCache,
	glficddelivery.ProvideDelivery,
	glficddispatcher.ProvideDriverServiceDispatcher,
	glficeexperimentplatform.ProvideDistributionExperimentPlatformClient,
	glficffleetarea.ProvideFleetAreaClient,
	glficffleetorder.ProvideFleetOrderClient,
	glficffraudadvisor.ProvideFraudAdvisorServiceImpl,
	glfichheatmapdemand.ProvideHeatMapDemand,
	glficicinet_client.ProvideINetClient,
	glfickkafcclient.ProvideVerdaKafkaCfg,
	glfickkafcclient.ProvideVerdaKafkaClient,
	glfickkafcclient.ProvideIMFKafkaClient,
	glfickkafcclient.ProvideSecureIMFKafkaClient,
	glfickkkafcdistribution.ProvideDistributionKafkaConsumer,
	glfickkkafcdistribution.ProvideSecureIMFKafkaSyncClient,
	glficllineinternal.ProvideLINEClient,
	glficllineinternal.ProvideLINEInternalClient,
	glficllocker.ProvideRedisLocker,
	glficmmmanmap.ProvideExperimentalMapServiceClient,
	glficmmatchrate.ProvideFeaturePlatformService,
	glficmmongoprofiler.ProvideMongoProfiler,
	glficmmongotxn.ProvideMongoTxnHelper,
	glficmmq.ProvideKafkaConsumer,
	glficppolygon.ProvideHttpPolygon,
	glficpprediction.ProvidePrediction,
	glficrriderlevel.ProvideRiderLevelConnector,
	glficssms.ProvideSMS,
	glficuuobclient.ProvideUobClient,
	glfiddatastore.ProvideRedisConn,
	glfiddatastore.ProvideRedis,
	glfiddatastore.ProvideSecondaryDBConnection,
	ProvideInit1,
	ProvideInit2,
	ProvideDBConnectionV2,
	ProvideInitModel,
	ProvideInitTestDBConn,
	ProvideInitTestData,
	ProvideInitMetric,
	ProvideInitSentry,
	ProvideInitToggle,
	ProvideTopkek,
	glfidmmodel.ProvideNewOTPSession,
	glfidmmodel.ProvideTimeNow,
	glfidsaaccountinghub.ProvideAccountingHubTransactionService,
	glfidsservice.ProvideAreaServiceImpl,
	glfidsservice.ProvideAssignmentBenchmarkCfg,
	glfidsservice.ProvideAssignmentBenchmarkService,
	glfidsservice.ProvideAttendances,
	glfidsservice.ProvideAwsServiceImpl,
	glfidsservice.ProvideBanServiceImpl,
	glfidsbbcp.ProvideBCPStatusTransitionerService,
	glfidsservice.ProvideDeliveryFeeService,
	glfidsservice.ProvideDistributionLogEventService,
	glfidsservice.ProvideDistributionLogManager,
	glfidsservice.ProvideDistributionService,
	glfidsservice.ProvideDriverInsuranceService,
	glfidsservice.ProvideDriverInsuranceCfg,
	glfidsservice.ProvideQRIncidentOrderService,
	glfidsservice.ProvideDeviceManagerImpl,
	glfidsservice.ProvideDriverService,
	glfidsservice.ProvideDriverServiceTypeCapacityService,
	glfidsservice.ProvideDriverTransactionServiceV2,
	glfidsservice.ProvideDriverLocationEventServiceImpl,
	glfidsddrivertransaction.ProvideOnTopSchemeTransactionProviderSelector,
	glfidsservice.ProvideIncentiveSourceServiceImpl,
	glfidsservice.ProvideLocationManagerImpl,
	glfidsservice.ProvideMissionLogEventService,
	glfidsservice.ProvidePushNotifier,
	glfidsservice.ProvideOnTopFareService,
	glfidsservice.ProvideOrderDistributionEventManager,
	glfidsservice.ProvideOrderDistributionEventService,
	glfidsservice.ProvideOrderHeartbeatService,
	glfidsservice.ProvidePredictionService,
	glfidsservice.ProvideRainedCache,
	glfidsservice.ProvideRainSituationService,
	glfidsservice.ProvideRewardService,
	glfidsservice.ProvideServiceAreaServiceImpl,
	glfidsservice.ProvideServiceOptInReminderService,
	glfidsservice.ProvideServicePreferenceKillSwitchService,
	glfidsservice.ProvideServicePreferenceService,
	glfidsservice.ProvideShiftServices,
	glfidsservice.ProvideStatisticServiceImpl,
	glfidsservice.ProvideSupplyPositioningRecommenderService,
	glfidsservice.ProvideTermAndConditionServiceImpl,
	glfidsservice.ProvideThrottledOrderService,
	glfidsservice.ProvideTiersAndBenefitsService,
	glfidsservice.ProvideTranslationServiceImpl,
	glfidsservice.ProvideTripServices,
	glfidsservice.ProvideVOSServiceImpl,
	glfidsservice.ProvideZoneServiceImpl,
	glfieemail.ProvideEmailService,
	glfieeevent.ProvideDriverStatRedisClient,
	glfieeevent.ProvideDriverOrderInfoProcessor,
	glfieeevent.ProvideDuplicatePreventionStore,
	glfieeevent.ProvideExceedArDetector,
	glfieeevent.ProvidePoller,
	glfieeevent.ProvideGokaStream,
	glfigffeatureplatform.ProvideGRPCFeaturePlatformClient,
	glfigffleetpool.ProvideFleetPoolGRPCClient,
	glfigffleetpool.ProvideRiderSearchServiceClient,
	glfigttranslationservice.ProvideTranslationGRPCClient,
	glfigttranslationservice.ProvideTranslationServiceClient,
	glfihhttpclient.ProvideDefaultClient,
	glfihhttpclient.ProvideDalianClient,
	glfiiaaggregate.ProvideIncomeAggregateService,
	glfiiincome.ProvideIncomeSummaryService,
	glfiiccache.ProvideCoinConversionRateLocalCache,
	glfiiccache.ProvideCoinConversionRateRedisCache,
	glfiiccache.ProvideCoinConversionRateMinimalRedisCache,
	glfiieexecutor.ProvideInfraLocalTaskExecutor,
	glfiiinfrastructure.ProvideKafkaConnector,
	glfiippersistence.ProvideDataStoreApprovalRepository,
	glfiippersistence.ProvideApprovalDataStore,
	glfiippersistence.ProvideAssignmentBenchmarkDataStore,
	glfiippersistence.ProvideAssignmentBenchmarkRepository,
	glfiippersistence.ProvideMongoAssignmentLogRepository,
	glfiippersistence.ProvideDriverAssignmentLogsDataStore,
	glfiippersistence.ProvideDriverAssignmentLogsRevisionsDataStore,
	glfiippersistence.ProvideDataStoreAssignmentRejectionRepository,
	glfiippersistence.ProvideAssignmentRejectionDataStore,
	glfiippersistence.ProvideAssignmentDataStore,
	glfiippersistence.ProvideAssignmentRepository,
	glfiippersistence.ProvideDataStoreAttendanceLogHistoryRepository,
	glfiippersistence.ProvideAttendanceLogHistoryDataStore,
	glfiippersistence.ProvideAuditLogRepository,
	glfiippersistence.ProvideAuditLogDataStore,
	glfiippersistence.ProvideBanHistoryServiceImpl,
	glfiippersistence.ProvideBanHistoriesDataStore,
	glfiippersistence.ProvideBanMetadataDataStore,
	glfiippersistence.ProvideBanMetadataRepository,
	glfiippersistence.ProvideBCPOrderDataStore,
	glfiippersistence.ProvideBCPOrderRepository,
	glfiippersistence.ProvideDataStoreBulkProcessInfoRepository,
	glfiippersistence.ProvideBulkProcessInfoDataStore,
	glfiippersistence.ProvideDataStoreCancelReasonRepository,
	glfiippersistence.ProvideCancelReasonDataStore,
	glfiippersistence.ProvideMongoClientAreaRepository,
	glfiippersistence.ProvideClientAreaDataStore,
	glfiippersistence.ProvideCoinCashConversionRateRepository,
	glfiippersistence.ProvideCoinCashConversionRateDataStore,
	glfiippersistence.ProvideConsolidatedInsuranceRepository,
	glfiippersistence.ProvideConsolidatedInsuranceDataStore,
	glfiippersistence.ProvideCookingTimeDelayRepository,
	glfiippersistence.ProvideCounterDataStore,
	glfiippersistence.ProvideCounterRepository,
	glfiippersistence.ProvideMongoDailyRewardRepository,
	glfiippersistence.ProvideMongoDailyRewardDataStore,
	glfiippersistence.ProvideDedicatedZoneDataStore,
	glfiippersistence.ProvideDedicatedZoneRepository,
	glfiippersistence.ProvideDeferredOrderDataStore,
	glfiippersistence.ProvideDeferredOrderRepository,
	glfiippersistence.ProvideDeliveryFeeSettingDataStore,
	glfiippersistence.ProvideDeliveryFeeSettingMongo,
	glfiippersistence.ProvideRedisDriverActiveTimeRepository,
	glfiippersistence.ProvideDriverDocumentRepository,
	glfiippersistence.ProvideDriverDocumentDatastore,
	glfiippersistence.ProvideInsuranceRepository,
	glfiippersistence.ProvideDriverInsuranceDataStore,
	glfiippersistence.ProvideRedisLastUpdateLocationTrackerRepository,
	glfiippersistence.ProvideLocationRedisClient,
	glfiippersistence.ProvideDriverOrderInfoRepository,
	glfiippersistence.ProvideDriverOrderInfoDataStore,
	glfiippersistence.ProvideDriverRegistrationRepository,
	glfiippersistence.ProvideDriverRegistrationDataStore,
	glfiippersistence.ProvideDriverRepository,
	glfiippersistence.ProvideDriversDataStore,
	glfiippersistence.ProvideDataStoreDriverStatisticRepository,
	glfiippersistence.ProvideDriverStatisticDataStore,
	glfiippersistence.ProvideDataStoreDriverTransactionRepository,
	glfiippersistence.ProvideDriverTransactionDataStore,
	glfiippersistence.ProvideMongoGroupTransactionRepository,
	glfiippersistence.ProvideGroupTransactionDataStore,
	glfiippersistence.ProvideHeatMapDataStore,
	glfiippersistence.ProvideHeatMapRepository,
	glfiippersistence.ProvideIllegalDriverRepository,
	glfiippersistence.ProvideIncomeDailySummaryStore,
	glfiippersistence.ProvideIncomeDailySummaryRepository,
	glfiippersistence.ProvideInternalCancelReasonDataStore,
	glfiippersistence.ProvideInternalCancelReasonRepository,
	glfiippersistence.ProvideLINEStatelessTokenCacheRepository,
	glfiippersistence.ProvideMatchingRateHeatMapDataStore,
	glfiippersistence.ProvideMatchingRateHeatMapRepository,
	glfiippersistence.ProvideMongoMinimalOrderRepository,
	glfiippersistence.ProvideOnTopFareDataStore,
	glfiippersistence.ProvideOnTopFareRepository,
	glfiippersistence.ProvideOrderHearthBeatRepository,
	glfiippersistence.ProvideMongoOrderRepository,
	glfiippersistence.ProvideOrderRevisionDataStore,
	glfiippersistence.ProvideOrderDataStore,
	glfiippersistence.ProvideOTPSessionRepo,
	glfiippersistence.ProvidePdpaRepository,
	glfiippersistence.ProvidePdpaDataStore,
	glfiippersistence.ProvideDataStorePendingTransactionRepository,
	glfiippersistence.ProvidePendingTransactionDataStore,
	glfiippersistence.ProvideProductGroupRepository,
	glfiippersistence.ProvideProductGroupDataStore,
	glfiippersistence.ProvideProductRepository,
	glfiippersistence.ProvideProductDataStore,
	glfiippersistence.ProvideProvinceRepository,
	glfiippersistence.ProvideProvinceDataStore,
	glfiippersistence.ProvideQuestionConfigs,
	glfiippersistence.ProvideFileQuestionRepository,
	glfiippersistence.ProvideMongoQuoteRepository,
	glfiippersistence.ProvideQuoteDataStore,
	glfiippersistence.ProvideRainSituationDataStore,
	glfiippersistence.ProvideRainSituationRepository,
	glfiippersistence.ProvideRatingOptionRepository,
	glfiippersistence.ProvideRatingOptionDataStore,
	glfiippersistence.ProvideRatingRestaurantRepository,
	glfiippersistence.ProvideRatingRestaurantDataStore,
	glfiippersistence.ProvideRegionRepository,
	glfiippersistence.ProvideRequestUpdateProfileRepository,
	glfiippersistence.ProvideRequestUpdateProfileDataStore,
	glfiippersistence.ProvideRequestUpdateProfileSectionRepository,
	glfiippersistence.ProvideRequestUpdateProfileSectionDataStore,
	glfiippersistence.ProvideRewardBalanceRepository,
	glfiippersistence.ProvideRewardBalanceDataStore,
	glfiippersistence.ProvideMongoRewardTransactionRepository,
	glfiippersistence.ProvideMongoRewardTransactionDataStore,
	glfiippersistence.ProvideMongoServiceAreaRepository,
	glfiippersistence.ProvideServiceAreaDataStore,
	glfiippersistence.ProvideOptInReminderRepository,
	glfiippersistence.ProvideSettingDeliveryFeePriceSchemesDataStore,
	glfiippersistence.ProvideSettingDeliveryFeePriceSchemesRepository,
	glfiippersistence.ProvideShiftCancelRepository,
	glfiippersistence.ProvideShiftCancelDataStore,
	glfiippersistence.ProvideShiftRepository,
	glfiippersistence.ProvideShiftDataStore,
	glfiippersistence.ProvideSummaryOfChangeRepository,
	glfiippersistence.ProvideSummaryOfChangeDataStore,
	glfiippersistence.ProvideMongoTermAndConditionRepository,
	glfiippersistence.ProvideTermAndConditionDataStore,
	glfiippersistence.ProvideThrottledDispatchDetailDataStore,
	glfiippersistence.ProvideThrottledDispatchDetailRepository,
	glfiippersistence.ProvideThrottledOrderDataStore,
	glfiippersistence.ProvideThrottledOrderRepository,
	glfiippersistence.ProvideTopupCreditReportDataStore,
	glfiippersistence.ProvideTopupCreditReportRepository,
	glfiippersistence.ProvideMongoTransactionFraudScoreRepository,
	glfiippersistence.ProvideTransactionFraudScoreDataStore,
	glfiippersistence.ProvideDataStoreTransactionRepository,
	glfiippersistence.ProvideTransactionDataStore,
	glfiippersistence.ProvideMongoTransactionSchemeRepository,
	glfiippersistence.ProvideTransactionSchemeDataStore,
	glfiippersistence.ProvideTripDataStore,
	glfiippersistence.ProvideTripRepository,
	glfiippersistence.ProvideUnAcknowledgeReassignRepository,
	glfiippersistence.ProvideMongoUobRefRepository,
	glfiippersistence.ProvideUobRefDataStore,
	glfiippersistence.ProvideWhitelistPhoneRepository,
	glfiippersistence.ProvideWhitelistPhoneDataStore,
	glfiippersistence.ProvideWithdrawalTransactionResultsRepository,
	glfiippersistence.ProvideWithdrawalTransactionResultsDataStore,
	glfiippersistence.ProvideWithholdingTaxCertificateRepository,
	glfiippersistence.ProvideWithholdingTaxCertificateDataStore,
	glfiippersistence.ProvideZoneDataStore,
	glfiippersistence.ProvideZoneRepository,
	glfiiinfrastructure.ProvideLazyRepEventBus,
	glfiiinfrastructure.ProvideRepEventBus,
	glfilccleanup.ProvideCleanupPriority,
	glfilllocalcache.ProvideLocalCache,
	glfimmessages.ProvideLineBotNotificationServiceWorker,
	glfimmessages.ProvideStubMessageService,
	glfinffirebase.ProvideFirebasePushNotificationService,
	glfinffirebase.ProvideMessaging,
	glfinffirebase.ProvideApp,
	glfinffirebase.ProvideFirebaseCustomClient,
	glfinssocketio.ProvideSocketIOPushNotificationService,
	glfinssocketio.ProvideSocketIOClient,
	glfinsstub.ProvideConsoleFBMessagingClient,
	glfippreload.ProvidePreloadExecutor,
	glfissafe.ProvideWorkerContext,
	glfisserviceprovider.ProvideRegistry,
	glfisserviceprovider.ProvideServiceSelectorAPI,
	glfitmmetric.ProvideMetricsRegistry,
	glfitmmetric.ProvidePrometheusMeter,
	glfitttestdata.ProvideDriverRegistrationTestData,
	glfitttestdata.ProvideOTPSessionTestData,
	glfitttestdata.ProvideProvincesTestData,
	glfitttestdata.ProvideRatingRestaurantTestData,
	glfitttestdata.ProvideWhitelistPhonesTestData,
	glfittmpl.ProvideTemplateService,
)

// nolint
var ServiceSet = wire.NewSet(
	glfidsfform.ProvideService,
	glfidsffraud.ProvideFraudService,
	glfidsoorder.ProvideOrderService,
	glfidsppendingtransaction.ProvidePendingTransactionService,
	glfidsttransaction.ProvideTransactionServiceV2,
)

// nolint
var TestConfigSet = wire.NewSet(
	glficbbcp.ProvideBCPTestConfig,
	glfiippersistence.ProvideDriverLocationTestConfig,
	glfitpproviders.ProvideRedisTestConfig,
	glfitpproviders.ProvideLocationRedisTestConfig,
	glfitpproviders.ProvideDriverStatRedisTestConfig,
)

// nolint
type Locator struct {
	AssignmentBenchmarkAPI                   *glfiaaassignmentbenchmarks.AssignmentBenchmarkAPI
	DbConfigAdminAPI                         *glfiaddbconfig.AdminAPI
	DedicatedZoneAPI                         *glfiadzdedicated_zone.DedicatedZoneAPI
	DeliveryFeeAPI                           *glfiaddeliveryfee.DeliveryFeeAPI
	Document                                 *glfiaddriver.Document
	DriverAdminAPI                           *glfiaddriver.DriverAdminAPI
	DriverNotifyAPI                          *glfiaddriver.DriverNotifyAPI
	DriverSalesforceAPI                      *glfiaddriver.DriverSalesforceAPI
	DriverStatusAPI                          *glfiaddriver.DriverStatusAPI
	PdpaApi                                  *glfiaddriver.PdpaApi
	RegistrationAdminAPI                     *glfiaddriver.RegistrationAdminAPI
	RegistrationAPI                          *glfiaddriver.RegistrationAPI
	InsuranceAPI                             *glfiaddriverinsurance.InsuranceAPI
	AutoBanCompleteOrderTooOftenCfg          *glfiaddriverorderinfo.AtomicAutoBanCompleteOrderTooOftenCfg
	AutoBanCompleteOrderTooFarCfg            *glfiaddriverorderinfo.AtomicAutoBanCompleteOrderTooFarCfg
	DriverOrderInfoAPI                       *glfiaddriverorderinfo.DriverOrderInfoAPI
	EventAPI                                 *glfiaeevent.EventAPI
	FeatureFlagConfigAPI                     *glfiaffeatureflagconfig.FeatureFlagConfigAPI
	FraudAPI                                 *glfiaffraud.FraudAPI
	HeatMapAPI                               *glfiahheatmap.API
	IncentiveAPI                             *glfiaiincentive.IncentiveAPI
	IncentiveProgressDataStore               glfiaiincentive.IncentiveProgressDataStore
	DataStoreIncentiveProgressRepository     *glfiaiincentive.ProxyIncentiveProgressRepository
	IncentiveDataStore                       glfiaiincentive.IncentiveDataStore
	DataStoreIncentiveRepository             *glfiaiincentive.ProxyIncentiveRepository
	IncentiveSourceAPI                       *glfiaiincentivesource.IncentiveSourceAPI
	InternalAPI                              *glfiaiinternalapi.InternalAPI
	MiddlewareBuilder                        *glfiammiddlewares.Builder
	AcceptorDeps                             *glfiaoorder.AcceptorDeps
	Acceptor                                 *glfiaoorder.Acceptor
	AdminAPI                                 *glfiaoorder.AdminAPI
	DeliveryAPIPortal                        *glfiaoorder.DeliveryPortalAPI
	Canceller                                *glfiaoorder.CancellerImpl
	AssigningStateManager                    glfiaoddistribution.AssigningStateManager
	AutoAssignOrderDistributor               *glfiaoddistribution.AutoAssignOrderDistributor
	AutoAssignOrderDistributorDeps           glfiaoddistribution.AutoAssignOrderDistributorDeps
	ConfigValidator                          *glfiaoddistribution.ConfigValidator
	MemLocker                                *glfiaoorder.MemLocker
	OrderAPI                                 *glfiaoorder.OrderAPI
	OrderPortalAPI                           *glfiaoorder.OrderPortalAPI
	OrderAssignerHandler                     *glfiaooorderassigner.OrderAssignerHandler
	FoodProvider                             *glfiaoorder.FoodProviderImpl
	ProviderDeps                             glfiaoorder.ProviderDeps
	OTPAPI                                   *glfiaootp.OTPAPI
	CitiAuthenticationService                *glfiapaauth.CitiAuthenticationService
	CitiAPI                                  *glfiappartners.CitiAPI
	UobAPI                                   *glfiappartners.UobAPI
	ApprovalAPI                              *glfiappayment.ApprovalAPI
	ApprovalCreator                          glfiappayment.ApprovalCreator
	ApprovalService                          glfiappayment.ApprovalService
	DriverTransactionAPI                     *glfiappayment.DriverTransactionAPI
	DriverTransactionService                 glfiappayment.DriverTransactionService
	GroupTransactionAPI                      *glfiappayment.GroupTransactionAPI
	PaymentService                           *glfiappayment.PaymentServiceImpl
	UOBPayoutMetric                          *glfiappayment.UOBPayoutMetric
	TransactionAPI                           *glfiappayment.TransactionAPI
	TransactionSchemeAPI                     *glfiappayment.TransactionSchemeAPI
	TransactionService                       glfiappayment.TransactionService
	PhoneAPI                                 *glfiapphones.PhonesAPI
	ProductAPI                               *glfiapproduct.ProductAPI
	ProvinceAdminAPI                         *glfiapprovince.ProvinceAdminAPI
	ProvinceAPI                              *glfiapprovince.ProvinceAPI
	RainSituationAdminAPI                    *glfiarsrain_situation.RainSituationAdminAPI
	RainSituationAPI                         *glfiarsrain_situation.RainSituationAPI
	RatingAdminAPI                           *glfiarrating.RatingAdminAPI
	RatingOptionAPI                          *glfiarrating.RatingAPI
	RegionAdminAPI                           *glfiarregion.RegionAdminAPI
	RegionAPI                                *glfiarregion.RegionAPI
	RewardAdminAPI                           *glfiarreward.RewardAdminAPI
	ShiftAPI                                 *glfiasshift.ShiftAPI
	ClientAreaAPI                            *glfiassrvarea.ClientAreaAPI
	ServiceAreaSettingAPI                    *glfiassrvarea.ServiceAreaSettingAPI
	SummaryOfChangeAPI                       *glfiassummaryofchange.SummaryOfChangeAPI
	ThrottledOrderAPI                        *glfiatothrottled_order.ThrottledOrderAPI
	ThrottledDispatchDetailAdminAPI          *glfiatthrottleddispatchdetail.ThrottledDispatchDetailAdminAPI
	TripAPI                                  *glfiattrip.TripAPI
	WithholdingTaxCertificateAPI             *glfiawwithholdingtaxcertificate.WithholdingTaxCertificateAPI
	ZoneAdminAPI                             *glfiazzone.ZoneAdminAPI
	AsynqClient                              *ghaasynq.Client
	RedisTokenStore                          *glfiaauth.RedisTokenStorage
	RekognitionClient                        *glfiaaws.RekognitionImpl
	DBConfigDataStore                        glficconfig.DBConfigDataStore
	DBConfigRepository                       *glficconfig.ProxyDBConfigRepository
	AtomicConfigLocator                      glficconfiglocator.AtomicConfigLocator
	BCPClient                                glficbbcp.BCPClient
	BCPTestClientStub                        *glficbbcp.BCPTestClientStub
	Cache                                    glficccache.Cache
	Delivery                                 glficddelivery.Delivery
	StubDeliveryFleetApi                     *glficddelivery.StubDeliveryFleetApi
	DriverServiceDispatcher                  glficddispatcher.Dispatcher
	DistributionExperimentPlatformClient     glficeexperimentplatform.DistributionExperimentPlatformClient
	FleetAreaClient                          glficffleetarea.FleetAreaClient
	FleetOrderClient                         glficffleetorder.FleetOrderClient
	FraudAdvisorServiceImpl                  *glficffraudadvisor.FraudAdvisorServiceImpl
	HeatMapDemand                            glfichheatmapdemand.HeatMapDemand
	INetClient                               glficicinet_client.INetClient
	VerdaKafkaCfg                            glfickkafcclient.VerdaKafkaCfg
	DistributionKafkaConsumer                glfickkkafcdistribution.DistributionKafkaConsumer
	SecureIMFKafkaSyncProducerClientForTest  *glfickkkafcdistribution.SecureIMFKafkaSyncProducerStubClient
	IMFKafkaProducerClientForTest            *glfickkafcclient.KafkaProducerStubClient
	SecureIMFKafkaProducerClientForTest      *glfickkafcclient.SecureKafkaProducerStubClient
	LINEClient                               glficllineinternal.LINEClient
	LINEInternalClient                       glficllineinternal.Client
	RedisLocker                              *glficllocker.RedisLocker
	ExperimentalMapServiceClient             glficmmapservice.ExperimentalMapService
	StubMapService                           *glficmmmanmap.StubMapService
	FeaturePlatformService                   glficmmatchrate.Service
	MongoProfiler                            glficmmongoprofiler.MongoProfiler
	MongoTxnHelper                           gladvttransaction.TxnHelper
	KafkaConsumer                            *glficmmq.KafkaConsumer
	HttpPolygon                              *glficppolygon.HttpPolygon
	Prediction                               glficpprediction.Prediction
	RiderLevelConnector                      *glficrriderlevel.RiderLevelClient
	StubRiderLevelConnector                  *glficrriderlevel.StubRiderLevelConnector
	Slack                                    glficsslack.Slack
	SlackStub                                *glficsslack.SlackStub
	SMS                                      glficssms.SMS
	UobClient                                glficuuobclient.UOB
	UWTErrorService                          glficuuwterror.Service
	StubUWTErrorService                      *glficuuwterror.StubUWTErrorService
	RedisConn                                glfiddatastore.RedisConn
	Redis                                    glfiddatastore.RedisClient
	SecondaryDBConnection                    glfiddatastore.SecondaryDBConnection
	DBConnectionForTest                      *gladvv2.Conn
	DBMonitorConnectionForTest               *glficmmongoprofiler.MonitorConn
	GinEngineRouter                          *ggggin.Engine
	InitToggle                               InitToggle
	NewOTPSession                            glfidmmodel.NewOTPSession
	TimeNow                                  glfidmmodel.TimeNowFunc
	AccountingHubTransactionService          glfidsaaccountinghub.AccountingHubTransactionService
	AreaServiceImpl                          *glfidsservice.AreaServiceImpl
	AssignmentBenchmarkCfg                   glfidsservice.AssignmentBenchmarkCfg
	AssignmentBenchmarkService               glfidsservice.AssignmentBenchmarkService
	Attendances                              glfidsservice.Attendances
	AwsServiceImpl                           *glfidsservice.AwsServiceImpl
	BanServiceImpl                           *glfidsservice.BanServiceImpl
	BCPStatusTransitionerService             glfidsbbcp.BCPStatusTransitionerService
	DeliveryFeeService                       glfidsservice.DeliveryFeeService
	DistributionLogEventService              glfidsservice.DistributionLogEventService
	DistributionLogManager                   glfidsservice.DistributionLogManager
	DistributionService                      glfidsservice.DistributionService
	DriverInsuranceService                   *glfidsservice.DriverInsuranceServiceImpl
	DriverInsuranceCfg                       glfidsservice.DriverInsuranceCfg
	QRIncidentOrderService                   *glfidsservice.QRIncidentOrderServiceImpl
	DeviceManagerImpl                        *glfidsservice.DeviceManagerImpl
	DriverService                            *glfidsservice.DriverService
	DriverServiceTypeCapacityService         glfidsservice.DriverServiceTypeCapacityService
	DriverTransactionServiceV2               glfidsservice.DriverTransactionServiceV2
	DriverLocationEventServiceImpl           glfidsservice.DriverUpdateLocationEventService
	InstallmentOnTopTransactionProvider      *glfidsddrivertransaction.InstallmentOnTopTransactionProvider
	OnTopSchemeTransactionProviderSelector   glfidsddrivertransaction.OnTopSchemeTransactionProviderSelector
	Service                                  glfidsfform.Service
	FraudService                             glfidsffraud.Service
	IncentiveSourceServiceImpl               *glfidsservice.IncentiveSourceServiceImpl
	LocationManagerImpl                      *glfidsservice.LocationManagerImpl
	MissionLogEventService                   glfidsservice.MissionLogEventService
	PushNotifier                             *glfidsservice.PushNotifier
	OnTopFareService                         glfidsservice.OnTopFareService
	OrderService                             glfidsoorder.Service
	OrderDistributionEventManager            glfidsservice.OrderDistributionEventManager
	OrderDistributionEventService            glfidsservice.OrderDistributionEventService
	OrderHeartbeatService                    *glfidsservice.OrderHeartbeatServiceImpl
	PendingTransactionService                glfidsppendingtransaction.Service
	PredictionService                        glfidsservice.PredictionService
	RainSituationService                     *glfidsservice.RainSituationServiceImpl
	RewardService                            *glfidsservice.RewardService
	ServiceAreaServiceImpl                   glfidsservice.ServiceAreaService
	ServiceOptInReminderService              glfidsservice.ServiceOptInReminderService
	ServicePreferenceKillSwitchService       glfidsservice.ServicePreferenceKillSwitchService
	ServicePreferenceService                 glfidsservice.ServicePreferenceService
	ShiftServices                            glfidsservice.ShiftServices
	StatisticServiceImpl                     *glfidsservice.StatisticServiceImpl
	SupplyPositioningRecommenderService      glfidsservice.SupplyPositioningRecommenderService
	TermAndConditionServiceImpl              *glfidsservice.TermAndConditionServiceImpl
	ThrottledOrderService                    glfidsservice.ThrottledOrderService
	TiersAndBenefitsService                  glfidsservice.TiersAndBenefitsService
	TransactionServiceV2                     glfidsttransaction.Service
	TranslationServiceImpl                   *glfidsservice.TranslationServiceImpl
	TripServices                             *glfidsservice.TripService
	VOSServiceImpl                           *glfidsservice.VOSServiceImpl
	ZoneServiceImpl                          *glfidsservice.ZoneServiceImpl
	EmailService                             glfieemail.EmailService
	DriverStatRedisClient                    glfieeevent.DriverStatRedisClient
	DriverOrderInfoProcessor                 *glfieeevent.DriverOrderInfoProcessor
	DuplicatePreventionStore                 glfieeevent.DuplicatePreventionStoreInterface
	ExceedArDetector                         *glfieeevent.ExceedARDetector
	GokaStream                               *glfieeevent.GokaStream
	StubUnleashAdmin                         *glfiffeatureflag.DefaultUnleashAdmin
	StubLMWNUnleasher                        *glfiffeatureflag.DefaultLMWNUnleasher
	FeatureFlagService                       glfiffeatureflag.Service
	ChatServiceClient                        ggplcvv1.ChatServiceClient
	StubChatGRPCService                      *glfigcmcmock_chat.MockChatServiceClient
	CoinPlanGRPCClient                       glfigccoinplan.CoinPlanGRPCClient
	StubCoinPlanGRPCClient                   *glfigcmcmock_coinplan.MockCoinPlanGRPCClient
	GRPCFeatureV2ServiceClient               ggpdfvv2.FeatureV2ServiceClient
	StubGRPCFeatureV2ServiceClient           *glfigddapfeast.StubGRPCFeatureV2ServiceClient
	GRPCDriverProvisionClient                glfigddriverprovision.DriverProvisionClient
	StubGRPCRecommendationService            *glfigddriverprovision.StubGRPCRecommendationService
	GRPCEGSServiceClient                     ggplevv1.EGSServiceClient
	StubGRPCEGSService                       *glfigemgmock_grpc.MockEGSServiceClient
	GRPCEGSBranchServiceClient               ggplevv1.BranchServiceClient
	StubGRPCEGSBranchService                 *glfigeegsbranch.StubGRPCEGSBranchService
	GRPCEGSOrderServiceClient                ggplevv1.EGSOrderServiceClient
	StubGRPCEGSOrderService                  *glfigeegsorder.StubGRPCEGSOrderService
	GRPCFeaturePlatformClient                *glfigffeatureplatform.Client
	StubGPRCFeaturePlatformClient            *glfigffeatureplatform.StubGRPCFeaturePlatformClient
	FleetPoolGRPCClient                      *glfigffleetpool.GRPCClient
	RiderSearchServiceClient                 ggplfpvv1.RiderSearchServiceClient
	GRPCFormServiceClient                    ggplfsvv1.FormServiceClient
	StubGRPCFormService                      *glfigfmgmock_grpc.MockFormServiceClient
	GRPCPriorityGroupServiceClient           ggplivv1.PriorityGroupServiceClient
	StubGRPCPriorityGroupService             *glfigiinventoryservice.StubGRPCPriorityGroupService
	GRPCProductServiceClient                 ggplivv1.ProductServiceClient
	StubGRPCProductService                   *glfigiinventoryservice.StubGRPCProductService
	GRPCMarketplaceServiceClient             ggplevv1.MarketplaceServiceClient
	StubGRPCMarketplaceService               *glfigmmgmock_grpc.MockMarketplaceServiceClient
	GRPCUserPolygonServiceClient             ggplpvv2.UserPolygonServiceClient
	StubGRPCUserPolygonService               *glfigpmgmock_grpc.MockUserPolygonServiceClient
	PriceInterventionClient                  glfigppriceintervention.PriceInterventionClient
	PriceInterventionServiceClientStub       *glfigppriceintervention.PriceInterventionClientStub
	RainServiceClient                        glfigrrainservice.RainServiceClient
	RainServiceClientStub                    *glfigrmrmock_rainservice.MockRainServiceClient
	TranslationGRPCClient                    *glfigttranslationservice.GRPCClient
	TranslationServiceClient                 ggpltvv1.TranslationServiceClient
	GRPCUserServiceClient                    ggplavv1.UserServiceClient
	StubGRPCUserService                      *glfigumgmock_grpc.MockUserServiceClient
	FraudAdvisorHTTPClient                   *glfihhttpclient.FraudAdvisorHTTPClient
	DefaultClient                            *glfihhttpclient.Client
	DalianClient                             *glfihhttpclient.DalianClient
	IncomeAggregateService                   *glfiiaaggregate.IncomeAggregateServiceImpl
	IncomeSummaryService                     *glfiiincome.IncomeSummaryServiceImpl
	CoinConversionRateLocalCache             glfiiccache.CoinConversionRateLocalCache
	CoinConversionRateRedisCache             glfiiccache.CoinConversionRateRedisCache
	CoinConversionRateMinimalRedisCache      glfiiccache.CoinConversionRateMinimalRedisCache
	InfraLocalTaskExecutor                   *glfiieexecutor.LocalTaskExecutor
	StubKafkaConnector                       *glfiiinfrastructure.StubKafkaConnector
	DataStoreApprovalRepository              *glfidrrepository.ProxyApprovalRepository
	ApprovalDataStore                        glfiippersistence.ApprovalDataStore
	AssignmentBenchmarkDataStore             glfiippersistence.AssignmentBenchmarkDataStore
	AssignmentBenchmarkRepository            glfidrrepository.AssignmentBenchmarkRepository
	MongoAssignmentLogRepository             *glfidrrepository.ProxyAssignmentLogRepository
	DriverAssignmentLogsDataStore            glfiippersistence.DriverAssignmentLogsDataStore
	DriverAssignmentLogsRevisionsDataStore   glfiippersistence.DriverAssignmentLogsRevisionsDataStore
	DataStoreAssignmentRejectionRepository   *glfidrrepository.ProxyAssignmentRejectionRepository
	AssignmentRejectionDataStore             glfiippersistence.AssignmentRejectionDataStore
	AssignmentDataStore                      glfiippersistence.AssignmentDataStore
	AssignmentRepository                     glfidrrepository.AssignmentRepository
	DataStoreAttendanceLogHistoryRepository  *glfidrrepository.ProxyAttendanceLogHistoryRepository
	AttendanceLogHistoryDataStore            glfiippersistence.AttendanceLogHistoryDataStore
	AuditLogRepository                       *glfidrrepository.ProxyAuditLogRepository
	AuditLogDataStore                        gladvv2.DataStoreInterface
	BanHistoryServiceImpl                    *glfidrrepository.ProxyBanHistoryRepository
	BanHistoriesDataStore                    glfiippersistence.BanHistoriesDataStore
	BanMetadataDataStore                     glfiippersistence.MongoBanMetadataDataStore
	BanMetadataRepository                    *glfidrrepository.ProxyBanMetadataRepository
	BCPOrderDataStore                        glfiippersistence.BCPOrderDataStore
	BCPOrderRepository                       glfidrrepository.BCPOrderRepository
	DataStoreBulkProcessInfoRepository       *glfidrrepository.ProxyBulkProcessInfoRepository
	BulkProcessInfoDataStore                 glfiippersistence.BulkProcessInfoDataStore
	DataStoreCancelReasonRepository          *glfidrrepository.ProxyCancelReasonRepository
	CancelReasonDataStore                    glfiippersistence.CancelReasonDataStore
	MongoClientAreaRepository                *glfidrrepository.ProxyClientAreaRepository
	ClientAreaDataStore                      glfiippersistence.ClientAreaDataStore
	CoinCashConversionRateRepository         *glfidrrepository.ProxyCoinCashConversionRateRepository
	CoinCashConversionRateDataStore          glfiippersistence.MongoCoinCashConversionRateDataStore
	ConsolidatedInsuranceRepository          *glfidrrepository.ProxyConsolidatedInsuranceRepository
	ConsolidatedInsuranceDataStore           glfiippersistence.ConsolidatedInsuranceDataStore
	CookingTimeDelayRepository               glfidrrepository.CookingTimeDelayRepository
	CounterDataStore                         glfiippersistence.CounterDataStore
	CounterRepository                        *glfidrrepository.ProxyCounterRepository
	MongoDailyRewardRepository               *glfidrrepository.ProxyDailyRewardRepository
	MongoDailyRewardDataStore                glfiippersistence.MongoDailyRewardDataStore
	DedicatedZoneDataStore                   glfiippersistence.DedicatedZoneDataStore
	DedicatedZoneRepository                  glfidrrepository.DedicatedZoneRepository
	DeferredOrderDataStore                   glfiippersistence.DeferredOrderDataStore
	DeferredOrderRepository                  glfidrrepository.DeferredOrderRepository
	DeliveryFeeSettingDataStore              glfiippersistence.DeliveryFeeSettingDataStore
	DeliveryFeeSettingMongo                  *glfidrrepository.ProxyDeliveryFeeSettingRepository
	RedisDriverActiveTimeRepository          *glfidrrepository.ProxyDriverActiveTimeRepository
	DriverDocumentRepository                 *glfidrrepository.ProxyDriverDocumentRepository
	DriverDocumentDatastore                  glfiippersistence.DriverDocumentDatastore
	InsuranceRepository                      *glfidrrepository.ProxyDriverInsuranceRepository
	DriverInsuranceDataStore                 glfiippersistence.DriverInsuranceDataStore
	RedisLastUpdateLocationTrackerRepository *glfidrrepository.ProxyDriverLastUpdateLocationTrackerRepository
	LocationRedisClient                      glfiippersistence.LocationRedisClient
	DriverLocationRepositoryForTest          *glfidrrepository.ProxyDriverLocationRepository
	DriverOrderInfoRepository                *glfidrrepository.ProxyDriverOrderInfoRepository
	DriverOrderInfoDataStore                 glfiippersistence.DriverOrderInfoDataStore
	DriverRegistrationRepository             *glfidrrepository.ProxyDriverRegistrationRepository
	DriverRegistrationDataStore              glfiippersistence.DriverRegistrationDataStore
	DriverRepository                         *glfidrrepository.ProxyDriverRepository
	DriversDataStore                         glfiippersistence.DriversDataStore
	DataStoreDriverStatisticRepository       *glfidrrepository.ProxyDriverStatisticRepository
	DriverStatisticDataStore                 glfiippersistence.DriverStatisticDataStore
	DataStoreDriverTransactionRepository     *glfidrrepository.ProxyDriverTransactionRepository
	DriverTransactionDataStore               glfiippersistence.DriverTransactionDataStore
	MongoGroupTransactionRepository          *glfidrrepository.ProxyGroupTransactionRepository
	GroupTransactionDataStore                glfiippersistence.GroupTransactionDataStore
	HeatMapDataStore                         glfiippersistence.HeatMapDataStore
	HeatMapRepository                        *glfidrrepository.ProxyHeatMapRepository
	IllegalDriverRepository                  glfidrrepository.IllegalDriverRepository
	IncomeDailySummaryStore                  glfiippersistence.IncomeDailySummaryStore
	IncomeDailySummaryRepository             *glfidrrepository.ProxyIncomeDailySummaryRepository
	InternalCancelReasonDataStore            glfiippersistence.InternalCancelReasonDataStore
	InternalCancelReasonRepository           *glfidrrepository.ProxyInternalCancelReasonRepository
	LINEStatelessTokenCacheRepository        glfidrrepository.LINEStatelessTokenCacheRepository
	MatchingRateHeatMapDataStore             glfiippersistence.HeatMapMatchingRateDataStore
	MatchingRateHeatMapRepository            *glfidrrepository.ProxyMatchingRateHeatMapRepository
	MongoMinimalOrderRepository              *glfidrrepository.ProxyMinimalOrderRepository
	OnTopFareDataStore                       glfiippersistence.OnTopFareDataStore
	OnTopFareRepository                      *glfidrrepository.ProxyOnTopFareRepository
	OrderHearthBeatRepository                *glfidrrepository.ProxyOrderHeartbeatRepository
	MongoOrderRepository                     *glfidrrepository.ProxyOrderRepository
	OrderRevisionDataStore                   glfiippersistence.OrderRevisionDataStore
	OrderDataStore                           glfiippersistence.OrderDataStore
	OTPSessionRepo                           *glfidrrepository.ProxyOTPSessionRepo
	PdpaRepository                           *glfidrrepository.ProxyPdpaRepository
	PdpaDataStore                            glfiippersistence.PdpaDataStore
	DataStorePendingTransactionRepository    *glfidrrepository.ProxyPendingTransactionRepository
	PendingTransactionDataStore              glfiippersistence.PendingTransactionDataStore
	ProductGroupRepository                   *glfidrrepository.ProxyProductGroupRepository
	ProductGroupDataStore                    glfiippersistence.ProductGroupDataStore
	ProductRepository                        *glfidrrepository.ProxyProductRepository
	ProductDataStore                         glfiippersistence.ProductDataStore
	ProvinceRepository                       *glfidrrepository.ProxyProvinceRepository
	ProvinceDataStore                        glfiippersistence.ProvinceDataStore
	QuestionConfigs                          glfiippersistence.QuestionsConfig
	FileQuestionRepository                   *glfidrrepository.ProxyQuestionRepository
	MongoQuoteRepository                     *glfidrrepository.ProxyQuoteRepository
	QuoteDataStore                           glfiippersistence.QuoteDataStore
	RainSituationDataStore                   glfiippersistence.RainSituationDataStore
	RainSituationRepository                  *glfidrrepository.ProxyRainSituationRepository
	RatingOptionRepository                   *glfidrrepository.ProxyRatingOptionRepository
	RatingOptionDataStore                    glfiippersistence.RatingOptionDataStore
	RatingRestaurantRepository               *glfidrrepository.ProxyRatingRestaurantRepository
	RatingRestaurantDataStore                glfiippersistence.RatingRestaurantDataStore
	RegionRepository                         *glfidrrepository.ProxyRegionRepository
	RequestUpdateProfileRepository           *glfidrrepository.ProxyRequestUpdateProfileRepository
	RequestUpdateProfileDataStore            glfiippersistence.RequestUpdateProfileDataStore
	RequestUpdateProfileSectionRepository    *glfidrrepository.ProxyRequestUpdateProfileSectionRepository
	RequestUpdateProfileSectionDataStore     glfiippersistence.RequestUpdateProfileSectionDataStore
	RewardBalanceRepository                  *glfidrrepository.ProxyRewardBalanceRepository
	RewardBalanceDataStore                   glfiippersistence.MongoRewardBalanceDataStore
	MongoRewardTransactionRepository         *glfidrrepository.ProxyRewardTransactionRepository
	MongoRewardTransactionDataStore          glfiippersistence.MongoRewardTransactionDataStore
	MongoServiceAreaRepository               *glfidrrepository.ProxyServiceAreaRepository
	ServiceAreaDataStore                     glfiippersistence.ServiceAreaDataStore
	OptInReminderRepository                  glfidrrepository.ServiceOptInReminderRepository
	SettingDeliveryFeePriceSchemesDataStore  glfiippersistence.SettingDeliveryFeePriceSchemesDataStore
	SettingDeliveryFeePriceSchemesRepository *glfidrrepository.ProxySettingDeliveryFeePriceSchemesRepository
	ShiftCancelRepository                    *glfidrrepository.ProxyShiftCancelRepository
	ShiftCancelDataStore                     glfiippersistence.ShiftCancelDataStore
	ShiftRepository                          *glfidrrepository.ProxyShiftRepository
	ShiftDataStore                           glfiippersistence.ShiftDataStore
	SummaryOfChangeRepository                *glfidrrepository.ProxySummaryOfChangeRepository
	SummaryOfChangeDataStore                 glfiippersistence.SummaryOfChangeDataStore
	MongoTermAndConditionRepository          *glfidrrepository.ProxyTermAndConditionRepository
	TermAndConditionDataStore                glfiippersistence.TermAndConditionDataStore
	ThrottledDispatchDetailDataStore         glfiippersistence.ThrottledDispatchDetailDataStore
	ThrottledDispatchDetailRepository        glfidrrepository.ThrottledDispatchDetailRepository
	ThrottledOrderDataStore                  glfiippersistence.ThrottledOrderDataStore
	ThrottledOrderRepository                 glfidrrepository.ThrottledOrderRepository
	TopupCreditReportDataStore               glfiippersistence.TopupCreditReportDataStore
	TopupCreditReportRepository              *glfidrrepository.ProxyTopupCreditReportRepository
	MongoTransactionFraudScoreRepository     *glfidrrepository.ProxyTransactionFraudScoreRepository
	TransactionFraudScoreDataStore           glfiippersistence.TransactionFraudScoreDataStore
	DataStoreTransactionRepository           *glfidrrepository.ProxyTransactionRepository
	TransactionDataStore                     glfiippersistence.TransactionDataStore
	MongoTransactionSchemeRepository         *glfidrrepository.ProxyTransactionSchemeRepository
	TransactionSchemeDataStore               glfiippersistence.TransactionSchemeDataStore
	TripDataStore                            glfiippersistence.TripDataStore
	TripRepository                           *glfidrrepository.ProxyTripRepository
	UnAcknowledgeReassignRepository          glfidrrepository.UnAcknowledgeReassignReposity
	MongoUobRefRepository                    *glfidrrepository.ProxyUobRefRepository
	UobRefDataStore                          glfiippersistence.UobRefDataStore
	WhitelistPhoneRepository                 *glfidrrepository.ProxyWhitelistPhoneRepository
	WhitelistPhoneDataStore                  glfiippersistence.WhitelistPhoneDataStore
	WithdrawalTransactionResultsRepository   *glfidrrepository.ProxyWithdrawalTransactionResultsRepository
	WithdrawalTransactionResultsDataStore    glfiippersistence.WithdrawalTransactionResultsDataStoreDataStore
	WithholdingTaxCertificateRepository      *glfidrrepository.ProxyWithholdingTaxCertificateRepository
	WithholdingTaxCertificateDataStore       glfiippersistence.WithholdingTaxCertificateDataStore
	ZoneDataStore                            glfiippersistence.ZoneDataStore
	ZoneRepository                           *glfidrrepository.ProxyZoneRepository
	LazyRepEventBus                          *glfiiinfrastructure.LazyRepEventBus
	RepEventBus                              *glfiiinfrastructure.RepEventBus
	CleanupPriority                          glfilccleanup.CleanupPriority
	LocalCache                               glfilllocalcache.Caches
	LineBotNotificationServiceWorker         *glfimmessages.LineBotNotificationServiceWorker
	StubMessageService                       *glfimmessages.StubMessageService
	FirebasePushNotificationService          glfinnotification.FirebasePushNotificationService
	SocketIOPushNotificationService          glfinnotification.SocketIOPushNotificationService
	SocketIOClient                           glfinssocketio.Client
	ConsoleFBMessagingClient                 *glfinsstub.ConsoleLogMessagingClient
	PreloadExecutor                          *glfippreload.PreloadExecutor
	WorkerContext                            glfissafe.WorkerContext
	Registry                                 *glfisserviceprovider.ProviderRegistry
	ServiceSelectorAPI                       *glfisserviceprovider.ServiceSelector
	MetricsRegistry                          *glfitmmetric.MetricsRegistryImpl
	PrometheusMeter                          *glfitmmetric.PrometheusMeter
	ServiceGRPCConnFactory                   *ggggrpclib.ConnectionFactory
	StubPolygonApiForTest                    *glfitpproviders.StubPolygonApi
	TopkekForTest                            *glfitpproviders.TopkekForTest
	DriverRegistrationTestData               *glfitttestdata.DriverRegistrationTestData
	Fixtures                                 *glfitttestdata.Fixtures
	OTPSessionTestData                       *glfitttestdata.OTPSessionTestData
	ProvincesTestData                        *glfitttestdata.ProvincesTestData
	RatingRestaurantTestData                 *glfitttestdata.RatingRestaurantTestData
	WhitelistPhonesTestData                  *glfitttestdata.WhitelistPhonesTestData
	TemplateService                          glfittmpl.Template
}
