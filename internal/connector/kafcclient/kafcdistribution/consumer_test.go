package kafcclientdistribution

import (
	"context"
	"fmt"
	"testing"

	"github.com/IBM/sarama"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher/mock_dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution/mock_kafcclientdistribution"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
)

var defaultTestConsumerConfig = DistributionKafkaConsumerConfig{LimitedRetryAttempts: 3}

func TestHandleWithRetry(t *testing.T) {
	t.Parallel()

	t.Run("happy case no error", func(tt *testing.T) {
		consumer, _, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		handler := handlerStub{}
		messageHandler := consumer.handleWithRetry(handler.handle)
		err := messageHandler(context.Background(), &sarama.ConsumerMessage{})
		require.NoError(tt, err)
		require.Equal(tt, 1, handler.count)
	})

	t.Run("should retry until reach max attempts with retryable error", func(tt *testing.T) {
		consumer, _, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		handler := handlerStub{
			expectedError: []error{
				NewRetryableError(fmt.Errorf("retryable error"), "LMF-1"),
				NewRetryableError(fmt.Errorf("retryable error"), "LMF-1"),
				NewRetryableError(fmt.Errorf("retryable error"), "LMF-1"),
			},
		}

		messageHandler := consumer.handleWithRetry(handler.handle)
		err := messageHandler(context.Background(), &sarama.ConsumerMessage{})
		require.NoError(tt, err)
		require.Equal(tt, 3, handler.count)
	})

	t.Run("shouldn't retry when handler doesn't return retryable error", func(tt *testing.T) {
		consumer, _, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		handler := handlerStub{
			expectedError: []error{
				NewRetryableError(fmt.Errorf("retryable error"), "LMF-1"),
				errors.New("something went wrong"),
			},
		}

		messageHandler := consumer.handleWithRetry(handler.handle)
		err := messageHandler(context.Background(), &sarama.ConsumerMessage{})
		require.Error(tt, err)
		require.Equal(tt, 2, handler.count)
	})
}

func TestHandleDistributeOrderEvent(t *testing.T) {
	t.Parallel()

	orderID := "LMF-1"

	t.Run("happy case no error", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		deps.dispatcher.EXPECT().DistributeV2(gomock.Any(), dispatcher.DistributeOrderV2Request{OrderID: orderID}, gomock.Any()).Return(dispatcher.DistributeOrderV2Response{}, nil)

		event := newDistributeOrderEvent(tt, &driverv1.DistributeOrderEvent{OrderId: orderID})
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.NoError(tt, err)
	})

	t.Run("happy case no error (empty orderId)", func(tt *testing.T) {
		consumer, _, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		event := newDistributeOrderEvent(tt, &driverv1.DistributeOrderEvent{OrderId: ""})
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.NoError(tt, err)
	})

	t.Run("should do nothing and drop message when it can't deserialize message", func(tt *testing.T) {
		consumer, _, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		event := "should error"
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.NoError(tt, err)
	})

	t.Run("call distribute and receive unexpected error", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		deps.dispatcher.EXPECT().DistributeV2(gomock.Any(), dispatcher.DistributeOrderV2Request{OrderID: orderID}, gomock.Any()).Return(dispatcher.DistributeOrderV2Response{}, errors.New("something went wrong"))

		event := newDistributeOrderEvent(tt, &driverv1.DistributeOrderEvent{OrderId: orderID})
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.Error(tt, err)
		retryableErr, ok := AsRetryableError(err)
		require.NotNil(tt, retryableErr)
		require.Equal(tt, true, ok)
	})

	t.Run("call distribute and receive validation error shouldn't return error (drop message)", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		apiError := &dispatcher.DispatcherErrorResponse{
			StatusCode: 400,
			Url:        "/distribute-order",
			Data: dispatcher.ApiErrResponse{
				Code: apiutil.ErrValidateDistributeOrderFailed,
			},
		}

		deps.dispatcher.EXPECT().DistributeV2(gomock.Any(), dispatcher.DistributeOrderV2Request{OrderID: orderID}, gomock.Any()).Return(dispatcher.DistributeOrderV2Response{}, apiError)

		event := newDistributeOrderEvent(tt, &driverv1.DistributeOrderEvent{OrderId: orderID})
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.NoError(tt, err)
	})

	t.Run("call distribute and receive internal error should return retryable error correctly", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		apiError := &dispatcher.DispatcherErrorResponse{
			StatusCode: 500,
			Url:        "/distribute-order",
			Data: dispatcher.ApiErrResponse{
				Code: api.ERRCODE_INTERNAL_ERROR,
			},
		}

		deps.dispatcher.EXPECT().DistributeV2(gomock.Any(), dispatcher.DistributeOrderV2Request{OrderID: orderID}, gomock.Any()).Return(dispatcher.DistributeOrderV2Response{}, apiError)

		event := newDistributeOrderEvent(tt, &driverv1.DistributeOrderEvent{OrderId: orderID})
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.Error(tt, err)
		retryableErr, ok := AsRetryableError(err)
		require.NotNil(tt, retryableErr)
		require.Equal(tt, true, ok)
	})

	t.Run("call distribute and receive unhandled error should return retryable error", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		apiError := &dispatcher.DispatcherErrorResponse{
			StatusCode: 400,
			Url:        "/distribute-order",
			Data: dispatcher.ApiErrResponse{
				Code: "UNKNOWN",
			},
		}

		deps.dispatcher.EXPECT().DistributeV2(gomock.Any(), dispatcher.DistributeOrderV2Request{OrderID: orderID}, gomock.Any()).Return(dispatcher.DistributeOrderV2Response{}, apiError)

		event := newDistributeOrderEvent(tt, &driverv1.DistributeOrderEvent{OrderId: orderID})
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.Error(tt, err)
		retryableErr, ok := AsRetryableError(err)
		require.NotNil(tt, retryableErr)
		require.Equal(tt, true, ok)
	})

	t.Run("call distribute and receive unreachable error", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		deps.dispatcher.EXPECT().DistributeV2(gomock.Any(), dispatcher.DistributeOrderV2Request{OrderID: orderID}, gomock.Any()).Return(dispatcher.DistributeOrderV2Response{}, dispatcher.ErrConnectionRefuse)

		event := newDistributeOrderEvent(tt, &driverv1.DistributeOrderEvent{OrderId: orderID})
		err := consumer.handleDistributeOrderEvent(context.Background(), event)
		require.Error(tt, err)
		retryableErr, ok := AsRetryableError(err)
		require.NotNil(tt, retryableErr)
		require.Equal(tt, true, ok)
	})
}

func TestConsumerRun(t *testing.T) {
	t.Parallel()

	t.Run("happy case no error", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		deps.consumer.EXPECT().OnMessage(gomock.Any()).Return(nil)
		consumer.Run(context.Background())
	})

	t.Run("error on run should panic", func(tt *testing.T) {
		consumer, deps, finish := newTestDistributionKafkaConsumer(tt, defaultTestConsumerConfig)
		defer finish()

		deps.consumer.EXPECT().OnMessage(gomock.Any()).Return(errors.New("something went wrong"))

		isPanic := false
		func() {
			defer func() {
				if r := recover(); r != nil {
					isPanic = true
				}
			}()
			defer consumer.Run(context.Background())
		}()
		require.Equal(tt, true, isPanic)
	})
}

func newDistributeOrderEvent(t *testing.T, event *driverv1.DistributeOrderEvent) *sarama.ConsumerMessage {
	value, err := proto.Marshal(event)
	require.NoError(t, err)

	return &sarama.ConsumerMessage{
		Value: value,
	}
}

type handlerStub struct {
	count         int
	expectedError []error
}

func (h *handlerStub) handle(ctx context.Context, msg interface{}) error {
	h.count++

	if len(h.expectedError) >= 1 {
		err := h.expectedError[0]
		h.expectedError = h.expectedError[1:]
		return err
	}

	return nil
}

type distributionKafkaConsumerDeps struct {
	consumer   *mock_kafcclientdistribution.MockDistributionKafkaConsumerGroup
	dispatcher *mock_dispatcher.MockDispatcher
}

func newTestDistributionKafkaConsumer(t *testing.T, cfg DistributionKafkaConsumerConfig) (distributionKafkaConsumer, *distributionKafkaConsumerDeps, func()) {
	ctrl := gomock.NewController(t)

	consumer := mock_kafcclientdistribution.NewMockDistributionKafkaConsumerGroup(ctrl)
	dispatcher := mock_dispatcher.NewMockDispatcher(ctrl)

	deps := &distributionKafkaConsumerDeps{
		consumer:   consumer,
		dispatcher: dispatcher,
	}

	s := distributionKafkaConsumer{
		consumer:   consumer,
		dispatcher: dispatcher,
		cfg:        cfg,
	}

	return s, deps, func() { ctrl.Finish() }
}
