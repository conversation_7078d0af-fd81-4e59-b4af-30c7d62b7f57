package kafcclientdistribution

//go:generate mockgen -source=./consumer.go -destination=./mock_kafcclientdistribution/mock_consumer.go -package=mock_kafcclientdistribution

import (
	"context"
	"errors"
	"time"

	"github.com/IBM/sarama"
	"github.com/avast/retry-go"
	"github.com/kelseyhightower/envconfig"
	"google.golang.org/protobuf/proto"

	"git.wndv.co/go/kafc"
	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

type DistributionKafkaConsumer interface {
	Run(ctx context.Context)
}

type DistributionKafkaConsumerGroup interface {
	OnMessage(handler kafc.MessageHandler) error
	Close() error
	IsRunning() bool
}

type distributionKafkaConsumer struct {
	consumer   DistributionKafkaConsumerGroup
	dispatcher dispatcher.Dispatcher
	cfg        DistributionKafkaConsumerConfig
}

type RetryableError struct {
	err                  error
	orderID              string
	ridersTriedAssigning *int32
}

func (e RetryableError) Error() string {
	return e.err.Error()
}

func (e RetryableError) OrderID() string {
	return e.orderID
}

func NewRetryableError(err error, orderID string) *RetryableError {
	return &RetryableError{err: err, orderID: orderID}
}

func AsRetryableError(err error) (*RetryableError, bool) {
	var retryableError *RetryableError
	return retryableError, errors.As(err, &retryableError)
}

func ProvideDistributionKafkaConsumer(cfg DistributionKafkaConsumerConfig, kafcCfg DistributionKafcConsumerConfig, dispatcher dispatcher.Dispatcher) (DistributionKafkaConsumer, func()) {
	consumer, err := kafc.NewConsumerGroup(kafcCfg.KafkaConfig(), cfg.ConsumerGroup, cfg.Topic, false)
	if err != nil {
		logx.Error().Err(err).Msg("unable to init distribution consumer")
		return nil, func() {}
	}

	cleanUp := func() {
		if consumer != nil {
			err := consumer.Close()
			if err != nil {
				logx.Error().Err(err).Msg("error closing consumer")
			}
		}
	}

	s := &distributionKafkaConsumer{
		consumer:   consumer,
		dispatcher: dispatcher,
		cfg:        cfg,
	}

	return s, cleanUp
}

func (k *distributionKafkaConsumer) Run(_ context.Context) {
	logx.Info().Msg("distributionKafkaConsumer: run")
	if err := k.consumer.OnMessage(k.handleWithRetry(k.handleDistributeOrderEvent)); err != nil {
		logx.Error().Err(err).Msg("kafka consumer group error occurred")
		safe.SentryError(err)
		panic(err) // consumer group already stop message consume, raise panic to exit application
	}
}

func (k *distributionKafkaConsumer) handleWithRetry(fn kafc.MessageHandler) kafc.MessageHandler {
	return func(ctx context.Context, msg interface{}) error {
		err := retry.Do(
			func() error {
				return fn(ctx, msg)
			},
			retry.Attempts(k.cfg.LimitedRetryAttempts), // including the first round
			retry.Delay(k.cfg.LimitedRetryDelay),
			retry.DelayType(retry.BackOffDelay),
			retry.RetryIf(func(err error) bool {
				_, ok := AsRetryableError(err)
				return ok
			}),
			retry.OnRetry(func(n uint, err error) {
				logx.Warn().Msgf("distributionKafkaConsumer retrying round %d due to err %s", n, err.Error())
			}),
			retry.LastErrorOnly(true),
		)
		if err != nil {
			if retryableErr, ok := AsRetryableError(err); ok {
				logx.Error().Err(err).Msgf("distributionKafkaConsumer error occurred exceed retry limit, dropping message for orderID: %v", retryableErr.OrderID())
				safe.SentryError(err, safe.WithOrderID(retryableErr.OrderID()))
				return nil // drop message when reach the attempts
			} else {
				logx.Error().Err(err).Msgf("distributionKafkaConsumer error occurred")
				safe.SentryError(err)
				return err
			}
		}
		return nil
	}
}

// handleDistributeOrderEvent
// return err = nil for drop message in case that we can't process the message anymore
// return err = RetryableError for using limited retry logic
// otherwise for any err will uncommit message and let consumer try to reconsume it until success (infinite retry)
func (k *distributionKafkaConsumer) handleDistributeOrderEvent(ctx context.Context, msg interface{}) error {
	saramaMsg, ok := msg.(*sarama.ConsumerMessage)
	if !ok {
		logx.Error().Msg("cannot convert to sarama consumer message")
		return nil // drop message
	}

	var event driverv1.DistributeOrderEvent
	if err := proto.Unmarshal(saramaMsg.Value, &event); err != nil {
		logx.Error().Msgf("distributionKafkaConsumer: cannot parse message %v", event.String())
		return nil // drop message
	}
	logx.Info().Msgf("distributionKafkaConsumer: OnMessage Event: %v", event.String())

	if event.OrderId == "" {
		return nil
	}

	var ridersTriedAssigning *int = nil
	if event.RidersTriedAssigning != nil {
		v := int(*event.RidersTriedAssigning)
		ridersTriedAssigning = &v
	}

	if _, err := k.dispatcher.DistributeV2(
		ctx,
		dispatcher.DistributeOrderV2Request{OrderID: event.OrderId, RidersTriedAssigning: ridersTriedAssigning},
		dispatcher.WithNoRetry(),
	); err != nil {
		logx.Error().Err(err).Msgf("distributionKafkaConsumer: OnMessage Event error: %+v", event.String())
		var errResp *dispatcher.DispatcherErrorResponse
		if errors.As(err, &errResp) && errResp.Data.Code == apiutil.ErrValidateDistributeOrderFailed {
			logx.Error().Msgf("dropping message for order %s, err: %s", event.OrderId, errResp.Error())
			return nil // drop message
		}

		return NewRetryableError(err, event.OrderId)
	}
	return nil
}

type DistributionKafkaConsumerConfig struct {
	ConsumerGroup        string        `envconfig:"FLEET_DISTRIBUTION_KAFKA_CONSUMER_GROUP_NAME"`
	Topic                string        `envconfig:"FLEET_DISTRIBUTION_KAFKA_CONSUMER_DISTRIBUTE_ORDER_KAFKA_TOPIC"`
	LimitedRetryAttempts uint          `envconfig:"FLEET_DISTRIBUTION_KAFKA_CONSUMER_LIMITED_RETRY_ATTEMPTS" default:"4"`
	LimitedRetryDelay    time.Duration `envconfig:"FLEET_DISTRIBUTION_KAFKA_CONSUMER_LIMITED_RETRY_DELAY" default:"1s"`
}

func ProvideDistributionKafkaConsumerConfig() DistributionKafkaConsumerConfig {
	var cfg DistributionKafkaConsumerConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

type DistributionKafcConsumerConfig kafc.KafkaConfig

func (cfg DistributionKafcConsumerConfig) KafkaConfig() kafc.KafkaConfig {
	return kafc.KafkaConfig(cfg)
}

func ProvideDistributionKafcConsumerConfig() DistributionKafcConsumerConfig {
	var cfg kafc.KafkaConfig
	envconfig.MustProcess("FLEET_DISTRIBUTION_CONSUMER", &cfg)
	return DistributionKafcConsumerConfig(cfg)
}
